<template>
  <div v-loading="loading">
    <el-tabs v-model="activeName">
      <el-tab-pane label="投档单" name="first">
        <avue-form v-model="form" :option="option">
          <template v-slot:file>
            <el-upload
                class="upload-demo"
                ref="upload"
                accept=".zip,.xlsx"
                action="/ksgl/dataProcessing/importData"
                :data="form"
                :on-change="handlechange"
                :on-remove="handleRemove"
                :on-success="handlesuccess"
                :on-error="handleerror"
                :file-list="fileList"
                :limit="1"
                :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <div slot="tip" class="el-upload__tip">只能上传zip/xlsx文件</div>
            </el-upload>
          </template>
          <template v-slot:cc>
            <el-radio v-model="radio" @input="changecc" label="tdd">以投档单为主</el-radio>
            <el-radio v-model="radio" @input="changecc" label="zdy">自定义设置</el-radio>
            <el-select v-if="dzyshow" v-model="value" @change="changezdycc" style="width: 30%!important;"
                       placeholder="请选择">
              <el-option
                  v-for="item in options"
                  :key="item.code"
                  :label="item.name"
                  :value="item.name">
              </el-option>
            </el-select>
          </template>
        </avue-form>
      </el-tab-pane>
      <el-tab-pane label="自定义" name="second">
        <avue-form v-model="zdyform" :option="zdyoption">
          <template v-slot:file>
            <el-upload
                class="upload-demo"
                ref="zdyupload"
                accept=".xlsx"
                action="/ksgl/tdd/importZdylqsj"
                :data="zdyform"
                :on-change="handlechange"
                :on-remove="handleRemove"
                :on-success="handlesuccess"
                :on-error="handleerror"
                :file-list="fileList"
                :limit="1"
                :auto-upload="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <div slot="tip" class="el-upload__tip">使用场景： 其他录取数据（自主招生、高校专项计划、保送生）
                请选择单个excel文件，文件大小在50M以内。
              </div>
              <div style="margin-top: 10px;">
                <el-button type="primary" @click="handleTemplate()">
                  点击下载模板<i class="el-icon-download el-icon--right"></i>
                </el-button>
              </div>
            </el-upload>
          </template>
        </avue-form>
      </el-tab-pane>
      <!--            <el-tab-pane label="人像核验" name="third">
                    <avue-form v-model="rlhyform" :option="rlhyoption">
                      <template v-slot:file>
                        <el-upload
                            class="upload-demo"
                            ref="rlhyupload"
                            accept=".xlsx"
                            action="/ksgl/dataProcessing/importData"
                            :data="rlhyform"
                            :on-change="handlechange"
                            :on-remove="handleRemove"
                            :on-success="handlesuccess"
                            :on-error="handleerror"
                            :file-list="fileList"
                            :limit="1"
                            :auto-upload="false">
                          <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                          <div slot="tip" class="el-upload__tip">使用场景： 人像核验的数据导入。
                            请选择单个excel文件，文件大小在50M以内。</div>
                        </el-upload>
                      </template>
                    </avue-form>
                  </el-tab-pane>-->
    </el-tabs>
    <div class="dialog-footer">
      <el-button type="primary" @click="submitform">导 入</el-button>
    </div>
  </div>
</template>

<script>

import {getAll} from "@/api/code/codeccb";

export default {
  name: "daoruluqv",
  data() {
    return {
      fileList: [],
      loading: false,
      nflist: [],
      activeName: 'first',
      radio: 'tdd',
      value: null,
      dzyshow: false,
      options: [],
      form: {},
      rlhyform: {},
      zdyform: {},
    };
  },
  computed: {
    headers() {
      return {
        // Authorization: getToken() // 直接从本地获取token就行
      };
    },
    option() {
      return {
        submitBtn: false,
        emptyBtn: false,
        size: 'mini',
        column: [
          {
            label: '自动修正',
            prop: 'zdxz',
            type: 'switch',
            span: 24,
            dicData: [{
              label: '否',
              value: '否'
            }, {
              label: '是',
              value: '是'
            }]
          },
          {
            label: '年份',
            prop: 'nf',
            type: 'select',
            span: 24,
            dicData: this.nflist
          },
          {
            label: '层次',
            prop: 'cc',
            // type: 'radio',
            span: 24,
            slot: true,
            // props: {
            //   label: 'name',
            //   value: 'code'
            // },
            // dicUrl: '/code/ccdm/all'
          },
          {
            label: '招生类型',
            prop: 'zslx',
            type: 'select',
            span: 24,
            props: {
              label: 'zslx',
              value: 'zslx'
            },
            dicUrl: '/code/codezslx/all'
          },
          {
            label: '',
            prop: 'file',
            type: 'upload',
            span: 24,
            slot: true
          },
        ]
      }
    },
    zdyoption() {
      return {
        submitBtn: false,
        emptyBtn: false,
        size: 'mini',
        column: [
          {
            label: '年份',
            prop: 'nf',
            type: 'select',
            span: 24,
            dicData: this.nflist
          },
          {
            label: '',
            prop: 'file',
            type: 'upload',
            span: 24,
            slot: true
          },
        ]
      }
    },
    rlhyoption() {
      return {
        submitBtn: false,
        emptyBtn: false,
        size: 'mini',
        column: [
          {
            label: '年份',
            prop: 'nf',
            type: 'select',
            span: 24,
            dicData: this.nflist
          },
          {
            label: '',
            prop: 'file',
            type: 'upload',
            span: 24,
            slot: true
          },
        ]
      }
    }
  },
  created() {
    const now = new Date();
    const currentYear = now.getFullYear();
    const specifiedNumber = currentYear;
    this.nflist = this.generateNumberArray(specifiedNumber);
    getAll().then(res => {
      console.log(res.data)
      this.options = res.data.data;
    })
  },
  methods: {
    changecc(value) {
      console.log(value)
      if (value == 'tdd') {
        this.form.cc = value;
        this.dzyshow = false;
      } else {
        this.dzyshow = true;
      }
    },
    changezdycc(value) {
      console.log(value)
      this.form.cc = value;
    },
    generateNumberArray(num) {
      const result = [];
      for (let i = num; i > num - 10; i--) {
        result.push({
          label: i,
          value: i,
        });
      }

      return result;
    },
    handlechange(file, fileList) {
      console.log(`handlechange`, file, fileList);
      this.fileList = fileList;
    },
    handleRemove(file, fileList) {
      console.log(file, fileList);
      this.fileList = [];
    },
    handlesuccess(response) {
      console.log(`handlesuccess`, response)
      if (response.code == 200 || response.code == '00000') {
        this.loading = false;
        this.$message({
          message: '导入成功',
          type: 'success'
        });
        this.$emit('onclose', true)
      } else {
        this.loading = false;
        this.$message.error('导入失败');
        this.$emit('onclose', true)
      }
    },
    handleerror(err) {
      console.log(`handleerror`, err)
      this.loading = false;
      this.$message.error('导入失败');
      this.$emit('onclose', true)
    },
    submitform() {
      this.loading = true;
      if (this.activeName == 'first') {
        this.$refs.upload.submit();
      } else if (this.activeName == 'second') {
        this.$refs.zdyupload.submit();
      } else if (this.activeName == 'third') {
        this.$refs.rlhyupload.submit();
      }
    },
    handleTemplate() {
      window.open(`/ksgl/tdd/downImportTemplate`);
    },
  }
}
</script>

<style scoped>

</style>
