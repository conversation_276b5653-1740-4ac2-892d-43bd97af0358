<template>
    <div class="wrap">
        <div class="title">
            <div style="height: 30px;line-height: 30px;">
                <span style="font-size: 14px;float: left;">
                    年份:
                </span>
                <el-select v-model="nfvalue" size="mini" @change="nfchange" style="float: left;margin-left: 5px;" placeholder="请选择">
                    <el-option
                        v-for="item in nfoptions"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value">
                    </el-option>
                </el-select>
                <el-button type="primary" plain style="float: right;margin-left: 10px;" @click="opendialog('shujvqingkong','数据清空')">数据清空</el-button>
<!--                <el-button type="primary" plain style="float: right;" @click="opendialog('shujvzhuaqv','数据抓取')">数据抓取</el-button>-->
                <el-button type="primary" plain style="float: right;" @click="opendialog('daorusanbiao','导入三表')">导入三表</el-button>
                <el-button type="primary" plain style="float: right;" @click="opendialog('daoruzhaopian','导入照片')">导入照片</el-button>
                <el-button type="primary" plain style="float: right;" @click="opendialog('daorubaodao','导入报道数据')">导入报道数据</el-button>
                <el-button type="primary" plain style="float: right;" @click="opendialog('daorujihua','导入计划数据')">导入计划数据</el-button>
<!--                <el-button type="primary" plain style="float: right;" @click="opendialog('jiemiluqv','解密录取数据')">解密录取数据</el-button>-->
                <el-button type="primary"  style="float: right;" @click="opendialog('daoruluqv','导入录取数据')">导入录取数据</el-button>
            </div>
            <div style="height: 100px;margin-top: 30px;display: flex;justify-content: space-around">
                <div style="width: 16.6%;height: 100%;text-align: left;border-right: 1px solid rgba(192,192,192,0.49);padding-left: 30px;">
                    <div style="font-size: 14px;color: rgb(169,169,169);height: 30px;line-height: 30px;">
                        计划数
                    </div>
                    <div style="font-size: 28px;height: 40px;line-height: 40px;">
                        {{dbtj[0].jhs !== '' ? dbtj[0].jhs : '-'}}
                    </div>
                    <div style="font-size: 12px;color: #409eff;height: 30px;line-height: 30px;cursor: pointer;" @click="tourl('enrollplan')">
                        查看详情
                    </div>
                </div>
                <div style="width: 16.6%;height: 100%;text-align: left;border-right: 1px solid rgba(192,192,192,0.49);padding-left: 30px;">
                    <div style="font-size: 14px;color: rgb(169,169,169);height: 30px;line-height: 30px;">
                        录取数
                    </div>
                    <div style="font-size: 28px;height: 40px;line-height: 40px;">
                        {{dbtj[0].lqs !== '' ? dbtj[0].lqs : '-'}}
                    </div>
                    <div style="font-size: 12px;color: #409eff;height: 30px;line-height: 30px;cursor: pointer;" @click="tourl()">
                        查看详情
                    </div>
                </div>
<!--                <div style="width: 16.6%;height: 100%;text-align: left;border-right: 1px solid rgba(192,192,192,0.49);padding-left: 30px;">-->
<!--                    <div style="font-size: 14px;color: rgb(169,169,169);height: 30px;line-height: 30px;">-->
<!--                        照片数-->
<!--                    </div>-->
<!--                    <div style="font-size: 28px;height: 40px;line-height: 40px;">-->
<!--                        {{dbtj[0].jhs}}-->
<!--                    </div>-->
<!--&lt;!&ndash;                    <div style="font-size: 12px;color: #409eff;height: 30px;line-height: 30px;">&ndash;&gt;-->
<!--&lt;!&ndash;                        查看详情&ndash;&gt;-->
<!--&lt;!&ndash;                    </div>&ndash;&gt;-->
<!--                </div>-->
                <div style="width: 16.6%;height: 100%;text-align: left;border-right: 1px solid rgba(192,192,192,0.49);padding-left: 30px;">
                    <div style="font-size: 14px;color: rgb(169,169,169);height: 30px;line-height: 30px;">
                        报到数
                    </div>
                    <div style="font-size: 28px;height: 40px;line-height: 40px;">
                        {{dbtj[0].bds !== '' ? dbtj[0].bds : '-'}}
                    </div>
                    <div style="font-size: 12px;color: #409eff;height: 30px;line-height: 30px;cursor: pointer;" @click="tourl('bdsj')">
                        查看详情
                    </div>
                </div>
                <div style="width: 16.6%;height: 100%;text-align: left;border-right: 1px solid rgba(192,192,192,0.49);padding-left: 30px;">
                    <div style="font-size: 14px;color: rgb(169,169,169);height: 30px;line-height: 30px;">
                        计划完成率
                    </div>
                    <div style="font-size: 28px;height: 40px;line-height: 40px;">
                        {{dbtj[0].jhwcl !== '' ? dbtj[0].jhwcl +'%' : '-'}}
                    </div>
<!--                    <div style="font-size: 12px;color: #409eff;height: 30px;line-height: 30px;">-->
<!--                        查看详情-->
<!--                    </div>-->
                </div>
                <div style="width: 16.6%;height: 100%;text-align: left;padding-left: 30px;">
                    <div style="font-size: 14px;color: rgb(169,169,169);height: 30px;line-height: 30px;">
                        报到率
                    </div>
                    <div style="font-size: 28px;height: 40px;line-height: 40px;">
                        {{dbtj[0].bdl !== '' ? dbtj[0].bdl +'%' : '-'}}
                    </div>
<!--                    <div style="font-size: 12px;color: #409eff;height: 30px;line-height: 30px;">-->
<!--                        查看详情-->
<!--                    </div>-->
                </div>

            </div>

        </div>
        <div class="content">
            <avue-tabs :option="option"
                       @change="handleChange"></avue-tabs>
            <div v-if="type.prop==='tab1'" style="width: 100%;height: 100%;background-color: #fff;padding: 0px 15px;">
                <avue-crud
                    ref="crud"
                    :data="sftj"
                    :option="crudoption"
                    :page.sync="page"
                    @selection-change="selectionChange"
                    @size-change="sizeChange"
                    @current-change="currentChange"
                >
                    <template slot-scope="{row}" slot="menu">
                        <el-button type="text"
                                   size="small"
                                   @click="tourl('enrollplan',row)"
                        >查看计划
                        </el-button>
                        <el-button type="text"
                                   size="small"
                                   @click="tourl('',row)"
                        >查看录取
                        </el-button>
<!--                        <el-button type="text"-->
<!--                                   size="small"-->
<!--                                   @click="deleteHandle(row)"-->
<!--                        >删除-->
<!--                        </el-button>-->
                    </template>
                    <template slot="zps" slot-scope="scope">
                        <span>{{scope.row.zps}}</span><span v-if="(scope.row.lqs - scope.row.zps) > 0" style="color: red!important;">（缺失: {{scope.row.lqs - scope.row.zps}}）</span>
                    </template>
                    <template slot="bmbs" slot-scope="scope">
                        <span>{{scope.row.bmbs}}</span><span v-if="(scope.row.lqs - scope.row.bmbs) > 0" style="color: red!important;">（缺失: {{scope.row.lqs - scope.row.bmbs}}）</span>
                    </template>
                    <template slot="tjbs" slot-scope="scope">
                        <span>{{scope.row.tjbs}}</span><span v-if="(scope.row.lqs - scope.row.tjbs) > 0" style="color: red!important;">（缺失: {{scope.row.lqs - scope.row.tjbs}}）</span>
                    </template>
                    <template slot="cjyzybs" slot-scope="scope">
                        <span>{{scope.row.cjyzybs}}</span><span v-if="(scope.row.lqs - scope.row.cjyzybs) > 0" style="color: red!important;">（缺失: {{scope.row.lqs - scope.row.cjyzybs}}）</span>
                    </template>
                </avue-crud>
            </div>
            <div v-else-if="type.prop==='tab2'" style="width: 100%;height: 100%;background-color: #fff;padding: 10px 15px;">
<!--                <avue-tabs :option="taboption"-->
<!--                           @change="tabhandleChange"></avue-tabs>-->
                <div style="width: 100%;height: 100%;background-color: #fff;display: flex;flex-wrap: wrap;justify-content: left;">
                    <div v-for="(item,index) in sftj" :key="index" class="crudbox">
                        <div class="crudtitle">
                            {{item.sfmc}}
                        </div>
<!--                        <div class="crudtip">-->
<!--                            本科-->
<!--                        </div>-->
                        <div class="crudcontent">
                            <div style="width: 60px;border-right: 1px solid rgba(192,192,192,0.49);text-align: center;margin-left: 10px;">
                                <div style="font-size: 14px;color: #448EF7;font-weight: bold;height: 30px;line-height: 30px;" @click="tourl('',item)">
                                    {{item.lqs}}
                                </div>
                                <div style="font-size: 12px;color: rgb(169,169,169);height: 20px;line-height: 20px;">
                                    录取数
                                </div>
                            </div>
                            <div style="width: 80px;border-right: 1px solid rgba(192,192,192,0.49);text-align: center">
                                <div style="font-size: 14px;color: #448EF7;font-weight: bold;height: 30px;line-height: 30px;" @click="tourl('',item)">
                                    {{item.zps}}
                                </div>
                                <div style="font-size: 12px;color: rgb(169,169,169);height: 20px;line-height: 20px;">
                                    录取照片数
                                </div>
                            </div>
                            <div style="width: 110px;text-align: center">
                                <div style="font-size: 14px;color: #448EF7;font-weight: bold;height: 30px;line-height: 30px;" @click="tourl('enrollplan',item)">
                                    {{item.tzjhs}}
                                </div>
                                <div style="font-size: 12px;color: rgb(169,169,169);height: 20px;line-height: 20px;">
                                    计划数（含调数）
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
<!--                <div v-else-if="tabtype.prop==='benke'" style="width: 100%;height: 200px;background-color: #fff">-->

<!--                </div>-->
<!--                <div v-else-if="tabtype.prop==='zhuanke'" style="width: 100%;height: 200px;background-color: #fff">-->

<!--                </div>-->
            </div>
        </div>
        <el-drawer
            :title="dialogtitle"
            :visible.sync="dialogVisible"
            :direction="direction"
            append-to-body
            :before-close="handleClose">
            <div style="padding: 0 20px;">
                <daoruluqv @onclose="onclose" v-if="opendialogtype.daoruluqv"></daoruluqv>
<!--                <jiemiluqv v-if="opendialogtype.jiemiluqv"></jiemiluqv>-->
                <daorujihua @onclose="onclose" v-if="opendialogtype.daorujihua"></daorujihua>
                <daorubaodao @onclose="onclose" v-if="opendialogtype.daorubaodao"></daorubaodao>
                <daoruzhaopian @onclose="onclose" v-if="opendialogtype.daoruzhaopian"></daoruzhaopian>
                <daorusanbiao @onclose="onclose" v-if="opendialogtype.daorusanbiao"></daorusanbiao>
<!--                <shujvzhuaqv v-if="opendialogtype.shujvzhuaqv"></shujvzhuaqv>-->
                <shujvqingkong @onclose="onclose" v-if="opendialogtype.shujvqingkong"></shujvqingkong>
            </div>
        </el-drawer>
    </div>
</template>

<script>
import {mapGetters} from "vuex";
import daoruluqv from "@/page/zsxtreception/improtmodul/daoruluqv";
import jiemiluqv from "@/page/zsxtreception/improtmodul/jiemiluqv";
import daorujihua from "@/page/zsxtreception/improtmodul/daorujihua";
import daorubaodao from "@/page/zsxtreception/improtmodul/daorubaodao";
import daoruzhaopian from "@/page/zsxtreception/improtmodul/daoruzhaopian";
import daorusanbiao from "@/page/zsxtreception/improtmodul/daorusanbiao";
import shujvzhuaqv from "@/page/zsxtreception/improtmodul/shujvzhuaqv";
import shujvqingkong from "@/page/zsxtreception/improtmodul/shujvqingkong";
import {getdbtj, getsftj} from "@/api/zsxt/tongji"
// import {deepClone} from "@/util/util";

export default {
    components:{
        daoruluqv,
        jiemiluqv,
        daorujihua,
        daorubaodao,
        daoruzhaopian,
        daorusanbiao,
        shujvzhuaqv,
        shujvqingkong
    },
    data() {
        return {
            nfoptions: [],
            direction: 'rtl',
            nfvalue: new Date().getFullYear(),
            type: {},
            tabtype: {},
            option: {
                column: [{
                    // icon: 'el-icon-info',
                    label: '列表展示',
                    prop: 'tab1',
                }, {
                    // icon: 'el-icon-warning',
                    label: '卡片展示',
                    prop: 'tab2',
                }]
            },
            taboption: {
                column: [{
                    // icon: 'el-icon-info',
                    label: '全部',
                    prop: 'all',
                }, {
                    // icon: 'el-icon-warning',
                    label: '本科',
                    prop: 'benke',
                }, {
                    // icon: 'el-icon-warning',
                    label: '高职（专科）',
                    prop: 'zhuanke',
                }]
            },
            datalist: [],
            crudoption: {
                indexTitle: '序号',
                menuAlign: "center",
                sizeValue: 'mini',
                align: 'left',
                indexWidth: 30,
                // menuWidth: 180,
                selection: false,
                tip: false,
                stripe: true,
                delBtn: false,
                header: false,
                editBtn: false,
                addBtn: false,
                searchBtn: false,
                emptyBtn: false,
                refreshBtn: false,
                searchShowBtn: false,
                columnBtn: false,
                column: [
                    // {
                    //     label: '序号',
                    //     prop: 'index',
                    //     // width: 50,
                    //     fixed: true
                    // },
                    {
                        label: "省份",
                        prop: "sfmc",
                    },
                    {
                        label: "招生计划数",
                        prop: "zsjhs",
                    },
                    {
                        label: "调整计划数",
                        prop: "tzjhs",
                    },
                    {
                        label: "录取数",
                        prop: "lqs",
                    },
                    {
                        label: "照片数",
                        prop: "zps",
                        solt: true,
                    },
                    {
                        label: "报名表数",
                        prop: "bmbs",
                        solt: true,

                    },
                    {
                        label: "体检表数",
                        prop: "tjbs",
                        solt: true,

                    },
                    {
                        label: "成绩与志愿表数",
                        prop: "cjyzybs",
                        solt: true,

                    },
                    {
                        label: "报到数",
                        prop: "bds",
                    },
                ],
            },
            page: {
                currentPage: 1,
                total: 5,
                pageSize: 7,
            },
            dialogVisible: false,
            opendialogtype: {
                daoruluqv: false,
                jiemiluqv: false,
                daorujihua: false,
                daorubaodao: false,
                daoruzhaopian: false,
                daorusanbiao: false,
                shujvzhuaqv: false,
                shujvqingkong: false,
            },
            dialogtitle: '',
            dialogtype: '',
            dbtj: null,
            sftj: null,
        };
    },
    created() {
        const now = new Date();
        const currentYear = now.getFullYear();
        const specifiedNumber = currentYear;
        this.nfoptions = this.generateNumberArray(specifiedNumber);
        this.type = this.option.column[0];
        this.getdbtjData();
    },
    computed: {
        ...mapGetters(["permission"]),
    },
    methods: {
        tourl(url,row){
            let query = ''
            if(url){
                if(row){
                  query = JSON.stringify({
                    syssmc: row.sfmc
                  })
                }
                this.$router.push({path:'/dataProcessing/'+url + '/index',query:{queryjson: query}});
            }else {
                if(row){
                  query = JSON.stringify({
                      sfmc: row.sfmc
                  })
                }
                this.$router.push({path:'/ksgl/tdd/index',query:{queryjson: query}});
            }

        },
        nfchange(value){
            this.getdbtjData();
        },
        getdbtjData(){
            getdbtj({nf: this.nfvalue}).then(res=>{
                this.dbtj = res.data.data;
                console.log(`getdbtj`,res.data)
            })
            getsftj({nf: this.nfvalue}).then(res=>{
                this.sftj = res.data.data;
                console.log(`getdbtj`,res.data)
            })
        },
        generateNumberArray(num) {
          const result = [];
          for (let i = num; i > num -10; i--) {
            result.push({
              label: i,
              value: i,
            });
          }

          return result;
        },
        submitform(){
            this.dialogVisible = false;
        },
        handleClose(done) {
            this.dialogVisible = false;
            this.opendialogtype[this.dialogtype] = false;
            this.getdbtjData();
            done();
        },
        onclose(data){
            this.dialogVisible = false;
            this.opendialogtype[this.dialogtype] = false;
            this.getdbtjData();
        },
        opendialog(type,title){
            this.dialogVisible = true;
            this.opendialogtype[type] = true;
            this.dialogtitle = title;
            this.dialogtype = type;
        },
        handleChange (column) {
            this.type = column
            // this.$message.success(JSON.stringify(column))
        },
        tabhandleChange(column){
            this.tabtype = column
        },
        sizeChange(val) {
            this.page.currentPage = 1;
            this.page.pageSize = val;
            // this.getList(this.pageParam(this.queryParam));
        },
        currentChange(val) {
            this.page.currentPage = val;
            // this.getList(this.pageParam(this.queryParam));
        },
        pageParam(queryParam) {
            return {
                page: this.page.currentPage,
                pageSize: this.page.pageSize,
                queryParam: queryParam
            }
        },
        selectionChange(list) {
            this.listhumancode = list;
        },
    }
};
</script>

<style>
.title{
    width: 98%;
    /*height: 100%;*/
    margin: auto;
    padding: 15px;
    background-color: #fff;
}
.wrap{
    height: 100%;
    width: 100%;
    /*border: 1px solid #00a5ec;*/
}
.content{
    width: 100%;
    padding: 15px;
}
.crudbox{
    height: 150px;
    width: 260px;
    border: 1px solid #ECECEC;
    border-radius: 3px;
    position: relative;
    margin-bottom: 10px;
    margin-right: 10px;
}
.crudtitle{
    height: 70px;
    line-height: 70px;
    font-size: 18px;
    font-weight: bold;
    margin-left: 20px;
}
.crudtip{
    height: 20px;
    width: 60px;
    background-color: #448EF7;
    position: absolute;
    right: 0;
    top: 20px;
    color: #fff;
    line-height: 20px;
    font-size: 12px;
    text-align: center;
    border-radius: 10px 0 0 10px;
}
.crudcontent{
    height: 50px;
    margin-top: 10px;
    width: 100%;
    display: flex;
}
.dialog-footer{
    text-align: center;
}
.my-html-element{
    color: red;
}
</style>
