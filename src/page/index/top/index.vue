<template>
    <div class="avue-top">
<!--        <div class="top-bar__left">-->
<!--            <div class="avue-breadcrumb"-->
<!--                 :class="[{ 'avue-breadcrumb&#45;&#45;active': isCollapse }]"-->
<!--                 v-if="showCollapse">-->
<!--                <i class="el-icon-s-fold" @click="setCollapse"></i>-->
<!--            </div>-->
<!--        </div>-->
        <div class="imgWrap" style="float: left;">
            <img class="logo" style="height: 40px;" :src="schoolLogo" alt />
        </div>
        <p class="systemName">{{ systemName }}</p>
        <ul class="toolbar">
            <router-link v-for="item in menuList" :key="item" tag="li" :to="item.path"
                         :activeClass="item.event ==null ?'toolbarClick':''">
                <p v-text="item.label"  @click="handleClick(item)"></p>
<!--                <p v-text="item.label" v-else></p>-->
                <span></span>
            </router-link>
        </ul>
        <div class="top-bar__title">
            <top-search></top-search>
            <p class="mobileTitle">数据分析</p>
        </div>
        <div class="top-bar__right">
            <!-- 文件管理图标 -->
            <div class="file-manager-icon" 
                 @click="openDownloadTask"
                 title="文件管理">
                <div class="folder-container">
                    <!-- 文件夹主体 -->
                    <div class="folder-body">
                        <div class="folder-tab"></div>
                        <div class="folder-main"></div>
                    </div>
                    <!-- 传输箭头 -->
                    <div class="transfer-arrows">
                        <div class="arrow arrow-in">↓</div>
                        <div class="arrow arrow-out">↑</div>
                    </div>
                </div>
                <span class="file-text">文件管理</span>
                <!-- 任务数量气泡 -->
                <div class="task-bubble" v-if="downloadTaskCount > 0">
                    {{ downloadTaskCount > 99 ? '99+' : downloadTaskCount }}
                </div>
            </div>
            <div style="display: inline-block;width: 30px; height: 30px;line-height: 1px;margin-top: 15px;">
                <img class="top-bar__img"
                     style="margin-right: 0px"
                     v-if="info.avatar"
                     :src="'/file/view/' + info.avatar"
                     :onerror="defaultImg"/>
                <img class="top-bar__img"
                     style="margin-right: 0px"
                     v-else
                     src="../../../styles/assets/image/touxiangf.png"/>
            </div>
            <el-dropdown style="margin-right: 20px">
                <span class="el-dropdown-link">
                  <!-- {{userInfo.username}} -->
                  <span class="loginName">{{ username }}</span>
                  <i class="el-icon-arrow-down el-icon--right"
                     style="vertical-align: top;color: #eee;"></i>
                </span>
                <el-dropdown-menu class="selectDrop" slot="dropdown">
                    <el-dropdown-item>
                        <div @click="checkRoleHandle">选择角色</div>
                    </el-dropdown-item>
                    <el-dropdown-item>
                        <router-link to="/mine/info/index">{{$t("navbar.userinfo")}}
                        </router-link>
                    </el-dropdown-item>
                    <el-dropdown-item>
                        <router-link to="/mine/password/index">修改密码</router-link>
                    </el-dropdown-item>
<!--                    <el-dropdown-item>-->
<!--                      <router-link to="/">返回前台</router-link>-->
<!--                    </el-dropdown-item>-->
                    <el-dropdown-item @click.native="logout" divided>{{$t("navbar.logOut")}}
                    </el-dropdown-item>
                </el-dropdown-menu>
            </el-dropdown>
        </div>
        <el-dialog class="role-container"
                   title="选择角色"
                   append-to-body
                   :visible.sync="isChangeRole"
                   width="300px"
                   :close-on-click-modal="false">
            <div class="role-item"
                 v-for="item in roleList"
                 :key="item"
                 :class="{ 'is-active': roleName === item }"
                 @click="changeRoleHandle(item)">
                {{ item }}
            </div>
        </el-dialog>
    </div>
</template>
<script>
import {getEMenuNologin, getRoleList, switchRole} from "@/api/user";
import {mapGetters, mapState} from "vuex";
import {fullscreenToggel, listenfullscreen} from "@/util/util";
import {setStore} from "@/util/store";
import {filterDesktop} from "@/util/filter";
import topLock from "./top-lock";
import topMenu from "./top-menu";
import topSearch from "./top-search";
import topTheme from "./top-theme";
import topLogs from "./top-logs";
import topColor from "./top-color";
import topNotice from "./top-notice";
import topLang from "./top-lang";
import {GetSysParamNoLogin} from "@/api/sysParam";

export default {
        components: {
            topLock,
            topMenu,
            topSearch,
            topTheme,
            topLogs,
            topColor,
            topNotice,
            topLang,
        },
        name: "top",
        data() {
            return {
                isChangeRole: false,
                roleList: [],
                menuList: [],
                userdata: null,
                roleName: "",
                username: "",
                schoolLogo: "",
                systemName: "",
                downloadTaskCount: 0, // 下载任务数量
                taskCountRefreshTimer: null, // 任务数量刷新定时器
                info: {
                    realname: "",
                    avatar: "",
                },
                defaultImg:
                    'this.src="' +
                    require("../../../styles/assets/image/touxiangf.png") +
                    '"',
            };
        },
        filters: {},
        created() {
            this.getRoleInfo();
            this.getDownloadTaskCount();
            this.startTaskCountRefresh();
        },
        mounted() {
            listenfullscreen(this.setScreen);
            // 监听任务数量变更事件
            this.$EventBus.$on('refreshDownloadTaskCount', this.getDownloadTaskCount);
        },
        beforeDestroy() {
            this.stopTaskCountRefresh();
            // 移除任务数量变更事件监听
            this.$EventBus.$off('refreshDownloadTaskCount', this.getDownloadTaskCount);
        },
        inject: ["appReload"],
        computed: {
            ...mapState({
                showDebug: (state) => state.common.showDebug,
                showTheme: (state) => state.common.showTheme,
                showLock: (state) => state.common.showLock,
                showFullScren: (state) => state.common.showFullScren,
                showCollapse: (state) => state.common.showCollapse,
                showSearch: (state) => state.common.showSearch,
                showMenu: (state) => state.common.showMenu,
                showColor: (state) => state.common.showColor,
            }),
            ...mapGetters([
                "userInfo",
                "isFullScren",
                "tagWel",
                "tagList",
                "isCollapse",
                "tag",
                "logsLen",
                "logsFlag",
            ]),
        },
        methods: {
            handleClick(o){
                console.log(`o===`,o)
                this.$router.push({path:o.path})
                // let split = o.split(",");
                // this[split[0]](split[1]);
            },
            getRoleInfo() {
                getRoleList().then((res) => {
                    if (res.data.code === "00000") {
                        this.userdata = res.data.info;
                        this.roleName = this.userdata.roleName;
                        this.username = this.userdata.humanname;
                        this.roleList = this.userdata.roleList;
                        setStore({name: "user-info", content: this.userdata});
                    }
                });
                getEMenuNologin().then((res)=>{
                    this.menuList = res.data.info;
                });
                GetSysParamNoLogin({ idOrNameOrType: "frontlogo" },).then((res) => {
                    // console.log(res);
                    const data = res.data.info;
                    let img = JSON.parse(data.img);
                    if (img.length > 0) {
                        this.schoolLogo = img[0].url;
                    }
                });
                GetSysParamNoLogin({ idOrNameOrType: "systemName" },).then((res) => {
                    // console.log(res);
                    const data = res.data.info;
                    this.systemName = data.value;

                });
            },
            // 切换角色
            checkRoleHandle() {
                this.isChangeRole = true;
            },
            changeRoleHandle(item) {
                switchRole({role: item}).then((res) => {
                    if (res.data && res.data.code === "00000") {
                        this.getRoleInfo();
                        this.isChangeRole = false;
                        this.$store.commit("DEL_ALL_TAG");
                        // this.$store.commit('SET_MENUID', {})
                        // this.$store.commit('SET_MENUALL', []);
                        // this.$store.commit('SET_MENU', [])
                        // this.$store.commit('SET_ROLES', [])


                        // window.location.href = "/index";
                        this.$message.success("角色切换成功！");
                        this.$router.replace({
                            path: this.$router.$avueRouter.getPath({
                                name: "首页",
                                src: "/wel/index",
                            }),
                            query: {},
                        });
                        this.appReload();
                    }
                });
            },
            handleScreen() {
                fullscreenToggel();
            },
            setCollapse() {
                this.$store.commit("SET_COLLAPSE");
            },
            setScreen() {
                this.$store.commit("SET_FULLSCREN");
            },
            logout() {
                this.$confirm(this.$t("logoutTip"), this.$t("tip"), {
                    confirmButtonText: this.$t("submitText"),
                    cancelButtonText: this.$t("cancelText"),
                    type: "warning",
                }).then(() => {
                    window.location.href = "/logout";
                });
            },
            toFront() {
                this.$router.push({
                    path: "/portal/index/common",
                });
            },
            toMineDesktopId() {
                filterDesktop("我的桌面").then((res) => {
                    this.$router.push("/portal/index/desktop?Did=" + res);
                });
            },
            // 打开下载任务管理页面
            openDownloadTask() {
                this.$router.push('/system/download-task');
            },
            // 获取下载任务数量
            getDownloadTaskCount() {
                // 调用API获取任务统计信息
                this.$http.get('/system/downloadTask/statistics').then(res => {
                    if (res.data && res.data.success) {
                        const stats = res.data.data;
                        // 徽章显示待处理和处理中的任务总数
                        this.downloadTaskCount = (stats.pending || 0) + (stats.processing || 0);
                    }
                }).catch(error => {
                    console.error('获取下载任务统计失败:', error);
                    this.downloadTaskCount = 0;
                });
            },
            
            // 开始任务数量刷新
            startTaskCountRefresh() {
                // 每30秒刷新一次任务数量
                this.taskCountRefreshTimer = setInterval(() => {
                    this.getDownloadTaskCount();
                }, 30000);
            },
            
            // 停止任务数量刷新
            stopTaskCountRefresh() {
                if (this.taskCountRefreshTimer) {
                    clearInterval(this.taskCountRefreshTimer);
                    this.taskCountRefreshTimer = null;
                }
            },
        },
    };
</script>

<style scoped>
    .avue-top {
      padding: 0 10px;
    }
    .systemName {
      display:inline-block;
      float:left;
      color: #fff;
      font-size: 16px;
      line-height: 55px;
      padding:0px;
      margin: 0px 0px 0px 15px;
    }
    .loginName {
        font-size: 14px;
        color: #eee;
        display: inline-block;
        width: 60px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }

    .toFront {
        position: absolute;
        font-size: 14px;
        padding: 0 10px;
        line-height: 50px;
        left: 0;
        top: 0;
        cursor: pointer;
    }

    .toFront:hover {
        background: #eee;
    }

    .el-icon-monitor {
        margin-right: 5px;
        font-size: 16px;
    }

    .el-dropdown {
        margin-left: 10px;
    }

    .el-dropdown-link {
        cursor: pointer;
    }

    .top-bar__title {
        padding-right: 210px;
    }

    .top-search {
        float: right;
        line-height: 60px;
        margin-top: -5px;
        margin-right: 20px;
    }

    .role-container >>> .el-dialog__body {
        padding: 10px 10px 30px;
    }

    .is-active {
        color: #00a5ec;
        font-weight: 700;
    }

    .role-item {
        height: 30px;
        line-height: 30px;
        padding: 0 10px;
        cursor: pointer;
    }

    .role-item:hover {
        background-color: #eee;
    }

    .my-dropdown >>> .el-dropdown-menu {
        color: red;
    }

    .avue-breadcrumb--active {
        -webkit-transform: rotate(180deg);
        transform: rotate(180deg);
    }

    .avue-breadcrumb i {
        font-size: 25px !important;
        font-weight: 100;
        color: #666;
    }

    .selectDrop {
        top: 40px !important;
        left: auto !important;
        right: 10px !important;
    }

    .mobileTitle {
        font-size: 16px;
        line-height: 50px;
        margin: 0;
        display: none;
    }
    .entryDiv{
        font-size: 14px;
        margin-left: 25px;
        line-height: 53px;
    }
    .entryDiv a{
        color: inherit;
    }
    .toolbar {
        /* position: absolute;
          left: 50%;
          top: 0;
          margin-left: -362px; */
        height: 50px;
        float: left;
        margin-left: 220px;
        line-height: 50px;
        font-size: 16px;
        color: #fff;
    }

    .toolbar li {
        float: left;
        height: 100%;
        padding: 0px;
        margin-right: 50px;
        cursor: pointer;
        color: #fff;
        font-size: 14px;
        border-top: 4px solid transparent;
        transition: all 0.3s ease-in-out;
    }
    .toolbar p{
        line-height: 50px;
        margin: 0;
    }
    .toolbar li:hover {
        border-top: 4px solid #ffffff !important;
        color: #fff;
    }

    /*.toolbarClick,.router-link-active {*/
    /*    border-top: 4px solid #f9c62d !important;*/
    /*    color: #f9c62d;*/
    /*}*/

    .toolbarClick {
        border-top: 4px solid #ffffff !important;
        color: #ffffff !important;
    }
    .imgWrap {
        float: left;
        margin-left: 5px;
        height: 40px;
        margin-top: 10px;
        overflow: hidden;
        margin-right: 10px;
    }
    
    /* 文件管理图标样式 */
    .file-manager-icon {
        position: relative;
        float: left;
        cursor: pointer;
        margin-right: 20px;
        margin-top: 10px;
        display: flex;
        flex-direction: column;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .file-manager-icon:hover {
        transform: translateY(-2px);
    }
    
    /* 文件夹容器 */
    .folder-container {
        position: relative;
        display: flex;
        align-items: center;
        margin-bottom: 6px;
    }
    
    /* 文件夹主体 */
    .folder-body {
        position: relative;
        width: 28px;
        height: 20px;
    }
    
    /* 文件夹标签页 */
    .folder-tab {
        position: absolute;
        top: 0;
        left: 0;
        width: 12px;
        height: 4px;
        background: #ffa726;
        border-radius: 2px 2px 0 0;
        transition: background-color 0.3s ease;
    }
    
    /* 文件夹主体部分 */
    .folder-main {
        position: absolute;
        top: 4px;
        left: 0;
        width: 28px;
        height: 16px;
        background: #ffa726;
        border-radius: 2px;
        border: 2px solid #ffa726;
        transition: all 0.3s ease;
        
        /* 添加一些内部装饰线条 */
        background-image: 
            linear-gradient(90deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.3) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.2) 1px, transparent 1px);
        background-size: 6px 2px, 8px 2px;
        background-position: 4px 6px, 4px 10px;
        background-repeat: repeat-x, repeat-x;
    }
    
    /* 传输箭头容器 */
    .transfer-arrows {
        position: absolute;
        right: -12px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        flex-direction: column;
        gap: 1px;
    }
    
    /* 箭头样式 */
    .arrow {
        font-size: 10px;
        line-height: 1;
        color: rgba(255, 255, 255, 0.8);
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        transition: all 0.3s ease;
        animation-duration: 2s;
        animation-iteration-count: infinite;
    }
    
    .arrow-in {
        color: #66bb6a;
        animation-name: pulse-in;
        animation-delay: 0s;
    }
    
    .arrow-out {
        color: #42a5f5;
        animation-name: pulse-out;
        animation-delay: 1s;
    }
    
    /* 悬停效果 */
    .file-manager-icon:hover .folder-tab,
    .file-manager-icon:hover .folder-main {
        background: #ff9800;
        border-color: #ff9800;
    }
    
    .file-manager-icon:hover .arrow {
        color: rgba(255, 255, 255, 1);
        transform: scale(1.2);
    }
    
    /* 文字标签 */
    .file-text {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
        line-height: 1;
        transition: color 0.3s ease;
        white-space: nowrap;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    }
    
    .file-manager-icon:hover .file-text {
        color: #fff;
    }
    
    /* 任务数量气泡 */
    .task-bubble {
        position: absolute;
        top: -8px;
        right: -10px;
        background: linear-gradient(135deg, #ff5722, #f44336);
        color: white;
        border-radius: 50%;
        min-width: 18px;
        height: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        box-shadow: 
            0 2px 8px rgba(244, 67, 54, 0.4),
            0 0 0 2px rgba(255, 255, 255, 0.3);
        z-index: 10;
        animation: bubble-pulse 2s infinite;
    }
    
    /* 动画效果 */
    @keyframes pulse-in {
        0%, 100% { 
            opacity: 0.6; 
            transform: translateY(0);
        }
        50% { 
            opacity: 1; 
            transform: translateY(2px);
        }
    }
    
    @keyframes pulse-out {
        0%, 100% { 
            opacity: 0.6; 
            transform: translateY(0);
        }
        50% { 
            opacity: 1; 
            transform: translateY(-2px);
        }
    }
    
    @keyframes bubble-pulse {
        0%, 100% {
            transform: scale(1);
            box-shadow: 
                0 2px 8px rgba(244, 67, 54, 0.4),
                0 0 0 2px rgba(255, 255, 255, 0.3);
        }
        50% {
            transform: scale(1.1);
            box-shadow: 
                0 4px 12px rgba(244, 67, 54, 0.6),
                0 0 0 3px rgba(255, 255, 255, 0.5);
        }
    }
</style>

