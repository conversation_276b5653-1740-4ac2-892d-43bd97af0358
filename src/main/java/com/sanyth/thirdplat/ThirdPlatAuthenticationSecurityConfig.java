package com.sanyth.thirdplat;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.annotation.SecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.DefaultSecurityFilterChain;
import org.springframework.security.web.authentication.AuthenticationFailureHandler;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.stereotype.Component;

/**
 *
 */
@Component
public class ThirdPlatAuthenticationSecurityConfig extends SecurityConfigurerAdapter<DefaultSecurityFilterChain, HttpSecurity> {

    // 自定义的成功失败处理器
    @Autowired
    private AuthenticationSuccessHandler imoocAuthenticationSuccessHandler;
    @Autowired
    private AuthenticationFailureHandler imoocAuthenticationFailureHandler;

    @Autowired
    private UserDetailsService userDetailsService;

    /**
     * 加入自定义的filter , provider
     * @param http
     * @throws Exception
     */
    @Override
    public void configure(HttpSecurity http) throws Exception {

        ThirdPlatAuthenticationFilter thirdPlatAuthenticationFilter = new ThirdPlatAuthenticationFilter();
        thirdPlatAuthenticationFilter.setAuthenticationManager(http.getSharedObject(AuthenticationManager.class));
//        SimpleUrlAuthenticationSuccessHandler simpleUrlAuthenticationSuccessHandler = new SimpleUrlAuthenticationSuccessHandler();
//        simpleUrlAuthenticationSuccessHandler.setAlwaysUseDefaultTargetUrl(true);
//        simpleUrlAuthenticationSuccessHandler.setDefaultTargetUrl("/");
//        thirdPlatAuthenticationFilter.setAuthenticationSuccessHandler(simpleUrlAuthenticationSuccessHandler);
        thirdPlatAuthenticationFilter.setAuthenticationSuccessHandler(imoocAuthenticationSuccessHandler);
        thirdPlatAuthenticationFilter.setAuthenticationFailureHandler(imoocAuthenticationFailureHandler);

        ThirdPlatAuthenticationProvider thirdPlatAuthenticationProvider = new ThirdPlatAuthenticationProvider();
        thirdPlatAuthenticationProvider.setUserDetailsService(userDetailsService);

        http.authenticationProvider(thirdPlatAuthenticationProvider)
                .addFilterAfter(thirdPlatAuthenticationFilter, UsernamePasswordAuthenticationFilter.class);

    }

}
