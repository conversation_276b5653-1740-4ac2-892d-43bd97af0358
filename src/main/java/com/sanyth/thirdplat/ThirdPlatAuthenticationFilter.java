package com.sanyth.thirdplat;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.core.common.SpringTool;
import com.sanyth.core.exception.ValidateException;
import com.sanyth.mapper.system.SytPermissionAccountMapper;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.util.AESUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.authentication.AbstractAuthenticationProcessingFilter;
import org.springframework.security.web.util.matcher.AntPathRequestMatcher;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 *
 */
public class ThirdPlatAuthenticationFilter extends AbstractAuthenticationProcessingFilter {

    @Resource
    SytPermissionAccountMapper sytPermissionAccountMapper;
    @Resource
    private RedisTemplate redisTemplate;

    private String authenticationParameter = "authentication";
    private String originParameter = "Origin";
    private boolean postOnly = true;

    public ThirdPlatAuthenticationFilter() {
        super(new AntPathRequestMatcher("/third_plat_login", "GET"));
    }

    public Authentication attemptAuthentication(HttpServletRequest request, HttpServletResponse response) throws AuthenticationException {
        if (postOnly && !request.getMethod().equals("GET")) {
            throw new AuthenticationServiceException(
                    "Authentication method not supported: " + request.getMethod());
        }

        String authentication = request.getParameter(authenticationParameter);
        String origin = request.getParameter(originParameter);
        response.setHeader( "Access-Control-Allow-Origin", origin);

        if(StringUtils.isBlank(authentication)){
            throw new ValidateException("无权限访问");
        }

        sytPermissionAccountMapper = (SytPermissionAccountMapper) SpringTool.getApplicationContext().getBean("sytPermissionAccountMapper");
        String humancode = AESUtil.decrypt(authentication).trim();
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        wrapper.eq("humancode", humancode);
        SytPermissionAccount account = sytPermissionAccountMapper.selectOne(wrapper);
        if (null == account) {
            throw new ValidateException("用户不存在");
        }

        UsernamePasswordAuthenticationToken authRequest = new UsernamePasswordAuthenticationToken(humancode, account.getPassword().trim());

        // Allow subclasses to set the "details" property
        setDetails(request, authRequest);

        return this.getAuthenticationManager().authenticate(authRequest);
    }

    protected void setDetails(HttpServletRequest request, UsernamePasswordAuthenticationToken authRequest) {
        authRequest.setDetails(authenticationDetailsSource.buildDetails(request));
    }

    public void setPostOnly(boolean postOnly) {
        this.postOnly = postOnly;
    }

}
