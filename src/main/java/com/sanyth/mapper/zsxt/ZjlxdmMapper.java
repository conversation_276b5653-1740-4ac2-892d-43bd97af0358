
package com.sanyth.mapper.zsxt;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanyth.model.zsxt.Zjlxdm;
import com.sanyth.vo.ZjlxdmVO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR> @since 2024-04-01
 */
@Mapper
public interface ZjlxdmMapper extends BaseMapper<Zjlxdm> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param zjlxdm
	 * @return
	 */
	List<ZjlxdmVO> selectZjlxdmPage(IPage page, ZjlxdmVO zjlxdm);

}
