
package com.sanyth.model.zsxt;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 实体类
 *
 * <AUTHOR> @since 2024-04-02
 */
@Data
@TableName("TD_CZLBDM")
@ApiModel(value = "Czlbdm对象", description = "Czlbdm对象")
public class Czlbdm implements Serializable {

    private static final long serialVersionUID = 1L;
  @TableId(type = IdType.ASSIGN_ID)
  private String id;
    /**
     * 编码
     */
    @ApiModelProperty(value = "编码")
    private String code;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String bz;
    /**
     * 年份
     */
    @ApiModelProperty(value = "年份")
    private String nf;
    /**
     * 省份代码
     */
    @ApiModelProperty(value = "省份代码")
    private String sfdm;
    /**
     * 省份名称
     */
    @ApiModelProperty(value = "省份名称")
    private String sfmc;


}
