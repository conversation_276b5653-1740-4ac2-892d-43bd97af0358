package com.sanyth.platform.qywx.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.sanyth.platform.qywx.enums.QywxParamKey;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@TableName("platform_qywx_param")
public class QywxParam extends Model<QywxParam> {

//    @TableField("key")
    @TableId(type = IdType.INPUT)
    private QywxParamKey key;
//    @TableField("enabled")
    private Boolean enabled;
    private String value;
//    @TableField("updated")
    private Date updated;
//    @TableField("created")
//    private Date created;
    private String description;


}
