package com.sanyth.platform.qywx.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @since 2022/2/23 11:23
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("platform_qywx_user")
public class QywxUser extends Model<QywxUser> {

    @TableId(type = IdType.ASSIGN_UUID)
    private String id;
    /**
     * 企业微信通讯录账号
     */
    private String wxId;
    /**
     * 系统用户账号
     */
    private String sysId;
    /**
     * 是否接收消息
     */
    private Boolean isReceiveMessage;
    private String description;
    private Date created;
    private Date updated;
}
