package com.sanyth.platform.qywx.web;

import com.sanyth.platform.qywx.dao.QywxAppDao;
import com.sanyth.platform.qywx.dao.QywxParamDao;
import com.sanyth.platform.qywx.enums.QywxParamKey;
import com.sanyth.platform.qywx.model.QywxApp;
import com.sanyth.platform.qywx.service.QywxClientService;
import com.sanyth.platform.utils.PlatformRequestUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

/**
 * @since 2022/3/17 17:03
 */
@Controller
@RequestMapping("/platform/qywx/user_bind_page")
public class QywxUserBindPageController {

    private final static String self_url = "/platform/qywx/user_bind_page";
    private final QywxParamDao paramDao;
    private final RedisTemplate<String, String> redisTemplate;
    private final QywxClientService clientService;
    private final QywxAppDao appDao;

    public QywxUserBindPageController(QywxParamDao paramDao, RedisTemplate<String, String> redisTemplate, QywxClientService clientService, QywxAppDao appDao) {
        this.paramDao = paramDao;
        this.redisTemplate = redisTemplate;
        this.clientService = clientService;
        this.appDao = appDao;
    }

    @GetMapping("portal")
    public String portal(String id, HttpServletRequest request) throws UnsupportedEncodingException {
//        LinkedHashMap<String, String> map = new LinkedHashMap<>();
//        map.put("id", id);
        String redirect_uri = PlatformRequestUtil.getUrlRoot(request)+ self_url+"/callback";
        String encodedRedirectUrl = URLEncoder.encode(redirect_uri, "utf-8");
        String corpId = paramDao.findValueByKey(QywxParamKey.CorpID);
        String url = String.format("https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s" +
                "&redirect_uri=%s&response_type=code&scope=snsapi_base&state=%s#wechat_redirect", corpId, encodedRedirectUrl, id);
        return "redirect:"+url;
    }

    @GetMapping("callback")
    public void callback(String code, String state) {
        String humancode = redisTemplate.opsForValue().get(state);

        String corpId = paramDao.findValueByKey(QywxParamKey.CorpID);
        String appId = paramDao.findValueByKey(QywxParamKey.BindAppId);
        QywxApp app = appDao.getById(appId);
        String accessToken = clientService.getAccessToken(corpId, app.getSecret());
    }
}
