package com.sanyth.platform.qywx.web;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.mapper.system.SytPermissionAccountMapper;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.platform.qywx.dao.QywxAppDao;
import com.sanyth.platform.qywx.dao.QywxParamDao;
import com.sanyth.platform.qywx.dao.QywxUserDao;
import com.sanyth.platform.qywx.dto.QywxUserInfoResponse;
import com.sanyth.platform.qywx.enums.QywxParamKey;
import com.sanyth.platform.qywx.model.QywxApp;
import com.sanyth.platform.qywx.model.QywxUser;
import com.sanyth.platform.qywx.service.NoAuthQywxToAuthService;
import com.sanyth.platform.qywx.service.QywxClientService;
import com.sanyth.platform.utils.PlatformRequestUtil;
import org.springframework.stereotype.Controller;
import org.springframework.util.Base64Utils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @since 2022/3/16 11:08
 */
@RequestMapping("/noAuth/platform/qywx/toAuth")
@Controller
public class NoAuthQywxToAuthController {

    private final QywxClientService clientService;
    private final QywxParamDao paramDao;
    private final QywxAppDao appDao;
    private final QywxUserDao qywxUserDao;
    private final SytPermissionAccountMapper sytPermissionAccountMapper;
    private final NoAuthQywxToAuthService noAuthQywxToAuthService;

    public NoAuthQywxToAuthController(QywxClientService clientService, QywxParamDao paramDao, QywxAppDao appDao, QywxUserDao qywxUserDao, SytPermissionAccountMapper sytPermissionAccountMapper, NoAuthQywxToAuthService noAuthQywxToAuthService) {
        this.clientService = clientService;
        this.paramDao = paramDao;
        this.appDao = appDao;
        this.qywxUserDao = qywxUserDao;
        this.sytPermissionAccountMapper = sytPermissionAccountMapper;
        this.noAuthQywxToAuthService = noAuthQywxToAuthService;
    }

    @GetMapping("directive")
    public String directive(String encodeUrl) {
        return "redirect:/noAuth/platform/qywx/toAuth/oauth2Redirect?encodeUrl=" + encodeUrl;
    }

    @GetMapping("oauth2Redirect")
    public String oauth2Redirect(String appId, String encodeUrl) throws UnsupportedEncodingException {
//        String appId2 = paramDao.findValueByKey(QywxParamKey.Oauth2AppId);
//        QywxApp app = appDao.getById(appId2);
        String urlRoot = PlatformRequestUtil.getUrlRoot();
        String callbackUrl = urlRoot + "/noAuth/platform/qywx/toAuth/oauth2Callback?encodeUrl=" + encodeUrl;
        String callbackUrlEncode = URLEncoder.encode(callbackUrl, "utf-8");

        String redirectUrl = "https://open.weixin.qq.com/connect/oauth2/authorize" +
                "?appid=" + paramDao.findValueByKey(QywxParamKey.CorpID) +
                "&redirect_uri=" + callbackUrlEncode + "&response_type=code&scope=snsapi_base" +
                "&state=state" +
                "#wechat_redirect";

        return "redirect:" + redirectUrl;
    }

    @GetMapping("oauth2Callback")
    public String oauth2Callback(String code, String state, String encodeUrl) {
        String corpId = paramDao.findValueByKey(QywxParamKey.CorpID);
        String appId2 = paramDao.findValueByKey(QywxParamKey.Oauth2AppId);
        QywxApp app = appDao.getById(appId2);
        String accessToken = clientService.getAccessToken(corpId, app.getSecret());
        QywxUserInfoResponse userInfo = clientService.getUserInfo(accessToken, code);
        String userId = userInfo.getUserId();
        String humanCode = null;

        QueryWrapper<SytPermissionAccount> accountQueryWrapper = new QueryWrapper<>();
        accountQueryWrapper.eq("humancode", userId);
        Long count = sytPermissionAccountMapper.selectCount(accountQueryWrapper);
        if (count == 0) {
            QywxUser qywxUserQ = new QywxUser();
            qywxUserQ.setWxId(userId);
            QywxUser qywxUser = qywxUserDao.queryFirst(qywxUserQ);
            if (qywxUser != null) {
                humanCode = qywxUser.getSysId();
            }
        }else {
            humanCode = userId;
        }

        if (StringUtils.hasText(humanCode)) {
            noAuthQywxToAuthService.doAuth(humanCode);
        }
        return "redirect:" + new String(Base64Utils.decodeFromUrlSafeString(encodeUrl), StandardCharsets.UTF_8);
    }

//    @GetMapping("encodeUrlTest")
//    @ResponseBody
//    public String encodeUrlTest(String url) {
//        return noAuthQywxToAuthService.encodeUrl(url);
//    }
}
