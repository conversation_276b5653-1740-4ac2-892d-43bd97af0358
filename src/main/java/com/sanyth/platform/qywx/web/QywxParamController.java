package com.sanyth.platform.qywx.web;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.core.commonDeal.ApiR;
import com.sanyth.core.commonDeal.REBaseApiController;
import com.sanyth.platform.dto.NameValuePair;
import com.sanyth.platform.qywx.dao.QywxParamDao;
import com.sanyth.platform.qywx.enums.QywxParamKey;
import com.sanyth.platform.qywx.model.QywxParam;
import com.sanyth.platform.qywx.query.QywxParamQuery;
import com.sanyth.platform.qywx.service.QywxParamService;
import com.sanyth.platform.utils.EnumUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @since 2022/2/21 14:30
 */
@Slf4j
@RestController
@RequestMapping("/platform/qywx/param")
public class QywxParamController extends REBaseApiController {

    private final QywxParamDao paramDao;
    private final QywxParamService paramService;

    public QywxParamController(QywxParamDao paramDao, QywxParamService paramService) {
        this.paramDao = paramDao;
        this.paramService = paramService;
    }

    @GetMapping("list")
    public ApiR<Page<QywxParam>> list(QywxParamQuery query) {
        Page<QywxParam> page = paramDao.queryPage(query);
        return ApiR.ok(page);
    }

    @PostMapping("save")
    public ApiR<String> save(@RequestBody QywxParam dto) {
        paramService.save(dto);
        return ApiR.ok();
    }

    @DeleteMapping("delete")
    public ApiR<String> delete(@RequestBody QywxParam dto) {
        paramDao.removeById(dto.getKey());
        return ApiR.ok();
    }

    @GetMapping("get")
    public ApiR<QywxParam> get(Long id) {
        QywxParam select = paramDao.getById(id);
        return ApiR.ok(select);
    }

    @GetMapping("paramKeys")
    public List<NameValuePair> paramKeys() {
        return EnumUtil.nameValuePairs(QywxParamKey.values());
    }
}
