package com.sanyth.platform.qywx.service;

import java.util.Set;

public interface QywxPushMessageService {
    void pushMessageText(Set<String> userIds, String content);

    /**
     * 文本卡片消息
     *
     * @param userIds     用户名
     * @param title       标题，不超过128个字节，超过会自动截断（支持id转译）
     * @param description 描述，不超过512个字节，超过会自动截断（支持id转译）
     * @param url         点击后跳转的链接。最长2048字节，请确保包含了协议头(http/https)
     * @since 2022/9/9 16:45
     */
    void pushMessageTextCard(Set<String> userIds, String title, String description, String url);
}
