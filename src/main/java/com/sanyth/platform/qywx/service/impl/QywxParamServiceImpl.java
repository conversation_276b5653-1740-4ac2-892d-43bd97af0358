package com.sanyth.platform.qywx.service.impl;

import com.sanyth.platform.qywx.dao.QywxParamDao;
import com.sanyth.platform.qywx.model.QywxParam;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class QywxParamServiceImpl implements com.sanyth.platform.qywx.service.QywxParamService {

    private final QywxParamDao paramDao;

    public QywxParamServiceImpl(QywxParamDao paramDao) {
        this.paramDao = paramDao;
    }

    @Override
    public void save(QywxParam dto) {
        dto.setUpdated(new Date());
        paramDao.saveOrUpdate(dto);
    }

}
