package com.sanyth.platform.qywx.service;

import com.sanyth.platform.qywx.dto.QywxUserInfoResponse;

import java.util.Collection;

/**
 * @since 2022/3/8 9:46
 */
public interface QywxClientService {
    String getAccessToken(String corpId, String secret);

    void sendMessageText(String accessToken, Integer agentId, String content, Collection<String> wxUserIds);

    QywxUserInfoResponse getUserInfo(String accessToken, String code);

    void sendMessageTextCard(String accessToken, Integer agentId, Collection<String> wxUserIds, String title, String description, String url);
}
