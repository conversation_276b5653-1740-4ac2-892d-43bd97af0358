package com.sanyth.platform.qywx.service.impl;

import com.sanyth.platform.error.PlatformRequestException;
import com.sanyth.platform.qywx.dao.QywxAppDao;
import com.sanyth.platform.qywx.dao.QywxParamDao;
import com.sanyth.platform.qywx.dao.QywxUserDao;
import com.sanyth.platform.qywx.dto.QywxUserInfoResponse;
import com.sanyth.platform.qywx.enums.QywxParamKey;
import com.sanyth.platform.qywx.model.QywxApp;
import com.sanyth.platform.qywx.model.QywxUser;
import com.sanyth.platform.qywx.service.QywxClientService;
import com.sanyth.platform.qywx.service.QywxUserService;
import org.springframework.stereotype.Service;

/**
 * @since 2022/8/25 16:43
 */
@Service
public class QywxUserBindServiceImpl implements com.sanyth.platform.qywx.service.QywxUserBindService {

    private final QywxUserDao userDao;
    private final QywxUserService qywxUserService;
    private final QywxClientService clientService;
    private final QywxAppDao appDao;
    private final QywxParamDao paramDao;

    public QywxUserBindServiceImpl(QywxUserDao userDao, QywxUserService qywxUserService, QywxClientService clientService, QywxAppDao appDao, QywxParamDao paramDao) {
        this.userDao = userDao;
        this.qywxUserService = qywxUserService;
        this.clientService = clientService;
        this.appDao = appDao;
        this.paramDao = paramDao;
    }

    @Override
    public void bindCallback(String code, String state) throws PlatformRequestException {
        String corpId = paramDao.findValueByKey(QywxParamKey.CorpID);
        String appId = paramDao.findValueByKey(QywxParamKey.BindAppId);
        QywxApp app = appDao.getById(appId);
        String accessToken = clientService.getAccessToken(corpId, app.getSecret());
        QywxUserInfoResponse userInfo = clientService.getUserInfo(accessToken, code);

        QywxUser qywxUser = new QywxUser();
        qywxUser.setWxId(userInfo.getUserId());
        qywxUser.setSysId(state);
        long count = userDao.count(qywxUser);
        PlatformRequestException.isTrue(count == 0, "已有重复绑定记录");
        qywxUserService.save(qywxUser);
    }
}
