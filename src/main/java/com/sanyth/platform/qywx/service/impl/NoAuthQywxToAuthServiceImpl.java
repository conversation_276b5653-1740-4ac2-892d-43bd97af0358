package com.sanyth.platform.qywx.service.impl;

import com.sanyth.platform.qywx.dao.QywxAppDao;
import com.sanyth.platform.qywx.dao.QywxParamDao;
import com.sanyth.platform.qywx.enums.QywxParamKey;
import com.sanyth.platform.qywx.model.QywxApp;
import com.sanyth.service.system.MyUserDetailsService;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;

import java.nio.charset.StandardCharsets;

/**
 * @since 2022/8/29 15:42
 */
@Service
public class NoAuthQywxToAuthServiceImpl implements com.sanyth.platform.qywx.service.NoAuthQywxToAuthService {

    private final MyUserDetailsService myUserDetailsService;
    private final QywxParamDao qywxParamDao;
    private final QywxAppDao qywxAppDao;

    public NoAuthQywxToAuthServiceImpl(MyUserDetailsService myUserDetailsService, QywxParamDao qywxParamDao, QywxAppDao qywxAppDao) {
        this.myUserDetailsService = myUserDetailsService;
        this.qywxParamDao = qywxParamDao;
        this.qywxAppDao = qywxAppDao;
    }

    @Override
    public void doAuth(String humanCode) {
        SecurityContext context = SecurityContextHolder.createEmptyContext();

        UserDetails userDetails = myUserDetailsService.loadUserByUsername(humanCode);
        UsernamePasswordAuthenticationToken authentication =
                new UsernamePasswordAuthenticationToken(userDetails, userDetails.getPassword(), userDetails.getAuthorities());
        context.setAuthentication(authentication);

        SecurityContextHolder.setContext(context);
    }

    @Override
    public String encodeUrl(String url) {
        String appId = qywxParamDao.findValueByKey(QywxParamKey.Oauth2AppId);
        QywxApp app = qywxAppDao.getById(appId);
        return app.getRedirectUrlStart() + "/noAuth/platform/qywx/toAuth/directive?encodeUrl=" + Base64Utils.encodeToUrlSafeString(url.getBytes(StandardCharsets.UTF_8));
    }
}
