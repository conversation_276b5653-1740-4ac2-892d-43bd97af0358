package com.sanyth.platform.qywx.dao.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.commonDeal.BaseQueryInterface;
import com.sanyth.platform.qywx.dao.QywxUserDao;
import com.sanyth.platform.qywx.mapper.QywxUserMapper;
import com.sanyth.platform.qywx.model.QywxUser;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Set;

@Service
public class QywxUserDaoImpl extends ServiceImpl<QywxUserMapper, QywxUser> implements QywxUserDao {

    @Override
    public Page<QywxUser> queryPage(BaseQueryInterface<QywxUser> query) {
        Page<QywxUser> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<QywxUser> wrapper = buildWrapper(query.getParam());
//        long count = this.count(wrapper);
        return page(page, wrapper);
    }

    private QueryWrapper<QywxUser> buildWrapper(QywxUser query) {
        QueryWrapper<QywxUser> wrapper = new QueryWrapper<>();
        if (query != null) {
            if (!StringUtils.isEmpty(query.getWxId())) {
                wrapper.eq("wx_id", query.getWxId());
            }
            if (!StringUtils.isEmpty(query.getSysId())) {
                wrapper.eq("sys_id", query.getSysId());
            }
            if (query.getIsReceiveMessage() != null) {
                wrapper.eq("is_receive_message", query.getIsReceiveMessage());
            }
        }
        wrapper.orderByDesc("created");
        return wrapper;
    }

    @Override
    public List<QywxUser> queryReceiveMessageUsersBySysId(Set<String> sysIds) {
        QueryWrapper<QywxUser> wrapper = new QueryWrapper<>();
        wrapper.in("sys_id", sysIds);
        wrapper.eq("is_receive_message", true);
        return list(wrapper);
    }

    @Override
    public long count(QywxUser query) {
        return count(buildWrapper(query));
    }

    @Override
    public QywxUser queryFirst(QywxUser query) {
        Page<QywxUser> page = new Page<>(1, 1);
        QueryWrapper<QywxUser> wrapper = buildWrapper(query);
        Page<QywxUser> page1 = page(page, wrapper);
        List<QywxUser> records = page1.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return null;
        }
        return records.get(0);
    }
}
