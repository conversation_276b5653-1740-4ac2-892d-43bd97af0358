package com.sanyth.platform.qywx.dao.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.commonDeal.BaseQueryInterface;
import com.sanyth.platform.qywx.dao.QywxParamDao;
import com.sanyth.platform.qywx.enums.QywxParamKey;
import com.sanyth.platform.qywx.mapper.QywxParamMapper;
import com.sanyth.platform.qywx.model.QywxParam;
import org.springframework.stereotype.Service;

@Service
public class QywxParamDaoImpl extends ServiceImpl<QywxParamMapper, QywxParam> implements QywxParamDao {

    private Wrapper<QywxParam> buildWrapper(QywxParam query) {
        QueryWrapper<QywxParam> wrapper = new QueryWrapper<>();
        if (query != null) {
            if (query.getKey() != null) {
                wrapper.eq("key", query.getKey());
            }
            if (query.getEnabled() != null) {
                wrapper.eq("enabled", query.getEnabled());
            }
        }
        wrapper.orderByDesc("key");
        return wrapper;
    }

    @Override
    public Page<QywxParam> queryPage(BaseQueryInterface<QywxParam> query) {
        Page<QywxParam> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<QywxParam> wrapper = buildWrapper(query.getParam());
//        long count = this.count(wrapper);
        return page(page, wrapper);
    }

    @Override
    public String findValueByKey(QywxParamKey key) {
        QueryWrapper<QywxParam> wrapper = new QueryWrapper<>();
        wrapper.eq("key", key);
        wrapper.eq("enabled", true);
        wrapper.orderByDesc("updated");
        QywxParam one = getOne(wrapper);
        if (one != null) {
            return one.getValue();
        }
        return null;
    }

    @Override
    public boolean findEnabledByKey(QywxParamKey key) {
        QueryWrapper<QywxParam> wrapper = new QueryWrapper<>();
        wrapper.eq("key", key);
        wrapper.eq("enabled", true);
        wrapper.orderByDesc("updated");
        long count = count(wrapper);
        return count > 0;
    }

}
