package com.sanyth.platform.qywx.dao.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.commonDeal.BaseQueryInterface;
import com.sanyth.platform.qywx.dao.QywxAppDao;
import com.sanyth.platform.qywx.mapper.QywxAppMapper;
import com.sanyth.platform.qywx.model.QywxApp;
import org.springframework.stereotype.Service;

@Service
public class QywxAppDaoImpl extends ServiceImpl<QywxAppMapper, QywxApp> implements QywxAppDao {

    @Override
    public Page<QywxApp> queryPage(BaseQueryInterface<QywxApp> query) {
        Page<QywxApp> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<QywxApp> wrapper = buildWrapper(query.getParam());
//        long count = this.count(wrapper);
        return page(page, wrapper);
    }

    private Wrapper<QywxApp> buildWrapper(QywxApp query) {
        QueryWrapper<QywxApp> wrapper = new QueryWrapper<>();
//        if (StringUtils.isNotBlank(query.getRuleId())) {
//            wrapper.eq("ruleId", query.getRuleId());
//        }
//        if (StringUtils.isNotBlank(query.getTaskName())) {
//            wrapper.like("taskName", query.getTaskName());
//        }
//        if (query.getStatus() != null) {
//            wrapper.eq("status", query.getStatus());
//        }
        wrapper.orderByDesc("created");
        return wrapper;
    }
}
