package com.sanyth.platform.qywx.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.commonDeal.BaseQueryInterface;
import com.sanyth.platform.qywx.enums.QywxParamKey;
import com.sanyth.platform.qywx.model.QywxParam;

/**
 * @since 2022/2/21 11:46
 */
public interface QywxParamDao extends IService<QywxParam> {
    Page<QywxParam> queryPage(BaseQueryInterface<QywxParam> query);

    String findValueByKey(QywxParam<PERSON>ey key);

    boolean findEnabledByKey(QywxParamKey key);
}
