package com.sanyth.platform.qywx.query;

import com.sanyth.core.commonDeal.BaseQueryAbstract;
import com.sanyth.core.commonDeal.BaseQueryInterface;
import com.sanyth.platform.qywx.model.QywxUser;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2022/3/4 16:39
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QywxUserQuery extends BaseQueryAbstract implements BaseQueryInterface<QywxUser> {

    private QywxUser param;

}
