package com.sanyth.platform.qywx.query;

import com.sanyth.core.commonDeal.BaseQueryAbstract;
import com.sanyth.core.commonDeal.BaseQueryInterface;
import com.sanyth.platform.qywx.model.QywxParam;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @since 2022/2/28 11:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class QywxParamQuery extends BaseQueryAbstract implements BaseQueryInterface<QywxParam> {

    private QywxParam param;

}
