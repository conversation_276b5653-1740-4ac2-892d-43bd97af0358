package com.sanyth.platform.utils;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @since 2022/3/7 17:09
 */
public class PlatformRequestUtil {

    /**
     * 格式化参数
     *
     * @since 2021/8/4 15:21
     */
    public static String formatParameter(Map<String, String> map) {
        return map.size() > 0 ? "?" + map.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.joining("&")) : "";
    }

    public static String getUrlRoot(HttpServletRequest request) {
        String scheme = request.getScheme();
        int serverPort = request.getServerPort();
        String url_root = scheme + "://" + request.getServerName();
        if (serverPort != 80 && serverPort != 443) {
            url_root += ":" + serverPort;
        }
        return url_root;
    }

    public static HttpServletRequest currentRequest() {
        ServletRequestAttributes attr = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
        return attr.getRequest();
    }

    public static String getUrlRoot() {
        HttpServletRequest request = currentRequest();
        return getUrlRoot(request);
    }
}
