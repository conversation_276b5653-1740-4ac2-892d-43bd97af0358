package com.sanyth.platform.utils;

import com.sanyth.platform.dto.NameValuePair;

import java.util.LinkedList;
import java.util.List;

public class EnumUtil {

    public static List<NameValuePair> nameValuePairs(EnumTextI[] values) {
        List<NameValuePair> list = new LinkedList<>();
        for (EnumTextI p : values) {
            list.add(new NameValuePair(p.getText(), p.toString()));
        }
        return list;
    }
}
