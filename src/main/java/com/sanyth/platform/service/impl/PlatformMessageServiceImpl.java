package com.sanyth.platform.service.impl;

import com.sanyth.platform.qywx.service.NoAuthQywxToAuthService;
import com.sanyth.platform.qywx.service.QywxPushMessageService;
import com.sanyth.platform.service.PlatformMessageService;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashSet;

/**
 * @since 2022/3/11 8:57
 */
@Service
public class PlatformMessageServiceImpl implements PlatformMessageService {

//    private final Logger logger = LoggerFactory.getLogger(PlatformMessageServiceImpl.class);
    private final QywxPushMessageService qywxPushMessageService;
    private final NoAuthQywxToAuthService noAuthQywxToAuthService;

    public PlatformMessageServiceImpl(QywxPushMessageService qywxPushMessageService, NoAuthQywxToAuthService noAuthQywxToAuthService) {
        this.qywxPushMessageService = qywxPushMessageService;
        this.noAuthQywxToAuthService = noAuthQywxToAuthService;
    }

    @Override
    public void pushMessage(String content, String... userIds) {
        qywxPushMessageService.pushMessageText(new HashSet<>(Arrays.asList(userIds)), content);
    }

    @Override
    public void pushMessageTextCard(String title, String description, String url, String... userIds) {
        qywxPushMessageService.pushMessageTextCard(new HashSet<>(Arrays.asList(userIds)), title, description, url);
    }

    @Override
    public String encodeUrl(String url) {
        return noAuthQywxToAuthService.encodeUrl(url);
    }
}
