package com.sanyth.platform.service.impl;

import com.sanyth.platform.service.PlatformActuatorService;
import org.springframework.stereotype.Service;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @since 2022/3/11 9:28
 */
@Service
public class PlatformActuatorServiceImpl implements PlatformActuatorService {

    private final ExecutorService executorService = Executors.newCachedThreadPool();

    @Override
    public void execute(Runnable command) {
        executorService.execute(command);
    }

    @Override
    public ExecutorService getExecutor() {
        return executorService;
    }
}
