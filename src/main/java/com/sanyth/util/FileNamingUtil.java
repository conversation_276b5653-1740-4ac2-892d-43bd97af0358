package com.sanyth.util;

import com.sanyth.enums.FileNamingRuleEnum;
import com.sanyth.model.ksgl.Tdd;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件命名工具类
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
public class FileNamingUtil {
    
    private static final Pattern PLACEHOLDER_PATTERN = Pattern.compile("\\{([^}]+)\\}");
    
    /**
     * 根据命名规则生成文件名
     * 
     * @param tdd 考生投档表对象
     * @param rule 命名规则
     * @param customTemplate 自定义模板（当rule为CUSTOM时使用）
     * @param fileExtension 文件扩展名
     * @return 生成的文件名
     */
    public static String generateFileName(Tdd tdd, FileNamingRuleEnum rule, String customTemplate, String fileExtension) {
        String template;
        
        if (rule == FileNamingRuleEnum.CUSTOM && StringUtils.isNotBlank(customTemplate)) {
            template = customTemplate;
        } else {
            template = rule.getTemplate();
        }
        
        if (StringUtils.isBlank(template)) {
            template = FileNamingRuleEnum.KSH_XM_ZYMC.getTemplate();
        }
        
        String fileName = replacePlaceholders(template, tdd);
        if (StringUtils.isNotBlank(fileExtension)) {
            fileName += "." + fileExtension;
        }
        return cleanFileName(fileName);
    }
    
    /**
     * 根据命名规则代码生成文件名
     */
    public static String generateFileName(Tdd tdd, String ruleCode, String customTemplate, String fileExtension) {
        FileNamingRuleEnum rule = FileNamingRuleEnum.getByCode(ruleCode);
        return generateFileName(tdd, rule, customTemplate, fileExtension);
    }
    
    /**
     * 替换模板中的占位符
     */
    private static String replacePlaceholders(String template, Tdd tdd) {
        Map<String, String> placeholderMap = buildPlaceholderMap(tdd);
        
        Matcher matcher = PLACEHOLDER_PATTERN.matcher(template);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            String placeholder = matcher.group(1);
            String value = placeholderMap.getOrDefault(placeholder, "");
            /*if (StringUtils.isBlank(value)) {
                value = placeholder;
            }*/
            
            matcher.appendReplacement(result, Matcher.quoteReplacement(value));
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * 构建占位符映射
     */
    private static Map<String, String> buildPlaceholderMap(Tdd tdd) {
        Map<String, String> map = new HashMap<>();
        
        if (tdd != null) {
            map.put("ksh", StringUtils.defaultString(tdd.getKsh(), ""));
            map.put("xm", StringUtils.defaultString(tdd.getXm(), ""));
            map.put("zymc", StringUtils.defaultString(tdd.getZymc(), ""));
            map.put("xymc", StringUtils.defaultString(tdd.getXymc(), ""));
            map.put("bj", StringUtils.defaultString(tdd.getBj(), ""));
            map.put("sfzh", StringUtils.defaultString(tdd.getSfzh(), ""));
            map.put("xb", StringUtils.defaultString(tdd.getXbmc(), ""));
            map.put("mz", StringUtils.defaultString(tdd.getMzmc(), ""));
            map.put("klmc", StringUtils.defaultString(tdd.getKlmc(), ""));
            map.put("pcmc", StringUtils.defaultString(tdd.getPcmc(), ""));
            map.put("nf", StringUtils.defaultString(tdd.getNf(), ""));
            map.put("xz", StringUtils.defaultString(tdd.getXzbh(), ""));
            map.put("lqfs", StringUtils.defaultString(tdd.getLqfs(), ""));
        }
        
        return map;
    }
    
    /**
     * 清理文件名中的非法字符
     */
    private static String cleanFileName(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "unnamed";
        }
        
        // 替换Windows和Linux文件系统中的非法字符
        String cleaned = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
        
        // 移除连续的下划线
        cleaned = cleaned.replaceAll("_{2,}", "_");
        
        // 移除开头和结尾的下划线
        cleaned = cleaned.replaceAll("^_+|_+$", "");
        
        // 如果清理后为空，返回默认名称
        if (StringUtils.isBlank(cleaned)) {
            return "unnamed";
        }
        
        return cleaned;
    }
    
    /**
     * 获取所有可用的占位符
     */
    public static Map<String, String> getAvailablePlaceholders() {
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("ksh", "考生号");
        placeholders.put("xm", "姓名");
        placeholders.put("zymc", "专业名称");
        placeholders.put("xymc", "学院名称");
        placeholders.put("bj", "班级");
        placeholders.put("sfzh", "身份证号");
        placeholders.put("xb", "性别");
        placeholders.put("mz", "民族");
        placeholders.put("klmc", "科类名称");
        placeholders.put("pcmc", "批次名称");
        placeholders.put("nf", "年份");
        placeholders.put("xz", "学制");
        placeholders.put("lqfs", "录取方式");
        return placeholders;
    }
}
