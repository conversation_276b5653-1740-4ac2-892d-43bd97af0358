package com.sanyth.service.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.system.SytSysDict;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

@Service
public class SytSysDictService {

    @Autowired
    MongoTemplate mongoTemplate;

    public void save(SytSysDict dict) {
        dict.setId(UUID.randomUUID().toString().replaceAll("-",""));
        mongoTemplate.save(dict);
    }

    public void update(SytSysDict dict) {
        Update update = new Update()
                .set("code", dict.getCode())
                .set("name", dict.getName())
                .set("value", dict.getValue())
                .set("extend", dict.getExtend())
                .set("sortBy", dict.getSortBy())
                .set("crateTime", dict.getCrateTime())
                .set("attachment", dict.getAttachment())
                .set("remark", dict.getRemark());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("id").is(dict.getId())),
                update, SytSysDict.class);
    }

    public SytSysDict get(String idOrNameOrCode) {
        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("code").is(idOrNameOrCode),
                Criteria.where("name").is(idOrNameOrCode),
                Criteria.where("type").is(idOrNameOrCode));
        return mongoTemplate.findOne(new Query().addCriteria(criteria), SytSysDict.class);
    }


    public void delete(String... id) {
        if (id.length > 0) {
            List<String> list = Arrays.asList(id);
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(list));
            mongoTemplate.remove(query, SytSysDict.class);
        }
    }
    public List<SytSysDict> queryByVo(SytSysDict dict){
        Query q = getQuery(dict);
        return mongoTemplate.find(q, SytSysDict.class);
    }

    public List<SytSysDict> queryList(BaseQuery<SytSysDict> query){
        Query q = getQuery(query.getQueryParam());
        return mongoTemplate.find(q, SytSysDict.class);
    }

    public Page<SytSysDict> queryPage(BaseQuery<SytSysDict> query) {
        Query q = getQuery(query.getQueryParam());
        long count = mongoTemplate.count(q, SytSysDict.class);
        Pageable pageable = PageRequest.of(query.getPage() - 1,
                query.getPageSize(),
                Sort.by(Sort.Direction.ASC, "sortBy"));
        List<SytSysDict> list = mongoTemplate.find(q.with(pageable), SytSysDict.class);
        Page<SytSysDict> objectPage = new Page<>();
        objectPage.setRecords(list);
        objectPage.setTotal(count);
        objectPage.setCurrent(query.getPage());
        objectPage.setSize(query.getPageSize());
        return objectPage;
    }


    private Query getQuery(SytSysDict dict) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(dict.getId()))
            criteria.and("id").in(dict.getId().split(","));
        if (StringUtils.isNotBlank(dict.getCode()))
            criteria.and("code").in(dict.getCode().split(","));
        if (StringUtils.isNotBlank(dict.getName())) {
            Pattern pattern = Pattern.compile("^.*" + dict.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("name").regex(pattern);
        }
        query.addCriteria(criteria).with(Sort.by(Sort.Direction.ASC, "sortBy"));
        return query;
    }
}
