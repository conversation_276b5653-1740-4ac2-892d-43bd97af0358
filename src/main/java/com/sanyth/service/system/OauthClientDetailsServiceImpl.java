package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.mapper.system.OauthClientDetailsMapper;
import com.sanyth.model.system.OauthClientDetails;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 服务实现
 * Created by JIANGPING on 2020-04-07.
 */
@Service
@Transactional
public class OauthClientDetailsServiceImpl extends ServiceImpl<OauthClientDetailsMapper, OauthClientDetails> implements OauthClientDetailsService {
    @Override
    public Page<OauthClientDetails> queryPage(BaseQuery<OauthClientDetails> query) {
        Page<OauthClientDetails> page = new Page<>(query.getPage(), query.getPageSize());
        QueryWrapper<OauthClientDetails> wrapper = new QueryWrapper<>();
        wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    private QueryWrapper<OauthClientDetails> buildWrapper(OauthClientDetails clientDetails) {
        QueryWrapper<OauthClientDetails> wrapper = new QueryWrapper<>();
        if (clientDetails != null) {
            if (StringUtils.isNotBlank(clientDetails.getClientName()))
                wrapper.like("client_name", clientDetails.getClientName());
        }
        return wrapper;
    }
}
