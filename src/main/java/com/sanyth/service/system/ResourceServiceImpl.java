package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.mapper.system.ResourceMapper;
import com.sanyth.mapper.system.RoleResourceMapper;
import com.sanyth.model.system.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 *  服务实现
 * Created by JIANGPING on 2020-02-04.
 */
@Service
@Transactional
public class ResourceServiceImpl extends ServiceImpl<ResourceMapper, Resource> implements ResourceService {

    @javax.annotation.Resource
    ResourceMapper resourceMapper;
    @javax.annotation.Resource
    RoleResourceMapper resourceRoleMapper;

    @Transactional
    @Override
    public void delete(String... id) {
        QueryWrapper<Resource> wrapper = new QueryWrapper<>();
        wrapper.in("id", id);
        resourceMapper.delete(wrapper);
        for (String s : id) {
            removeById(s);
        }
    }

    @Override
    public List<Resource> getListByRole(String roleId) {
        return resourceMapper.getListByRole(roleId);
    }
    @Override
    public List<Resource> getButtonsListByRole(String roleId) {
        return resourceMapper.getButtonsListByRole(roleId);
    }

    @Override
    public List<Resource> getFrontListByRole(String roleId) {
        return resourceMapper.getFrontListByRole(roleId);
    }

    @Override
    public List<Resource> queryList(Resource resource) {
        return list(buildWrapper(resource));
    }

    @Override
    public Page<Resource> getAllChildren(String id, BaseQuery query) {
        Page<Resource> page = new Page<>(query.getPage(), query.getPageSize());
        Map<String, Object> params = new HashMap();
        params.put("id", id);
        page.setRecords(resourceMapper.getAllChildren(params, page));
        return page;
    }

    private Wrapper<Resource> buildWrapper(Resource resource) {
        QueryWrapper<Resource> wrapper = new QueryWrapper();
        if (resource.getType() != null)
            wrapper.eq("type", resource.getType());
        if (resource.getType() != null)
            wrapper.eq("is_btn", resource.getIsBtn());
        if (StringUtils.isNotBlank(resource.getParentId()))
            wrapper.eq("parent_id", resource.getParentId());
        if (StringUtils.isNotBlank(resource.getStatus()))
            wrapper.eq("status", resource.getStatus());
        wrapper.orderByAsc("sort");
        return wrapper;
    }
}
