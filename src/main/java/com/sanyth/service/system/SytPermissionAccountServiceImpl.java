package com.sanyth.service.system;

import cn.hutool.core.codec.Base64;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.*;
import com.sanyth.core.exception.BusinessException;
import com.sanyth.mapper.system.*;
import com.sanyth.model.system.*;
import com.sanyth.util.SM4EncoderAndDecoder;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by JIANGPING on 2020/5/15.
 */
@Service
public class SytPermissionAccountServiceImpl extends ServiceImpl<SytPermissionAccountMapper, SytPermissionAccount> implements ISytPermissionAccountService {

    @Resource
    SytPermissionAccountMapper sytPermissionAccountMapper;
    @Resource
    SytPermissionAccountRoleMapper sytPermissionAccountRoleMapper;
    @Resource
    SytSysOrganizationMapper sytSysOrganizationMapper;
    @Resource
    SytSysOrganizationUserMapper sytSysOrganizationUserMapper;
    @Resource
    SytPermissionRoleMapper sytPermissionRoleMapper;
    @Resource
    SytSysParamService paramService;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private ISytSysOrganizationService sytSysOrganizationService;
    @Value("${syt.sm4.telmobile.key}")
    private String telmobileKey;

    @Override
    public SytPermissionAccount getByHumancode(String humancode) {
        SytPermissionAccount account = new SytPermissionAccount();
        account.setHumancode(humancode);
        Wrapper<SytPermissionAccount> accountWrapper = buildWrapper(account);
        return getOne(accountWrapper);
    }

    @Override
    public List<SytPermissionAccount> queryList(SytPermissionAccount account) {
        return list(buildWrapper(account));
    }

    @Override
    public List<SytPermissionAccount> queryListByVo(SytPermissionAccount account) {
        List<SytPermissionAccount> list = list(buildWrapper(account));
        if (!CollectionUtils.isEmpty(list)) {
            list.forEach(acc->{
                List<SytPermissionRole> roles = sytPermissionRoleMapper.getByAccount(acc.getId());
                acc.setRoles(roles);
            });
        }
        return list;
    }

    @Override
    public Page<SytPermissionAccount> queryPage(BaseQuery<Map<String, Object>> query) {
        Page<SytPermissionAccount> page = new Page<>(query.getPage(), query.getPageSize());
        if (StringUtils.isNotBlank(telmobileKey)) {
            Map<String, Object> queryParam = query.getQueryParam();
            Object telmobile1 = queryParam.get("telmobile1");
            if (telmobile1 != null&&StringUtils.isNotBlank(telmobile1.toString())) {
                try {
                    queryParam.put("telmobile1", this.getCiphertextTelmobile((String) telmobile1));
                } catch (Exception e) {

                }

            }
        }
        List<SytPermissionAccount> list = sytPermissionAccountMapper.queryList(query.getQueryParam(),page);
        if (!CollectionUtils.isEmpty(list)) {
            for (SytPermissionAccount account : list) {
                try {
                    account.setTelmobile1(this.getCleartextTelmobile(account.getTelmobile1()));
                } catch (Exception e) {
                    account.setTelmobile1(account.getTelmobile1());
                }
                JSONArray roles = new JSONArray();
                List<SytPermissionRole> roleList = sytPermissionRoleMapper.getByAccount(account.getId());
                if (!CollectionUtils.isEmpty(roleList)) {
                    for (SytPermissionRole sytPermissionRole : roleList) {
                        JSONObject r = new JSONObject();
                        r.put("label", sytPermissionRole.getRolename());
                        r.put("value", sytPermissionRole.getId());
                        roles.add(r);
                    }
                }
                account.setRole(roles);

                // 组织关系
                QueryWrapper<SytSysOrganizationUser> param = new QueryWrapper<>();
                param.eq("USER_ID", account.getId());
                List<SytSysOrganizationUser> organizationUsers = sytSysOrganizationUserMapper.selectList(param);
                JSONArray org = new JSONArray();
                if (!CollectionUtils.isEmpty(organizationUsers)) {
                    for (SytSysOrganizationUser organizationUser : organizationUsers) {
                        org.add(organizationUser.getOrganizationId());
                    }
                }
                account.setOrganization(org);
            }
        }
        page.setRecords(list);
        return page;
    }

    @Transactional
    @Override
    public void edit(JSONObject params) throws Exception {
        JSONObject accountJson = params.getJSONObject("account");
        JSONArray roleJson = params.getJSONArray("role");
        JSONArray organizationJson = params.getJSONArray("organization");
        if (accountJson == null || roleJson == null || organizationJson == null) {
            throw new BusinessException(ErrorInfo.MSG_0003);
        }

        SytPermissionAccount account = JSON.toJavaObject(accountJson, SytPermissionAccount.class);

        StringBuilder organizationBuider = new StringBuilder();
        StringBuilder orgshortnameBuider = new StringBuilder();
       /* Wrapper<SytSysOrganization> organizationWrapper = new EntityWrapper<>();
        organizationWrapper.isNull("parent");
        List<SytSysOrganization> organizations = sytSysOrganizationMapper.list(organizationWrapper);
        if (!CollectionUtils.isEmpty(organizations)) {
            organizationBuider.append(organizations.get(0).getOrgname()).append(";");
            if (StringUtils.isNotBlank(organizations.get(0).getOrgshortname())) {
                orgshortnameBuider.append(organizations.get(0).getOrgshortname()).append(";");
            }
        }*/

        List<SytSysOrganizationUser> organizationUsers = new ArrayList<>();
        for (int i = 0; i < organizationJson.size(); i++) {
            Object obj = organizationJson.get(i);
            String o = (String) obj;
            SytSysOrganization organization = sytSysOrganizationMapper.selectById(o);
            if (i == 0 && organization.getParent() != null) {
                SytSysOrganization parent = sytSysOrganizationMapper.selectById(organization.getParent());
                organizationBuider.append(parent.getOrgname()).append(";");
                orgshortnameBuider.append(parent.getOrgshortname()).append(";");
            }
            organizationBuider.append(organization.getOrgname()).append(",");
            orgshortnameBuider.append(organization.getOrgshortname()).append(",");
            SytSysOrganizationUser ou = new SytSysOrganizationUser();
            ou.setOrganizationId(o);
            organizationUsers.add(ou);
        }
        account.setOrganizationnames(organizationBuider.deleteCharAt(organizationBuider.length() - 1).toString());
        account.setOrgshortname(orgshortnameBuider.deleteCharAt(orgshortnameBuider.length() - 1).toString());

        SytPermissionAccount accountParam = new SytPermissionAccount();
        accountParam.setHumancode(account.getHumancode());
        List<SytPermissionAccount> list = list(buildWrapper(accountParam));// 当前登录账号是否存在

        if (StringUtils.isBlank(account.getId())) {
            if (!CollectionUtils.isEmpty(list))
                throw new BusinessException(ErrorInfo.MSG_0004);
            String pass = passwordHandler(account);
            account.setHumanpassword(DigestUtils.md5DigestAsHex(pass.getBytes()));
            account.setCreatedate(new Date());
            save(account);
        } else {
            if (!CollectionUtils.isEmpty(list) && !list.get(0).getHumancode().equals(account.getHumancode()))
                throw new BusinessException(ErrorInfo.MSG_0004);

            SytPermissionAccount accountIndb = getById(account.getId());
            accountIndb.setHumancode(accountJson.getString("humancode"));
            accountIndb.setHumanname(accountJson.getString("humanname"));
            accountIndb.setTelmobile1(accountJson.getString("telmobile1"));
            accountIndb.setDutyid(accountJson.getString("dutyid"));
            accountIndb.setIdtype(accountJson.getString("idtype"));
            accountIndb.setIdcode(accountJson.getString("idcode"));
            accountIndb.setTeloffice(accountJson.getString("teloffice"));
            accountIndb.setTelhome(accountJson.getString("telhome"));
            accountIndb.setTelmobile2(accountJson.getString("telmobile2"));
            if (StringUtils.isNotEmpty(accountJson.getString("birthday")))
                accountIndb.setBirthday(new SimpleDateFormat("yyyy-MM-dd").parse(accountJson.getString("birthday")));
            accountIndb.setEmail(accountJson.getString("email"));
            accountIndb.setPostalcode(accountJson.getString("postalcode"));
            accountIndb.setValidflag(accountJson.getDouble("validflag"));
            accountIndb.setDisplayorder(accountJson.getDouble("displayorder"));
            accountIndb.setSex(accountJson.getString("sex"));
            accountIndb.setAddress(accountJson.getString("address"));
            accountIndb.setValidfromdate(accountJson.getDate("validfromdate"));
            accountIndb.setValidtodate(accountJson.getDate("validtodate"));
            accountIndb.setEmployeeType(accountJson.getString("employeeType"));

//            accountIndb.setOrganizationnames(organizationBuider.deleteCharAt(organizationBuider.length() - 1).toString());
            accountIndb.setOrganizationnames(organizationBuider.toString().trim());
            accountIndb.setOrgshortname(orgshortnameBuider.toString().trim());
            updateById(accountIndb);
            sytPermissionAccountRoleMapper.delete(new QueryWrapper<SytPermissionAccountRole>().eq("ACCOUNT_ID", accountIndb.getId()));
            sytSysOrganizationUserMapper.delete(new QueryWrapper<SytSysOrganizationUser>().eq("USER_ID", accountIndb.getId()));
        }

        for (int i = 0; i < roleJson.size(); i++) {
            String roleid = (String) roleJson.get(i);
            SytPermissionAccountRole accountRole = new SytPermissionAccountRole();
            accountRole.setAccountId(account.getId());
            accountRole.setRoleId(roleid);
            sytPermissionAccountRoleMapper.insert(accountRole);
        }

        for (SytSysOrganizationUser ou : organizationUsers) {
            ou.setUserId(account.getId());
            sytSysOrganizationUserMapper.insert(ou);
        }

        //更新数据权限
        sytSysOrganizationService.updateDataScope(account);
    }

    @Transactional
    @Override
    public void delete(String... id) throws Exception {
        if (id != null && id.length > 0) {
            sytPermissionAccountRoleMapper.delete(new QueryWrapper<SytPermissionAccountRole>().in("ACCOUNT_ID", id));
            sytSysOrganizationUserMapper.delete(new QueryWrapper<SytSysOrganizationUser>().in("USER_ID", id));
            removeByIds(Arrays.asList(id));
        } else {
            throw new BusinessException(ErrorInfo.MSG_0003);
        }
    }

    private Wrapper<SytPermissionAccount> buildWrapper(SytPermissionAccount account) {
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(account.getHumancode()))
            wrapper.eq("humancode", account.getHumancode());
        if (StringUtils.isNotBlank(account.getHumanname()))
            wrapper.like("humanname", account.getHumanname());
        if (StringUtils.isNotBlank(account.getIdcode()))
            wrapper.like("idcode", account.getIdcode());
        if (StringUtils.isNotBlank(account.getEmployeeType()))
            wrapper.eq("employeeType", account.getEmployeeType());
        if (StringUtils.isNotBlank(account.getTelmobile1()))
            wrapper.eq("telmobile1", account.getTelmobile1());
        if (StringUtils.isNotBlank(account.getHumanpassword()))
            wrapper.eq("humanpassword", account.getHumanpassword());
        return wrapper;
    }

    @Transactional
    @Override
    public Resp updateInfo(SytPermissionAccount account) {
        if (StringUtils.isBlank(account.getId())) {
            return Resp.error("id不能为空");
        }
        SytPermissionAccount accountIndb = getById(account.getId());
        if(accountIndb == null){
            return Resp.error("用户不存在");
        }
        if (StringUtils.isNotBlank(account.getHumanname())) {
            accountIndb.setHumanname(account.getHumanname());
        }
        accountIndb.setSex(account.getSex());
        accountIndb.setTelmobile1(account.getTelmobile1());
        accountIndb.setEmail(account.getEmail());
        accountIndb.setIdtype(account.getIdtype());
        accountIndb.setIdcode(account.getIdcode());
        accountIndb.setAddress(account.getAddress());
        updateById(accountIndb);
        return Resp.success();
    }


    /**
     * ldap md5加密
     * @param psw
     * @return
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     */
    @SneakyThrows
    public static String LdapEncoderByMd5(String psw) {
        MessageDigest md5= MessageDigest.getInstance("MD5");
        String md5psw= Base64.encode(md5.digest(psw.getBytes("utf-8")));
        return "{MD5}"+ md5psw;
    }

    @Transactional
    @Override
    public Resp changePasswd(String oldpassword, String newpassword, String newpasswords, CurrentUser currentUser) {
        if(StringUtils.isNotBlank(oldpassword) && StringUtils.isNotBlank(newpassword) && StringUtils.isNotBlank(newpasswords)){
            if(!newpassword.equals(newpasswords)){
                return Resp.error("两次新密码输入不一致");
            }
            if (!ToolsUtil.checkPasswordStrength("", newpassword)&&!ToolsUtil.checkPasswordStrength("", newpasswords)) {
                return Resp.error("密码强度不符");
            }
            String humancode = currentUser.getHumancode();
            SytPermissionAccount account = new SytPermissionAccount();
            account.setHumancode(humancode);
            account.setHumanpassword(DigestUtils.md5DigestAsHex(oldpassword.getBytes()));
            List<SytPermissionAccount> list = queryList(account);
            if(list != null && list.size() > 0){
                SytPermissionAccount currentAccount = list.get(0);
                currentAccount.setHumanpassword(DigestUtils.md5DigestAsHex(newpassword.getBytes()));
                updateById(currentAccount);
                return Resp.success();
            }else{
                return Resp.error(currentUser.getHumancode()+"用户原始密码不正确");
            }
        }
        return Resp.error("请完整填写密码");
    }

    @Transactional
    @Override
    public Resp resetPassword(Map<String, String> param) {
        String type = param.get("verifyType"); // 区分是手机验证，还是邮箱验证
        if(StringUtils.isBlank(type)){
            return Resp.error("参数type不能为空");
        }
        String humancode = param.get("humancode");
        if(StringUtils.isBlank(humancode)){
            return Resp.error("账号不能为空");
        }
        String verifyCode = param.get("verifyCode");
        if(StringUtils.isBlank(verifyCode)){
            return Resp.error("验证码不能为空");
        }

        SytPermissionAccount account = new SytPermissionAccount();
        account.setHumancode(humancode);
        List<SytPermissionAccount> list = queryList(account);
        if(list != null && list.size() > 0){
            SytPermissionAccount accountIndb = list.get(0);
            String phone = accountIndb.getTelmobile1();
            String email = accountIndb.getEmail();
            String code = "";
//            Map tmp = redisTemplate.opsForHash().entries("FIND_PASSWORD_INFO");
            String key = "FIND_PASSWORD_INFO" + "_" + humancode + "_";
            if("phone".equals(type)){
                key += phone;
            }else if("email".equals(type)){
                key += email;
            }

            // 获取验证码
            Object str = redisTemplate.opsForHash().get(key, "code");
            if(str != null){
                code = String.valueOf(str);
            }
            if(StringUtils.isBlank(code)){
                return Resp.error("验证码已过期");
            }
            if(!verifyCode.equals(code)){
                return Resp.error("验证码无效");
            }
            String newpassword = param.get("newpassword");
            String newpasswords = param.get("newpasswords");
            if(StringUtils.isNotBlank(newpassword) && StringUtils.isNotBlank(newpasswords)){
                if(!newpassword.equals(newpasswords)){
                    return Resp.error("两次新密码输入不一致");
                }
                if (!ToolsUtil.checkPasswordStrength("", newpassword)&&!ToolsUtil.checkPasswordStrength("", newpasswords)) {
                    return Resp.error("密码强度不符");
                }
                accountIndb.setHumancode(humancode);
                accountIndb.setHumanpassword(DigestUtils.md5DigestAsHex(newpassword.getBytes()));
                updateById(accountIndb);
                // 删除验证码
                redisTemplate.opsForHash().delete(key, "code");
                return Resp.success();
            }
        }
        return Resp.error("请完整填写密码");
    }

    @Transactional
    @Override
    public Resp resetPasswordByAdmin(JSONObject param) {
        String humancode = param.getString("humancode");
        SytPermissionAccount account = this.getByHumancode(humancode);
        if (account == null) {
            return Resp.error("未找到用户,操作失败");
        }
        String password = passwordHandler(account);
        account.setHumanpassword(DigestUtils.md5DigestAsHex(password.getBytes()));
        updateById(account);
        return Resp.success();
    }

    @Override
    public String getCleartextTelmobile(String telmobile) {
        return SM4EncoderAndDecoder.decryptBase64(telmobile, "gbk", telmobileKey);
    }

    @Override
    public String getCiphertextTelmobile(String telmobile) throws InvalidKeyException {
        return SM4EncoderAndDecoder.encryptBase64(telmobile, "gbk", telmobileKey);
    }

    /**
     * 用户密码统一生成规则，适用于添加用户、导入用户、重置用户密码，生成按优先级排序
     * 1.参数表默认密码+身份证后六位
     * 2.参数表默认密码+用户账户名
     * 3.身份证后六位
     * 4.用户账户名
     * @param account
     * @return
     */
    public String passwordHandler(SytPermissionAccount account){
        SytSysParam sysParam = paramService.get(Constants.DEFAULT_PASSWORD);
        String pass = "";
        if (sysParam != null) {
            pass = sysParam.getValue();
            // 有身份证号，则参数表值加上身份证后六位；没有身份证号的，则加上用户账号
            if (StringUtils.isNotEmpty(account.getIdcode())&&account.getIdcode().length() >= 6) {
                pass += account.getIdcode().substring(account.getIdcode().length() - 6);
            } else {
                pass += account.getHumancode();
            }
        } else if (StringUtils.isNotEmpty(account.getIdcode())&&account.getIdcode().length() >= 6) {
            pass = account.getIdcode().substring(account.getIdcode().length() - 6);
        } else {
            pass = account.getHumancode();
        }
        return pass;
    }
}
