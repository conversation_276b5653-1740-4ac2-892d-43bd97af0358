
package com.sanyth.service.system;

import com.sanyth.model.system.CodeDqb;
import com.sanyth.vo.CodeDqbVO;
import com.sanyth.mapper.system.CodeDqbMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务实现类
 *
 * <AUTHOR> @since 2024-02-20
 */
@Service
public class CodeDqbServiceImpl extends ServiceImpl<CodeDqbMapper, CodeDqb> implements ICodeDqbService {

	@Override
	public IPage<CodeDqbVO> selectCodeDqbPage(IPage<CodeDqbVO> page, CodeDqbVO codeDqb) {
		return page.setRecords(baseMapper.selectCodeDqbPage(page, codeDqb));
	}

}
