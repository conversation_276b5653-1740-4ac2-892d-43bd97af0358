
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.CodeZslx;
import com.sanyth.vo.CodeZslxVO;

/**
 * 招生类型管理 服务类
 *
 * <AUTHOR> @since 2024-03-06
 */
public interface ICodeZslxService extends IService<CodeZslx> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param codeZslx
	 * @return
	 */
	IPage<CodeZslxVO> selectCodeZslxPage(IPage<CodeZslxVO> page, CodeZslxVO codeZslx);

}
