
package com.sanyth.service.system;

import com.sanyth.model.system.CodeDqb;
import com.sanyth.vo.CodeDqbVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务类
 *
 * <AUTHOR> @since 2024-02-20
 */
public interface ICodeDqbService extends IService<CodeDqb> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param codeDqb
	 * @return
	 */
	IPage<CodeDqbVO> selectCodeDqbPage(IPage<CodeDqbVO> page, CodeDqbVO codeDqb);

}
