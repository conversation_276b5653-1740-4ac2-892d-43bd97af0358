package com.sanyth.service.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.system.OauthClientDetails;

/**
 *  服务接口
 * Created by JIANGPING on 2020-04-07.
 */
public interface OauthClientDetailsService extends IService<OauthClientDetails> {
    Page<OauthClientDetails> queryPage(BaseQuery<OauthClientDetails> query);
}
