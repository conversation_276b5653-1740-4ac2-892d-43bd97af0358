package com.sanyth.service.system;

import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.Constants;
import com.sanyth.core.common.CurrentUser;
import com.sanyth.core.enums.DataScopeType;
import com.sanyth.mapper.system.SytSysOrganizationMapper;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.model.system.SytPermissionRole;
import com.sanyth.model.system.SytSysOrganization;
import com.sanyth.util.DataScopeUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 组织机构表 服务实现
 * Created by JIANGPING on 2020-05-14.
 */
@Service("sytSysOrganizationService")
public class SytSysOrganizationServiceImpl extends ServiceImpl<SytSysOrganizationMapper, SytSysOrganization> implements ISytSysOrganizationService {

    @Resource
    SytSysOrganizationMapper sytSysOrganizationMapper;
    @Resource
    private ISytPermissionAccountService sytPermissionAccountService;
    @Resource
    private ISytPermissionRoleService sytPermissionRoleService;
    @Resource
    RedisTemplate redisTemplate;

    @Override
    @Async
    public Map<String, Set<String>> updateDataScope(SytPermissionAccount account) {
        Map<String, Set<String>> result = new HashMap<>();
        if (account != null && StringUtils.isNotBlank(account.getId())) {
//            account.setRoles(sytPermissionRoleService.getByAccount(account.getId()));
            List<SytPermissionRole> roles = sytPermissionRoleService.getByAccount(account.getId());
            Map<String, Set<String>> entries = redisTemplate.opsForHash().entries(Constants.DATA_SCOPE_INFO);
            for (int i = 0; i < roles.size(); i++) {
                SytPermissionRole role = roles.get(i);
                SytPermissionAccount permissionAccount = new SytPermissionAccount();
                permissionAccount.setId(account.getId());
                permissionAccount.setHumancode(account.getHumancode());
                List<SytPermissionRole> roleList = new ArrayList<>();
                roleList.add(role);
                permissionAccount.setRoles(roleList);
                Set<String> calcScope = new HashSet<>();
                DataScopeUtil.calcScope(permissionAccount, calcScope);
                if (MapUtil.isNotEmpty(entries)) {
                    entries.put(account.getId() + "_" + role.getId(), calcScope);
                    result = entries;
                } else {
                    result.put(account.getId() + "_" + role.getId(), calcScope);
                }
            }

        } else {
            List<SytPermissionAccount> accountList = sytPermissionAccountService.queryListByVo(account);
            for (int i = 0; i < accountList.size(); i++) {
                SytPermissionAccount acc = accountList.get(i);
                List<SytPermissionRole> roles = acc.getRoles();
                for (int l = 0; l < roles.size(); l++) {
                    SytPermissionRole role = roles.get(l);
                    SytPermissionAccount permissionAccount = new SytPermissionAccount();
                    permissionAccount.setId(acc.getId());
                    permissionAccount.setHumancode(acc.getHumancode());
                    List<SytPermissionRole> roleList = new ArrayList<>();
                    roleList.add(role);
                    permissionAccount.setRoles(roleList);
                    Set<String> calcScope = new HashSet<>();
                    DataScopeUtil.calcScope(permissionAccount, calcScope);
                    result.put(permissionAccount.getId() + "_" + role.getId(), calcScope);
                }

            }
        }
        redisTemplate.opsForHash().putAll(Constants.DATA_SCOPE_INFO, result);
        return result;
    }

    @Override
    public List<SytSysOrganization> getOrganizationByUser(String userId) {
        return sytSysOrganizationMapper.getOrganizationByUser(userId);
    }

    @Override
    public List<SytSysOrganization> queryList(SytSysOrganization organization,CurrentUser user) {
        QueryWrapper<SytSysOrganization> queryWrapper = buildWapper(organization);
        if (DataScopeType.OWN_AND_CHILD_DEPT.getType().equals(user.getRoletype()) || DataScopeType.OWN_ONLY_DEPT.getType().equals(user.getRoletype())) {
            List<SytSysOrganization> organizations = user.getOrganizations();
            List<String> orgIds = organizations.stream().map(SytSysOrganization::getId).collect(Collectors.toList());
            organization.setId(StringUtils.join(orgIds, ","));
            queryWrapper.in("id", organization.getId().split(","));
        }
        return list(queryWrapper);
    }

    @Override
    public List<SytSysOrganization> queryParentList() {
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        wrapper.isNull("parent").or().eq("parent", "");
        return list(wrapper);
    }

    @Override
    public Page<SytSysOrganization> queryPage(BaseQuery<SytSysOrganization> query) {
        Page<SytSysOrganization> page = new Page<>(query.getPage(), query.getPageSize());
        return page(page, buildWapper(query.getQueryParam()));
    }

    private QueryWrapper buildWapper(SytSysOrganization organization){
        QueryWrapper<SytSysOrganization> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(organization.getCode()))
            wrapper.eq("code", organization.getCode());
        if(StringUtils.isNotBlank(organization.getValid()))
            wrapper.eq("valid", organization.getValid());
        if(StringUtils.isNotBlank(organization.getCategoryId()))
            wrapper.eq("categoryId", organization.getCategoryId());
        if(StringUtils.isNotBlank(organization.getParent()) && !"search".equals(organization.getParent()))
            wrapper.eq("parent", organization.getParent());
        if(StringUtils.isNotBlank(organization.getOrgname()))
            wrapper.like("orgname", organization.getOrgname());
        if(StringUtils.isNotBlank(organization.getLabel()))
            wrapper.like("orgname", organization.getLabel());
        wrapper.orderByAsc("displayorder");
        return wrapper;
    }
}
