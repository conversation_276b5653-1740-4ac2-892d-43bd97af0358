package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.DownloadTask;
import com.sanyth.vo.DownloadTaskVO;

import java.util.List;
import java.util.Map;

/**
 * 下载任务表 服务类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
public interface IDownloadTaskService extends IService<DownloadTask> {

    /**
     * 自定义分页
     *
     * @param page 分页对象
     * @param downloadTask 查询条件
     * @return 分页结果
     */
    IPage<DownloadTaskVO> selectDownloadTaskPage(IPage<DownloadTaskVO> page, DownloadTaskVO downloadTask);

    /**
     * 创建下载任务
     *
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param taskDesc 任务描述
     * @param taskParams 任务参数
     * @param createUserId 创建用户ID
     * @param createUserName 创建用户名
     * @param expireHours 过期小时数，默认24小时
     * @return 任务ID
     */
    String createDownloadTask(String taskName, String taskType, String taskDesc, 
                            String taskParams, String createUserId, String createUserName, Integer expireHours);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 状态
     * @param errorMsg 错误信息
     * @return 是否成功
     */
    boolean updateTaskStatus(String taskId, String status, String errorMsg);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param progress 进度
     * @return 是否成功
     */
    boolean updateTaskProgress(String taskId, Integer progress);

    /**
     * 设置任务完成
     *
     * @param taskId 任务ID
     * @param filePath 文件路径
     * @param fileName 文件名称
     * @param fileSize 文件大小
     * @param fileId 文件ID
     * @return 是否成功
     */
    boolean completeTask(String taskId, String filePath, String fileName, Long fileSize, String fileId);

    /**
     * 获取待处理的任务
     *
     * @return 待处理任务列表
     */
    List<DownloadTask> getPendingTasks();

    /**
     * 处理任务队列
     */
    void processTaskQueue();

    /**
     * 清理过期任务
     */
    void cleanExpiredTasks();

    /**
     * 获取用户任务列表
     *
     * @param userId 用户ID
     * @return 任务列表
     */
    List<DownloadTaskVO> getUserTasks(String userId);

    /**
     * 检查用户是否可以创建新任务
     *
     * @param userId 用户ID
     * @return 是否可以创建
     */
    boolean canCreateTask(String userId);

    /**
     * 获取任务统计信息
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics(String userId);

    /**
     * 重试失败的任务
     *
     * @param taskId 任务ID
     * @return 是否成功
     */
    boolean retryTask(String taskId);

    /**
     * 根据任务类型和参数执行具体的下载任务
     *
     * @param task 任务对象
     */
    void executeTask(DownloadTask task);
} 