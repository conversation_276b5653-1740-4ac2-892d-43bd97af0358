
package com.sanyth.service.system;

import com.sanyth.model.system.CodeNjb;
import com.sanyth.vo.CodeNjbVO;
import com.sanyth.mapper.system.CodeNjbMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务实现类
 *
 * <AUTHOR> @since 2024-02-20
 */
@Service
public class CodeNjbServiceImpl extends ServiceImpl<CodeNjbMapper, CodeNjb> implements ICodeNjbService {

	@Override
	public IPage<CodeNjbVO> selectCodeNjbPage(IPage<CodeNjbVO> page, CodeNjbVO codeNjb) {
		return page.setRecords(baseMapper.selectCodeNjbPage(page, codeNjb));
	}

}
