package com.sanyth.service.system;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.system.SytJustAuthConfig;

import java.util.List;

public interface SytJustAuthConfigService extends IService<SytJustAuthConfig> {

    List<SytJustAuthConfig> queryList(SytJustAuthConfig onlineUser);

    Page<SytJustAuthConfig> queryPage(BaseQuery<SytJustAuthConfig> query);

    void delete(String... id) throws Exception;

    SytJustAuthConfig getByAuthType(String authType);

    void edit(JSONObject params);
}
