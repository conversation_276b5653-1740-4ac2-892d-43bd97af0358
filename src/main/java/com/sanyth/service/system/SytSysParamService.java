package com.sanyth.service.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.system.SytSysParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * Created by JIANGPING on 2020/5/15.
 */
@Service
public class SytSysParamService {

    @Autowired
    MongoTemplate mongoTemplate;

    public void save(SytSysParam sytSysParam) {
        sytSysParam.setId(UUID.randomUUID().toString().replaceAll("-",""));
        mongoTemplate.save(sytSysParam);
    }

    public void update(SytSysParam param) {
        Update update = new Update()
                .set("img", param.getImg())
                .set("name", param.getName())
                .set("value", param.getValue())
                .set("type", param.getType())
                .set("bz", param.getBz());
        mongoTemplate.upsert(new Query().addCriteria(Criteria.where("id").is(param.getId())),
                update, SytSysParam.class);
    }

    public SytSysParam get(String idOrNameOrType) {
        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("id").is(idOrNameOrType),
                Criteria.where("name").is(idOrNameOrType),
                Criteria.where("type").is(idOrNameOrType));
        return mongoTemplate.findOne(new Query().addCriteria(criteria), SytSysParam.class);
    }
    public String getValue(String idOrNameOrType) {
        Criteria criteria = new Criteria();
        criteria.orOperator(
                Criteria.where("id").is(idOrNameOrType),
                Criteria.where("name").is(idOrNameOrType),
                Criteria.where("type").is(idOrNameOrType));
        SytSysParam one = mongoTemplate.findOne(new Query().addCriteria(criteria), SytSysParam.class);
        if (one != null) {
            return one.getValue();
        }
        return null;
    }


    public void delete(String... id) {
        if (id.length > 0) {
            List<String> list = Arrays.asList(id);
            Query query = new Query();
            query.addCriteria(Criteria.where("id").in(list));
            mongoTemplate.remove(query, SytSysParam.class);
        }
    }
    public List<SytSysParam> queryList(BaseQuery<SytSysParam> query){
        Query q = getQuery(query.getQueryParam());
        return mongoTemplate.find(q, SytSysParam.class);
    }

    public Page<SytSysParam> queryPage(BaseQuery<SytSysParam> query) {
//        SytSysParam sysParam = JSON.toJavaObject(query.getQueryParam(), SytSysParam.class);
        Query q = getQuery(query.getQueryParam());
        long count = mongoTemplate.count(q, SytSysParam.class);
        Pageable pageable = PageRequest.of(query.getPage() - 1,
                query.getPageSize(),
                Sort.by(Sort.Direction.DESC, "id"));
        List<SytSysParam> list = mongoTemplate.find(q.with(pageable), SytSysParam.class);
        Page<SytSysParam> objectPage = new Page<>();
        objectPage.setRecords(list);
        objectPage.setTotal(count);
        objectPage.setCurrent(query.getPage());
        objectPage.setSize(query.getPageSize());
        return objectPage;
    }


    private Query getQuery(SytSysParam param) {
        Query query = new Query();
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(param.getId()))
            criteria.and("id").in(param.getId().split(","));
        if (StringUtils.isNotBlank(param.getType()))
            criteria.and("type").in(param.getType().split(","));
        if (StringUtils.isNotBlank(param.getName())) {
            Pattern pattern = Pattern.compile("^.*" + param.getName() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria.and("name").regex(pattern);
        }
        query.addCriteria(criteria);
        return query;
    }
}
