package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.mapper.system.ResourceLinkMapper;
import com.sanyth.model.system.ResourceLink;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional
public class ResourceLinkServiceImpl extends ServiceImpl<ResourceLinkMapper, ResourceLink> implements ResourceLinkService {

    @Override
    public Page<ResourceLink> queryPage(BaseQuery<ResourceLink> query) {
        Page<ResourceLink> page = new Page<>(query.getPage(), query.getPageSize());
        return page(page, buildWapper(query.getQueryParam()));
    }

    @Override
    public List<ResourceLink> getParentList() {
        QueryWrapper<ResourceLink> wrapper = new QueryWrapper<>();
        wrapper.isNull("parent_id");
        List<ResourceLink> resourceLinks = list(wrapper);
        return resourceLinks;
    }

    private QueryWrapper buildWapper(ResourceLink resourceLink){
        QueryWrapper<ResourceLink> wrapper = new QueryWrapper<>();
        if(StringUtils.isNotBlank(resourceLink.getParentId()))
            wrapper.eq("parent_id", resourceLink.getParentId());
        return wrapper;
    }
}
