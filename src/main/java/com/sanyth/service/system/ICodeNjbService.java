
package com.sanyth.service.system;

import com.sanyth.model.system.CodeNjb;
import com.sanyth.vo.CodeNjbVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务类
 *
 * <AUTHOR> @since 2024-02-20
 */
public interface ICodeNjbService extends IService<CodeNjb> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param codeNjb
	 * @return
	 */
	IPage<CodeNjbVO> selectCodeNjbPage(IPage<CodeNjbVO> page, CodeNjbVO codeNjb);

}
