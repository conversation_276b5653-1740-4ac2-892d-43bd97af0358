
package com.sanyth.service.system;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.CustomCol;
import com.sanyth.vo.CustomColVO;

/**
 * 自定义列 服务类
 *
 * <AUTHOR> @since 2024-02-28
 */
public interface ICustomColService extends IService<CustomCol> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param customCol
	 * @return
	 */
	IPage<CustomColVO> selectCustomColPage(IPage<CustomColVO> page, CustomColVO customCol);

	void deleteCustomColData(String type);
	void saveCustomColData(JSONObject param);

}
