
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.TemplateField;
import com.sanyth.vo.TemplateFieldVO;

/**
 * 模板字段表 服务类
 *
 * <AUTHOR> @since 2024-04-10
 */
public interface ITemplateFieldService extends IService<TemplateField> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param templateField
	 * @return
	 */
	IPage<TemplateFieldVO> selectTemplateFieldPage(IPage<TemplateFieldVO> page, TemplateFieldVO templateField);

}
