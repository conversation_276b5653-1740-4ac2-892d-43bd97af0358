
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.CodeZybMapper;
import com.sanyth.model.system.CodeZyb;
import com.sanyth.vo.CodeZybVO;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR> @since 2024-02-16
 */
@Service
public class CodeZybServiceImpl extends ServiceImpl<CodeZybMapper, CodeZyb> implements ICodeZybService {

	@Override
	public IPage<CodeZybVO> selectCodeZybPage(IPage<CodeZybVO> page, CodeZybVO codeZyb) {
		return page.setRecords(baseMapper.selectCodeZybPage(page, codeZyb));
	}

}
