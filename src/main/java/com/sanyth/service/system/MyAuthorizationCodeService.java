package com.sanyth.service.system;

import org.springframework.security.oauth2.common.util.RandomValueStringGenerator;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.code.JdbcAuthorizationCodeServices;

import javax.sql.DataSource;

/**
 * Created by JIANGPING on 2020/6/6.
 */
public class MyAuthorizationCodeService extends JdbcAuthorizationCodeServices {

    private RandomValueStringGenerator generator = new RandomValueStringGenerator(12);

    public MyAuthorizationCodeService(DataSource dataSource) {
        super(dataSource);
    }

    @Override
    public String createAuthorizationCode(OAuth2Authentication authentication) {
        String code = generator.generate();
        store(code, authentication);
        return code;
    }
}
