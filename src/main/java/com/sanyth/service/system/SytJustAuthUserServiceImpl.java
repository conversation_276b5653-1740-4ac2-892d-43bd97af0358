package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.ErrorInfo;
import com.sanyth.core.exception.BusinessException;
import com.sanyth.mapper.system.SytJustAuthUserMapper;
import com.sanyth.model.system.SytJustAuthUser;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Service
public class SytJustAuthUserServiceImpl extends ServiceImpl<SytJustAuthUserMapper, SytJustAuthUser> implements SytJustAuthUserService {

    @Resource
    SytJustAuthUserMapper sytJustAuthUserMapper;

    @Override
    public List<SytJustAuthUser> queryList(SytJustAuthUser onlineUser) {
        return list(buildWrapper(onlineUser));
    }

    @Override
    public Page<SytJustAuthUser> queryPage(BaseQuery<SytJustAuthUser> query) {
        Page<SytJustAuthUser> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytJustAuthUser> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    @Override
    public void delete(String... id) throws Exception {
        if (id != null && id.length > 0) {
            removeByIds(Arrays.asList(id));
        } else {
            throw new BusinessException(ErrorInfo.MSG_0003);
        }
    }

    @Override
    public List<SytJustAuthUser> findByHumanCode(String humanCode) {
        QueryWrapper<SytJustAuthUser> wrapper = new QueryWrapper<>();
        wrapper.eq("humancode", humanCode);
        return list(wrapper);
    }

    @Override
    public SytJustAuthUser getByUserIdAndOauthType(String userId, String oauthType) {
        SytJustAuthUser account = new SytJustAuthUser();
        account.setUserId(userId);
        account.setAuthType(oauthType);
        Wrapper<SytJustAuthUser> accountWrapper = buildWrapper(account);
        return getOne(accountWrapper);
    }

    @Override
    public SytJustAuthUser getByHumanCodeAndOauthType(String humanCode, String oauthType) {
        SytJustAuthUser account = new SytJustAuthUser();
        account.setHumancode(humanCode);
        account.setAuthType(oauthType);
        Wrapper<SytJustAuthUser> accountWrapper = buildWrapper(account);
        return getOne(accountWrapper);
    }

    private Wrapper<SytJustAuthUser> buildWrapper(SytJustAuthUser onlineUser) {
        QueryWrapper<SytJustAuthUser> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(onlineUser.getHumancode()))
            wrapper.eq("humancode", onlineUser.getHumancode());
        if (StringUtils.isNotBlank(onlineUser.getUserId()))
            wrapper.eq("userId", onlineUser.getUserId());
        if (StringUtils.isNotBlank(onlineUser.getAuthType()))
            wrapper.eq("authType", onlineUser.getAuthType());
        return wrapper;
    }
}
