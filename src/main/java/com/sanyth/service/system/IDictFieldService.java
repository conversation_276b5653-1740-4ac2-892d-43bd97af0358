
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.DictField;
import com.sanyth.vo.DictFieldVO;

/**
 * 字典字段 服务类
 *
 * <AUTHOR> @since 2024-02-23
 */
public interface IDictFieldService extends IService<DictField> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param dictField
	 * @return
	 */
	IPage<DictFieldVO> selectDictFieldPage(IPage<DictFieldVO> page, DictFieldVO dictField);

}
