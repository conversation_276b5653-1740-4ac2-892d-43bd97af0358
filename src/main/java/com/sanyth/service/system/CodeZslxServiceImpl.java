
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.CodeZslxMapper;
import com.sanyth.model.system.CodeZslx;
import com.sanyth.vo.CodeZslxVO;
import org.springframework.stereotype.Service;

/**
 * 招生类型管理 服务实现类
 *
 * <AUTHOR> @since 2024-03-06
 */
@Service
public class CodeZslxServiceImpl extends ServiceImpl<CodeZslxMapper, CodeZslx> implements ICodeZslxService {

	@Override
	public IPage<CodeZslxVO> selectCodeZslxPage(IPage<CodeZslxVO> page, CodeZslxVO codeZslx) {
		return page.setRecords(baseMapper.selectCodeZslxPage(page, codeZslx));
	}

}
