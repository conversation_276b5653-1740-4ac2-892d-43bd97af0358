package com.sanyth.service.system;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.CurrentUser;
import com.sanyth.core.common.Resp;
import com.sanyth.model.system.SytPermissionAccount;

import java.security.InvalidKeyException;
import java.util.List;
import java.util.Map;

/**
 * 用户信息表 服务接口
 * Created by JIANGPING on 2020-05-14.
 */
public interface ISytPermissionAccountService extends IService<SytPermissionAccount> {

    SytPermissionAccount getByHumancode(String humancode);

    List<SytPermissionAccount> queryList(SytPermissionAccount account);

    List<SytPermissionAccount> queryListByVo(SytPermissionAccount account);

    Page<SytPermissionAccount> queryPage(BaseQuery<Map<String, Object>> query);

    void edit(JSONObject params) throws Exception;

    void delete(String... id) throws Exception;

    Resp updateInfo(SytPermissionAccount account);

    Resp changePasswd(String oldpassword, String newpassword, String newpasswords, CurrentUser currentUser);

    Resp resetPassword(Map<String, String> param);

    Resp resetPasswordByAdmin(JSONObject param);

    String getCleartextTelmobile(String telmobile);
    String getCiphertextTelmobile(String telmobile) throws InvalidKeyException;
}
