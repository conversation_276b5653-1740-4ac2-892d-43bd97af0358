package com.sanyth.service.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.system.SytJustAuthUser;

import java.util.List;

public interface SytJustAuthUserService extends IService<SytJustAuthUser> {

    List<SytJustAuthUser> queryList(SytJustAuthUser onlineUser);

    Page<SytJustAuthUser> queryPage(BaseQuery<SytJustAuthUser> query);

    void delete(String... id) throws Exception;

    List<SytJustAuthUser> findByHumanCode(String humanCode);

    SytJustAuthUser getByUserIdAndOauthType(String userId, String oauthType);

    SytJustAuthUser getByHumanCodeAndOauthType(String humanCode, String oauthType);
}
