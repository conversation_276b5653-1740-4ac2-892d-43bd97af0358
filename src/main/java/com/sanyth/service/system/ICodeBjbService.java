
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.CodeBjb;
import com.sanyth.vo.CodeBjbVO;

/**
 *  服务类
 *
 * <AUTHOR> @since 2024-02-16
 */
public interface ICodeBjbService extends IService<CodeBjb> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param codeBjb
	 * @return
	 */
	IPage<CodeBjbVO> selectCodeBjbPage(IPage<CodeBjbVO> page, CodeBjbVO codeBjb);

}
