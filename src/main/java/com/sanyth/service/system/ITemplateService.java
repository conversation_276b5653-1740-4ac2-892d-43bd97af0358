
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.Template;
import com.sanyth.vo.TemplateVO;

/**
 * 模板表 服务类
 *
 * <AUTHOR> @since 2024-04-10
 */
public interface ITemplateService extends IService<Template> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param template
	 * @return
	 */
	IPage<TemplateVO> selectTemplatePage(IPage<TemplateVO> page, TemplateVO template);

}
