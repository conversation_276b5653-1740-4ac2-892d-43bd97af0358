package com.sanyth.service.system;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.FileOperationUtil;
import com.sanyth.core.enums.DownloadTaskStatusEnum;
import com.sanyth.core.enums.DownloadTaskTypeEnum;
import com.sanyth.core.strategy.ExportStrategy;
import com.sanyth.core.strategy.ExportStrategyRegistry;
import com.sanyth.mapper.system.DownloadTaskMapper;
import com.sanyth.model.system.DownloadTask;
import com.sanyth.vo.DownloadTaskVO;
import com.sanyth.vo.ExportParams;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 下载任务表 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-25
 */
@Slf4j
@Service
public class DownloadTaskServiceImpl extends ServiceImpl<DownloadTaskMapper, DownloadTask> implements IDownloadTaskService {

    @Autowired
    private FileOperationUtil fileOperationUtil;

    @Autowired
    private ExportStrategyRegistry exportStrategyRegistry;

    private static final int MAX_RUNNING_TASKS_PER_USER = 3; // 每个用户最多同时执行3个任务
    private static final int MAX_PENDING_TASKS_PER_USER = 10; // 每个用户最多10个待处理任务
    private static final int DEFAULT_EXPIRE_HOURS = 720; // 默认一个月过期(30天*24小时)

    @Override
    public IPage<DownloadTaskVO> selectDownloadTaskPage(IPage<DownloadTaskVO> page, DownloadTaskVO downloadTask) {
        List<DownloadTaskVO> records = baseMapper.selectDownloadTaskPage(page, downloadTask);
        
        // 设置扩展字段
        records.forEach(this::setExtendedFields);
        
        page.setRecords(records);
        return page;
    }

    @Override
    @Transactional
    public String createDownloadTask(String taskName, String taskType, String taskDesc, 
                                   String taskParams, String createUserId, String createUserName, Integer expireHours) {
        // 检查用户是否可以创建新任务
        if (!canCreateTask(createUserId)) {
            throw new RuntimeException("您的待处理任务已达上限，请等待当前任务完成后再试");
        }

        // 创建任务
        DownloadTask task = new DownloadTask();
        task.setId(IdUtil.simpleUUID());
        task.setTaskName(taskName);
        task.setTaskType(taskType);
        task.setTaskStatus(DownloadTaskStatusEnum.PENDING.getCode());
        task.setTaskDesc(taskDesc);
        task.setTaskParams(taskParams);
        task.setCreateUserId(createUserId);
        task.setCreateUserName(createUserName);
        task.setCreateTime(new Date());
        task.setProgress(0);
        task.setRetryCount(0);
        task.setMaxRetryCount(3);
        // 设置过期时间
        int hours = expireHours != null ? expireHours : DEFAULT_EXPIRE_HOURS;
        task.setExpireTime(DateUtil.offsetHour(new Date(), hours));
        save(task);
        log.info("创建下载任务: taskId={}, taskName={}, taskType={}, userId={}",task.getId(), taskName, taskType, createUserId);
        processTaskAsync(task);
        return task.getId();
    }

    @Override
    @Transactional
    public boolean updateTaskStatus(String taskId, String status, String errorMsg) {
        UpdateWrapper<DownloadTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", taskId)
                    .set("task_status", status);
        
        // 只有当errorMsg不为null时才设置error_msg字段
        if (errorMsg != null) {
            updateWrapper.set("error_msg", errorMsg);
        }
        
        if (DownloadTaskStatusEnum.PROCESSING.getCode().equals(status)) {
            updateWrapper.set("start_time", new Date());
        } else if (DownloadTaskStatusEnum.COMPLETED.getCode().equals(status) || 
                   DownloadTaskStatusEnum.FAILED.getCode().equals(status)) {
            updateWrapper.set("finish_time", new Date());
        }

        return update(updateWrapper);
    }

    @Override
    public boolean updateTaskProgress(String taskId, Integer progress) {
        UpdateWrapper<DownloadTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", taskId).set("progress", progress);
        return update(updateWrapper);
    }

    @Override
    @Transactional
    public boolean completeTask(String taskId, String filePath, String fileName, Long fileSize, String fileId) {
        UpdateWrapper<DownloadTask> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", taskId)
                    .set("task_status", DownloadTaskStatusEnum.COMPLETED.getCode())
                    .set("file_path", filePath)
                    .set("file_name", fileName)
                    .set("file_size", fileSize)
                    .set("file_id", fileId)
                    .set("finish_time", new Date())
                    .set("progress", 100);

        boolean result = update(updateWrapper);
        
        if (result) {
            log.info("任务完成: taskId={}, fileName={}, fileSize={}", taskId, fileName, fileSize);
        }
        
        return result;
    }

    @Override
    public List<DownloadTask> getPendingTasks() {
        return baseMapper.selectPendingTasks();
    }

    @Override
    public void processTaskQueue() {
        List<DownloadTask> pendingTasks = getPendingTasks();
        
        for (DownloadTask task : pendingTasks) {
            processTaskAsync(task);
        }
    }

    @Async
    public void processTaskAsync(DownloadTask task) {
        CompletableFuture.runAsync(() -> {
            try {
                // 开始处理任务时，清空错误信息
                updateTaskStatus(task.getId(), DownloadTaskStatusEnum.PROCESSING.getCode(), "");
                executeTask(task);
            } catch (Exception e) {
                log.error("处理任务异常: taskId={}, error={}", task.getId(), e.getMessage(), e);
                task.setRetryCount(task.getRetryCount() + 1);
                if (task.getRetryCount() >= task.getMaxRetryCount()) {
                    // 超过最大重试次数，标记为失败
                    updateTaskStatus(task.getId(), DownloadTaskStatusEnum.FAILED.getCode(), e.getMessage());
                } else {
                    // 重新加入队列
                    updateTaskStatus(task.getId(), DownloadTaskStatusEnum.PENDING.getCode(), e.getMessage());
                }
            }
        });
    }

    @Override
    public void executeTask(DownloadTask task) {
        try {
            String taskType = task.getTaskType();
            String taskParams = task.getTaskParams();
            
            log.info("开始执行任务: taskId={}, taskType={}", task.getId(), taskType);
            
            if (!exportStrategyRegistry.supports(taskType)) {
                throw new RuntimeException("不支持的任务类型: " + taskType);
            }
            
            ExportStrategy strategy = exportStrategyRegistry.getStrategy(taskType);
            JSONObject params = JSON.parseObject(taskParams);
            ExportParams exportParams = new ExportParams();
            exportParams.setBusinessType(taskType);
            exportParams.setExportType(params.getString("exportType"));
            exportParams.setFileName(task.getTaskName());
            exportParams.setQueryParams(params.get("queryParams"));
            exportParams.setCreateUserId(task.getCreateUserId());
            exportParams.setCreateUserName(task.getCreateUserName());
            
            updateTaskProgress(task.getId(), 20);
            Map<String, Object> result = strategy.export(exportParams, exportParams.getExportType());
            updateTaskProgress(task.getId(), 80);
            
            if (result != null && Boolean.TRUE.equals(result.get("success"))) {
                String fileName = (String) result.get("fileName");
                String fileId = (String) result.get("fileId");
                String filePath = (String) result.get("filePath");
                Long fileSize = (Long) result.get("fileSize");
                completeTask(task.getId(), filePath, fileName, fileSize, fileId);
            } else {
                String error = result != null ? (String) result.get("error") : "导出失败";
                throw new RuntimeException(error);
            }
            
        } catch (Exception e) {
            log.error("执行任务失败: taskId={}, error={}", task.getId(), e.getMessage(), e);
        }
    }



    @Override
    public void cleanExpiredTasks() {
        List<DownloadTask> expiredTasks = baseMapper.selectExpiredTasks();
        
        for (DownloadTask task : expiredTasks) {
            try {
                if (StrUtil.isNotBlank(task.getFileId())) {
                    fileOperationUtil.remove(task.getFileId());
                }
                updateTaskStatus(task.getId(), DownloadTaskStatusEnum.EXPIRED.getCode(), "任务已过期");
                log.info("清理过期任务: taskId={}, taskName={}", task.getId(), task.getTaskName());
            } catch (Exception e) {
                log.error("清理过期任务异常: taskId={}, error={}", task.getId(), e.getMessage(), e);
            }
        }
    }

    @Override
    public List<DownloadTaskVO> getUserTasks(String userId) {
        List<DownloadTaskVO> tasks = baseMapper.selectTasksByUserId(userId);
        tasks.forEach(this::setExtendedFields);
        return tasks;
    }

    @Override
    public boolean canCreateTask(String userId) {
        int pendingCount = baseMapper.countTasksByUserIdAndStatus(userId, DownloadTaskStatusEnum.PENDING.getCode());
        int processingCount = baseMapper.countTasksByUserIdAndStatus(userId, DownloadTaskStatusEnum.PROCESSING.getCode());
        return (pendingCount + processingCount) < MAX_PENDING_TASKS_PER_USER;
    }

    @Override
    public Map<String, Object> getTaskStatistics(String userId) {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("pending", baseMapper.countTasksByUserIdAndStatus(userId, DownloadTaskStatusEnum.PENDING.getCode()));
        statistics.put("processing", baseMapper.countTasksByUserIdAndStatus(userId, DownloadTaskStatusEnum.PROCESSING.getCode()));
        statistics.put("completed", baseMapper.countTasksByUserIdAndStatus(userId, DownloadTaskStatusEnum.COMPLETED.getCode()));
        statistics.put("failed", baseMapper.countTasksByUserIdAndStatus(userId, DownloadTaskStatusEnum.FAILED.getCode()));
        statistics.put("expired", baseMapper.countTasksByUserIdAndStatus(userId, DownloadTaskStatusEnum.EXPIRED.getCode()));
        
        return statistics;
    }

    @Override
    public boolean retryTask(String taskId) {
        DownloadTask task = getById(taskId);
        if (task == null) {
            return false;
        }
        if (!DownloadTaskStatusEnum.FAILED.getCode().equals(task.getTaskStatus())) {
            return false;
        }
        task.setTaskStatus(DownloadTaskStatusEnum.PENDING.getCode());
        task.setErrorMsg(""); // 清空错误信息，使用空字符串而不是null
        task.setProgress(0);
        boolean result = updateById(task);
        if (result) {
            processTaskAsync(task);
        }
        return result;
    }

    private void setExtendedFields(DownloadTaskVO vo) {
        vo.setTaskStatusDesc(DownloadTaskStatusEnum.getEnumDesc(vo.getTaskStatus()));
        vo.setTaskTypeDesc(DownloadTaskTypeEnum.getEnumDesc(vo.getTaskType()));
        if (vo.getFileSize() != null) {
            vo.setFileSizeFormat(formatFileSize(vo.getFileSize()));
        }
        vo.setCanDownload(DownloadTaskStatusEnum.COMPLETED.getCode().equals(vo.getTaskStatus())
                         && vo.getExpireTime() != null 
                         && vo.getExpireTime().after(new Date()));
        vo.setIsExpired(vo.getExpireTime() != null && vo.getExpireTime().before(new Date()));
        if (vo.getExpireTime() != null && vo.getExpireTime().after(new Date())) {
            long remainingMillis = vo.getExpireTime().getTime() - System.currentTimeMillis();
            vo.setRemainingMinutes(remainingMillis / (60 * 1000));
        }
    }
    private String formatFileSize(Long fileSize) {
        if (fileSize == null || fileSize == 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.2f %s", size, units[unitIndex]);
    }
} 