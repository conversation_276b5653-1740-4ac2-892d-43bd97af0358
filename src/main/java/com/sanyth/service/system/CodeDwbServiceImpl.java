
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.CodeDwbMapper;
import com.sanyth.model.system.CodeDwb;
import com.sanyth.vo.CodeDwbVO;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR> @since 2024-02-16
 */
@Service
public class CodeDwbServiceImpl extends ServiceImpl<CodeDwbMapper, CodeDwb> implements ICodeDwbService {

	@Override
	public IPage<CodeDwbVO> selectCodeDwbPage(IPage<CodeDwbVO> page, CodeDwbVO codeDwb) {
		return page.setRecords(baseMapper.selectCodeDwbPage(page, codeDwb));
	}

}
