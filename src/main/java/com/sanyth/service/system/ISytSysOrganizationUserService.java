package com.sanyth.service.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.system.SytSysOrganizationUser;

/**
 * ${table.comment} 服务接口
 * Created by JIANGPING on 2020-05-15.
 */
public interface ISytSysOrganizationUserService extends IService<SytSysOrganizationUser> {
        Page<SytSysOrganizationUser> queryPage(BaseQuery<SytSysOrganizationUser> query);
}
