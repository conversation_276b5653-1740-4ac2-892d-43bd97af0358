
package com.sanyth.service.system;

import com.sanyth.model.system.CodeXqb;
import com.sanyth.vo.CodeXqbVO;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务类
 *
 * <AUTHOR> @since 2024-02-20
 */
public interface ICodeXqbService extends IService<CodeXqb> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param codeXqb
	 * @return
	 */
	IPage<CodeXqbVO> selectCodeXqbPage(IPage<CodeXqbVO> page, CodeXqbVO codeXqb);

}
