
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.TemplateFieldMapper;
import com.sanyth.model.system.TemplateField;
import com.sanyth.vo.TemplateFieldVO;
import org.springframework.stereotype.Service;

/**
 * 模板字段表 服务实现类
 *
 * <AUTHOR> @since 2024-04-10
 */
@Service
public class TemplateFieldServiceImpl extends ServiceImpl<TemplateFieldMapper, TemplateField> implements ITemplateFieldService {

	@Override
	public IPage<TemplateFieldVO> selectTemplateFieldPage(IPage<TemplateFieldVO> page, TemplateFieldVO templateField) {
		return page.setRecords(baseMapper.selectTemplateFieldPage(page, templateField));
	}

}
