package com.sanyth.service.system;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.exception.ValidateException;
import com.sanyth.mapper.system.SytPermissionAccountMapper;
import com.sanyth.mapper.system.SytPermissionRoleMapper;
import com.sanyth.mapper.system.SytSysOrganizationMapper;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.model.system.SytPermissionRole;
import com.sanyth.model.system.SytSysOrganization;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
public class MyUserDetailsService extends ServiceImpl<SytPermissionAccountMapper, SytPermissionAccount> implements UserDetailsService, IService<SytPermissionAccount> {

    @Resource
    private SytPermissionRoleMapper sytPermissionRoleMapper;
    @Resource
    private SytPermissionAccountMapper sytPermissionAccountMapper;
    @Resource
    private SytSysOrganizationMapper sysOrganizationMapper;

    @Override
    public UserDetails loadUserByUsername(String userName) throws AuthenticationException {
        QueryWrapper<SytPermissionAccount> wrapper = new QueryWrapper<>();
        wrapper.eq("humancode", userName).or().eq("email", userName)
                .or().eq("telmobile1", userName).or().eq("idcode", userName);
        SytPermissionAccount account = sytPermissionAccountMapper.selectOne(wrapper);
        if (null != account) {
            if (account.getValidflag() == 1) {
                throw new ValidateException("登录失败, 当前账户已被锁定");
            }

            if (!checkAccountExpired(account)) {
                throw new ValidateException("登录失败, 当前账户已过期");
            }
            List<SytPermissionRole> roles = sytPermissionRoleMapper.getByAccount(account.getId());
            if (!CollectionUtils.isEmpty(roles)) {
                roles = Arrays.asList(roles.get(0));    // 多角色时默认取第一个, 进入系统再切换角色
            }
            account.setRoles(roles);
            List<SytSysOrganization> organizations = sysOrganizationMapper.getOrganizationByUser(account.getId());
            account.setOrganizationList(organizations);
            return account;
        }
        throw new ValidateException("用户名或密码错误");
    }

    private boolean checkAccountExpired(SytPermissionAccount account) {
        long now = new Date().getTime();
        if (account.getValidfromdate() != null && account.getValidtodate() != null) {
            return (now >= account.getValidfromdate().getTime() && now < account.getValidtodate().getTime()) ? true : false;
        } else if (account.getValidfromdate() != null) {
            return now >= account.getValidfromdate().getTime() ? true : false;
        } else if (account.getValidtodate() != null) {
            return now < account.getValidtodate().getTime() ? true : false;
        }
        return true;
    }
}
