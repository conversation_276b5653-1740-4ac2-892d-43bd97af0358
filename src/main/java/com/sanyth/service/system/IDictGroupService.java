
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.DictGroup;
import com.sanyth.vo.DictGroupVO;

import java.util.List;

/**
 * 字典组 服务类
 *
 * <AUTHOR> @since 2024-02-23
 */
public interface IDictGroupService extends IService<DictGroup> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param dictGroup
	 * @return
	 */
	IPage<DictGroupVO> selectDictGroupPage(IPage<DictGroupVO> page, DictGroupVO dictGroup);

	List<DictGroup> selectDictGroupList(DictGroup dictGroup);

}
