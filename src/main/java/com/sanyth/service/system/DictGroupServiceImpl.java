
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.DictGroupMapper;
import com.sanyth.model.system.DictGroup;
import com.sanyth.vo.DictGroupVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 字典组 服务实现类
 *
 * <AUTHOR> @since 2024-02-23
 */
@Service
public class DictGroupServiceImpl extends ServiceImpl<DictGroupMapper, DictGroup> implements IDictGroupService {

	@Override
	public IPage<DictGroupVO> selectDictGroupPage(IPage<DictGroupVO> page, DictGroupVO dictGroup) {
		return page.setRecords(baseMapper.selectDictGroupPage(page, dictGroup));
	}

	@Override
	public List<DictGroup> selectDictGroupList(DictGroup dictGroup) {
		return baseMapper.selectDictGroupList(dictGroup);
	}

}
