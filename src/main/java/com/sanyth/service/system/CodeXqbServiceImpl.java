
package com.sanyth.service.system;

import com.sanyth.model.system.CodeXqb;
import com.sanyth.vo.CodeXqbVO;
import com.sanyth.mapper.system.CodeXqbMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务实现类
 *
 * <AUTHOR> @since 2024-02-20
 */
@Service
public class CodeXqbServiceImpl extends ServiceImpl<CodeXqbMapper, CodeXqb> implements ICodeXqbService {

	@Override
	public IPage<CodeXqbVO> selectCodeXqbPage(IPage<CodeXqbVO> page, CodeXqbVO codeXqb) {
		return page.setRecords(baseMapper.selectCodeXqbPage(page, codeXqb));
	}

}
