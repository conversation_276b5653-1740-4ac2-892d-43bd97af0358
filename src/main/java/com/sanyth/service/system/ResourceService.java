package com.sanyth.service.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.system.Resource;

import java.util.List;

/**
 *  服务接口
 * Created by JIANGPING on 2020-02-04.
 */
public interface ResourceService extends IService<Resource> {
    void delete(String...id);

    List<Resource> getListByRole(String roleId);
    List<Resource> getButtonsListByRole(String roleId);
    List<Resource> getFrontListByRole(String roleId);

    List<Resource> queryList(Resource resource);

    Page<Resource> getAllChildren(String id, BaseQuery query);
}
