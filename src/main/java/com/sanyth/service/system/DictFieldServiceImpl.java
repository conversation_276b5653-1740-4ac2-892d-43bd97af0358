
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.DictFieldMapper;
import com.sanyth.model.system.DictField;
import com.sanyth.vo.DictFieldVO;
import org.springframework.stereotype.Service;

/**
 * 字典字段 服务实现类
 *
 * <AUTHOR> @since 2024-02-23
 */
@Service
public class DictFieldServiceImpl extends ServiceImpl<DictFieldMapper, DictField> implements IDictFieldService {

	@Override
	public IPage<DictFieldVO> selectDictFieldPage(IPage<DictFieldVO> page, DictFieldVO dictField) {
		return page.setRecords(baseMapper.selectDictFieldPage(page, dictField));
	}

}
