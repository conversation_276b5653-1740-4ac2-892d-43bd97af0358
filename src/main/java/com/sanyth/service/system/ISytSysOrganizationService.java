package com.sanyth.service.system;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.CurrentUser;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.model.system.SytSysOrganization;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 组织机构表 服务接口
 * Created by JIANGPING on 2020-05-14.
 */
public interface ISytSysOrganizationService extends IService<SytSysOrganization> {
    List<SytSysOrganization> getOrganizationByUser(String userId);
    List<SytSysOrganization> queryList(SytSysOrganization organization, CurrentUser user);
    List<SytSysOrganization> queryParentList();
    Page<SytSysOrganization> queryPage(BaseQuery<SytSysOrganization> query);
    /**
     * 更新用户数据权限范围
     *
     * @param account 账户（可作单个用户或查询多个用户）
     * @return {@link Map}<{@link String}, {@link Set}<{@link String}>>
     */
    Map<String, Set<String>> updateDataScope(SytPermissionAccount account);

}
