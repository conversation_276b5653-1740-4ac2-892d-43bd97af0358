package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.mapper.system.SytSysOrganizationUserMapper;
import com.sanyth.model.system.SytSysOrganizationUser;
import org.springframework.stereotype.Service;

/**
 * ${table.comment} 服务实现
 * Created by JIANGPING on 2020-05-15.
 */
@Service
public class SytSysOrganizationUserServiceImpl extends ServiceImpl<SytSysOrganizationUserMapper, SytSysOrganizationUser> implements ISytSysOrganizationUserService {

    @Override
    public Page<SytSysOrganizationUser> queryPage(BaseQuery<SytSysOrganizationUser> query) {
        Page<SytSysOrganizationUser> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<SytSysOrganizationUser> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    private Wrapper<SytSysOrganizationUser> buildWrapper(SytSysOrganizationUser sytSysOrganizationUser) {
        QueryWrapper<SytSysOrganizationUser> wrapper = new QueryWrapper<>();
        // Query condition...

        return wrapper;
    }
}
