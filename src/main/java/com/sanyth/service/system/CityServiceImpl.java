package com.sanyth.service.system;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.CodeCityMapper;
import com.sanyth.mapper.system.CodeProvinceMapper;
import com.sanyth.mapper.system.CodeTownMapper;
import com.sanyth.model.system.CodeCity;
import com.sanyth.model.system.CodeProvince;
import com.sanyth.model.system.CodeTown;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: TODO
 **/
@Service
@AllArgsConstructor
public class CityServiceImpl extends ServiceImpl<CodeProvinceMapper, CodeProvince> implements CityService {

    private CodeProvinceMapper codeProvinceMapper;
    private CodeCityMapper codeCityMapper;
    private CodeTownMapper codeTownMapper;

    @Override
    public List<CodeProvince> findProvince() {
        return codeProvinceMapper.findProvinceAll();
    }

    @Override
    public List<CodeCity> findCity() {
        return codeCityMapper.findCityAll();
    }

    @Override
    public List<CodeTown> findTown() {
        return codeTownMapper.findTownAll();
    }

    @Override
    public List<CodeCity> findCityByProvince(String provinceCode) {
        return baseMapper.findCityByProvince(provinceCode);
    }

    @Override
    public List<CodeTown> findTownByCity(String cityCode) {
        return baseMapper.findTownByCity(cityCode);
    }
}
