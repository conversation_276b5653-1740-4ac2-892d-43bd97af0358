
package com.sanyth.service.system;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.CustomColMapper;
import com.sanyth.model.system.CustomCol;
import com.sanyth.vo.CustomColVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Map;

/**
 * 自定义列 服务实现类
 *
 * <AUTHOR> @since 2024-02-28
 */
@Service
public class CustomColServiceImpl extends ServiceImpl<CustomColMapper, CustomCol> implements ICustomColService {

	@Resource
	CustomColMapper customColMapper;

	@Override
	public IPage<CustomColVO> selectCustomColPage(IPage<CustomColVO> page, CustomColVO customCol) {
		return page.setRecords(baseMapper.selectCustomColPage(page, customCol));
	}

	@Override
	public void deleteCustomColData(String type) {
		customColMapper.delete(new LambdaQueryWrapper<CustomCol>().eq(CustomCol::getType, type));
	}

	@Override
	public void saveCustomColData(JSONObject param) {
		String type = param.getString("type");
		JSONArray columnData = param.getJSONArray("columnData");
		// 删除原有的数据
		this.deleteCustomColData(type);
		// 保存数据
		if (ArrayUtil.isNotEmpty(columnData) && columnData.size() > 0) {
			ArrayList<CustomCol> list = new ArrayList<>();
			columnData.forEach(item -> {
				Map<String, Object> obj = (Map) item;
				CustomCol customColumn = new CustomCol();
				customColumn.setType(type);
				customColumn.setFielden(String.valueOf(obj.get("fielden")));
				customColumn.setFieldzh(String.valueOf(obj.get("fieldzh")));
				customColumn.setType(String.valueOf(obj.get("type")));
				customColumn.setSort(Integer.parseInt(obj.get("sort").toString()));
				customColumn.setSfss(String.valueOf(obj.get("sfss")));
				customColumn.setFieldType(String.valueOf(obj.get("fieldType")));
				customColumn.setFieldAttribute(String.valueOf(obj.get("fieldAttribute")));
				customColumn.setFieldData(String.valueOf(obj.get("fieldData")));
				list.add(customColumn);
			});
			this.saveBatch(list);
		}
	}

}
