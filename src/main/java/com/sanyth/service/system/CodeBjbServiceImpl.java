
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.CodeBjbMapper;
import com.sanyth.model.system.CodeBjb;
import com.sanyth.vo.CodeBjbVO;
import org.springframework.stereotype.Service;

/**
 *  服务实现类
 *
 * <AUTHOR> @since 2024-02-16
 */
@Service
public class CodeBjbServiceImpl extends ServiceImpl<CodeBjbMapper, CodeBjb> implements ICodeBjbService {

	@Override
	public IPage<CodeBjbVO> selectCodeBjbPage(IPage<CodeBjbVO> page, CodeBjbVO codeBjb) {
		return page.setRecords(baseMapper.selectCodeBjbPage(page, codeBjb));
	}

}
