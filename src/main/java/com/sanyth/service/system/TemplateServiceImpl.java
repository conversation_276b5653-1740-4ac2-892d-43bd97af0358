
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.TemplateMapper;
import com.sanyth.model.system.Template;
import com.sanyth.vo.TemplateVO;
import org.springframework.stereotype.Service;

/**
 * 模板表 服务实现类
 *
 * <AUTHOR> @since 2024-04-10
 */
@Service
public class TemplateServiceImpl extends ServiceImpl<TemplateMapper, Template> implements ITemplateService {

	@Override
	public IPage<TemplateVO> selectTemplatePage(IPage<TemplateVO> page, TemplateVO template) {
		return page.setRecords(baseMapper.selectTemplatePage(page, template));
	}

}
