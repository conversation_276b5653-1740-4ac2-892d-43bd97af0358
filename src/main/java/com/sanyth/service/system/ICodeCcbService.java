
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.CodeCcb;
import com.sanyth.vo.CodeCcbVO;

/**
 * 层次表 服务类
 *
 * <AUTHOR> @since 2025-06-12
 */
public interface ICodeCcbService extends IService<CodeCcb> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param codeCcb
	 * @return
	 */
	IPage<CodeCcbVO> selectCodeCcbPage(IPage<CodeCcbVO> page, CodeCcbVO codeCcb);

}
