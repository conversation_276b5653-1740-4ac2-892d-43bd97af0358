package com.sanyth.service.system;


import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.model.system.CodeCity;
import com.sanyth.model.system.CodeProvince;
import com.sanyth.model.system.CodeTown;

import java.util.List;

/**
 * @ClassName: CityService
 * @Description: TODO
 **/
public interface CityService extends IService<CodeProvince> {

    List<CodeProvince> findProvince();
    List<CodeCity> findCity();
    List<CodeTown> findTown();
    List<CodeCity> findCityByProvince(String provinceCode);

    List<CodeTown> findTownByCity(String cityCode);
}
