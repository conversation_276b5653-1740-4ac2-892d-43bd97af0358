
package com.sanyth.service.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.system.CodeCcbMapper;
import com.sanyth.model.system.CodeCcb;
import com.sanyth.vo.CodeCcbVO;
import org.springframework.stereotype.Service;

/**
 * 层次表 服务实现类
 *
 * <AUTHOR> @since 2025-06-12
 */
@Service
public class CodeCcbServiceImpl extends ServiceImpl<CodeCcbMapper, CodeCcb> implements ICodeCcbService {

	@Override
	public IPage<CodeCcbVO> selectCodeCcbPage(IPage<CodeCcbVO> page, CodeCcbVO codeCcb) {
		return page.setRecords(baseMapper.selectCodeCcbPage(page, codeCcb));
	}

}
