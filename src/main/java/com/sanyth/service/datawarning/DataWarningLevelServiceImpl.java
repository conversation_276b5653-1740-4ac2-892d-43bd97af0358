package com.sanyth.service.datawarning;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.ErrorInfo;
import com.sanyth.core.common.Resp;
import com.sanyth.mapper.datawarning.DataWarningLevelMapper;
import com.sanyth.model.datawarning.DataWarningLevel;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 预警级别
 * <AUTHOR>
 * @since 2022-03-02
 */
@Service
public class DataWarningLevelServiceImpl extends ServiceImpl<DataWarningLevelMapper, DataWarningLevel> implements DataWarningLevelService {

    @Override
    public Page<DataWarningLevel> queryPage(BaseQuery<DataWarningLevel> query) {
        Page<DataWarningLevel> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataWarningLevel> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    @Override
    public Resp editLevel(DataWarningLevel entity) {
        boolean b = this.saveOrUpdate(entity);
        if(b){
            return Resp.success();
        }
        return Resp.error(ErrorInfo.CODE_MSG_00001);
    }

    @Override
    public Resp delete(String id) {
        if(StringUtils.isNotBlank(id)){
            String[] idArr = id.split(",");
            if (idArr != null && idArr.length > 0) {
                removeByIds(Arrays.asList(idArr));
                return Resp.success();
            }
        }
        return Resp.error(ErrorInfo.MSG_0003);
    }

    @Override
    public String getLevelByValue(String ruleId, String judgeValue) {
        if(StringUtils.isNotBlank(ruleId) && StringUtils.isNotBlank(judgeValue)){
            DataWarningLevel dataWarningLevel = new DataWarningLevel();
            dataWarningLevel.setRuleId(ruleId);
            Wrapper<DataWarningLevel> wrapper = buildWrapper(dataWarningLevel);
            List<DataWarningLevel> list = this.list(wrapper);
            for (DataWarningLevel warningLevel : list) {
                BigDecimal decimalJudge = new BigDecimal(judgeValue);
                BigDecimal decimalMin = new BigDecimal(String.valueOf(warningLevel.getMinVal()));
                BigDecimal decimalMax = new BigDecimal(String.valueOf(warningLevel.getMaxVal()));
                if(decimalJudge.compareTo(decimalMin) >= 0 && decimalJudge.compareTo(decimalMax) < 0){
                    return warningLevel.getLevelName();
                }
            }
        }
        return "";
    }

    @Override
    public List<DataWarningLevel> getList(DataWarningLevel query) {
        Wrapper<DataWarningLevel> wrapper = buildWrapper(query);
        return this.list(wrapper);
    }

    private Wrapper<DataWarningLevel> buildWrapper(DataWarningLevel dto) {
        QueryWrapper<DataWarningLevel> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(dto.getId()))
            wrapper.eq("id", dto.getId());
        if (StringUtils.isNotBlank(dto.getRuleId()))
            wrapper.eq("ruleId", dto.getRuleId());
        if (StringUtils.isNotBlank(dto.getLevelName()))
            wrapper.like("levelName", dto.getLevelName());
        wrapper.orderByAsc("RULEID").orderByDesc("sort");
        return wrapper;
    }
}
