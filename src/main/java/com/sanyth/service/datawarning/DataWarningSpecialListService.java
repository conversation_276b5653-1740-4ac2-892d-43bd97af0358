package com.sanyth.service.datawarning;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.Resp;
import com.sanyth.model.datawarning.DataWarningSpecialList;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 特殊名单(预警白名单)
 * <AUTHOR>
 * @since 2022-04-26
 */
public interface DataWarningSpecialListService extends IService<DataWarningSpecialList> {

    IPage<DataWarningSpecialList> queryPage(BaseQuery<DataWarningSpecialList> query);

    Resp delete(String... id);

    List<DataWarningSpecialList> getList(DataWarningSpecialList query);

    DataWarningSpecialList findOne(JSONObject params);

    void importData(MultipartFile file, String ruleid, HttpServletResponse response);

    List<String> getHumancodeList(String ruleid);
}
