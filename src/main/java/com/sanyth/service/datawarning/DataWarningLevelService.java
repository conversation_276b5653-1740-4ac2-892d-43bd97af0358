package com.sanyth.service.datawarning;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.Resp;
import com.sanyth.model.datawarning.DataWarningLevel;

import java.util.List;

/**
 * 预警级别
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface DataWarningLevelService extends IService<DataWarningLevel> {

    Page<DataWarningLevel> queryPage(BaseQuery<DataWarningLevel> query);

    Resp editLevel(DataWarningLevel dto);

    Resp delete(String id);

    /**
     * 获取指定预警规则对应的级别
     * @date 2022-3-2
     * @param ruleId 规则id
     * @param judgeValue 要判断的值
     * @return java.lang.String
     */
    String getLevelByValue(String ruleId, String judgeValue);

    List<DataWarningLevel> getList(DataWarningLevel query);
}
