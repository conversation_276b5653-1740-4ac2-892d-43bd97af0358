package com.sanyth.service.datawarning;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.Resp;
import com.sanyth.model.datawarning.DataWarningDealRecord;

import java.util.List;

/**
 * 预警信息处理记录
 *
 * <AUTHOR>
 * @since 2022-04-25
 */
public interface DataWarningDealRecordService extends IService<DataWarningDealRecord> {

    IPage<DataWarningDealRecord> queryPage(BaseQuery<DataWarningDealRecord> query);

    Resp delete(String... id);

    List<DataWarningDealRecord> getList(DataWarningDealRecord query);

    DataWarningDealRecord findOne(JSONObject params);

    Resp edit(DataWarningDealRecord dto);

    DataWarningDealRecord getByInfoId(String infoId);
}
