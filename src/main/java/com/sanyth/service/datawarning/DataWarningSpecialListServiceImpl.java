package com.sanyth.service.datawarning;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.ErrorInfo;
import com.sanyth.core.common.Resp;
import com.sanyth.mapper.datawarning.DataWarningSpecialListMapper;
import com.sanyth.model.datawarning.DataWarningSpecialList;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.pojo.Account;
import com.sanyth.pojo.AccountListener;
import com.sanyth.pojo.ImportErrorInfo;
import com.sanyth.service.system.ISytPermissionAccountService;
import com.sanyth.service.system.ISytPermissionRoleService;
import com.sanyth.service.system.ISytSysOrganizationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.*;

/**
 * 特殊名单(预警白名单)
 * <AUTHOR>
 * @since 2022-04-26
 */
@Service
public class DataWarningSpecialListServiceImpl extends ServiceImpl<DataWarningSpecialListMapper, DataWarningSpecialList> implements DataWarningSpecialListService {
    @Resource
    DataWarningSpecialListMapper dataWarningSpecialListMapper;
    @Autowired
    private DataWarningInfoService dataWarningInfoService;
    @Resource
    private ISytPermissionAccountService iSytPermissionAccountService;
    @Resource
    private ISytPermissionRoleService iSytPermissionRoleService;
    @Resource
    private ISytSysOrganizationService iSytSysOrganizationService;

    @Override
    public IPage<DataWarningSpecialList> queryPage(BaseQuery<DataWarningSpecialList> query) {
        Page<DataWarningSpecialList> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataWarningSpecialList> wrapper = buildWrapper(query.getQueryParam());
        return dataWarningSpecialListMapper.selectPageVo(page, wrapper, DataWarningSpecialList.class);
    }

    @Override
    public Resp delete(String... id) {
        if (id != null && id.length > 0) {
            removeByIds(Arrays.asList(id));
            return Resp.success();
        }
        return Resp.error(ErrorInfo.MSG_0003);
    }

    @Override
    public List<DataWarningSpecialList> getList(DataWarningSpecialList query) {
        Wrapper<DataWarningSpecialList> wrapper = buildWrapper(query);
        return this.list(wrapper);
    }

    @Override
    public DataWarningSpecialList findOne(JSONObject params) {
        QueryWrapper<DataWarningSpecialList> wrapper = new QueryWrapper<>();
        wrapper.eq("id", params.getString("id"));
        DataWarningSpecialList info = dataWarningSpecialListMapper.selectOneVo(wrapper, DataWarningSpecialList.class);
        return info;
    }

    @Override
    public void importData(MultipartFile file, String ruleid, HttpServletResponse response) {
        try{
            List<Account> accountList = EasyExcel.read(file.getInputStream(), Account.class, new AccountListener()).sheet().doReadSync();
            List<ImportErrorInfo> errorInfo = new ArrayList<>();
            if (!CollectionUtils.isEmpty(accountList)) {

                List<SytPermissionAccount> permissionAccountList = iSytPermissionAccountService.queryList(new SytPermissionAccount());
                Map<String, SytPermissionAccount> accountMap = new HashMap<>();
                for(SytPermissionAccount account : permissionAccountList ){
                    accountMap.put(account.getHumancode(), account);
                }

                Set<String> accountSets = new HashSet<>();
                DataWarningSpecialList specialObj = new DataWarningSpecialList();
                specialObj.setRuleid(ruleid);
                List<DataWarningSpecialList> accounts = this.getList(specialObj);
                for (DataWarningSpecialList account : accounts) {
                    accountSets.add(account.getHumancode());
                }

                for (Account account : accountList) {
                    String humancode = account.getHumancode();
                    boolean error = false;
                    SytPermissionAccount sytPermissionAccount = accountMap.get(humancode);
                    if(sytPermissionAccount == null){
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), humancode, "系统中不存在当前账号"));
                        error = true;
                    }
                    if (org.springframework.util.StringUtils.isEmpty(humancode)) {
                        errorInfo.add(new ImportErrorInfo(account.getRowIndex(), humancode, "不能为空"));
                        error = true;
                    } else {
                        if (accountSets.contains(humancode)) {
                            errorInfo.add(new ImportErrorInfo(account.getRowIndex(), humancode, "当前登录账号已存在"));
                            error = true;
                        }
                    }

                    if (!error) {
                        DataWarningSpecialList dataWarningSpecialList = new DataWarningSpecialList();
                        dataWarningSpecialList.setHumancode(humancode);
                        dataWarningSpecialList.setHumanname(sytPermissionAccount.getHumanname());
                        dataWarningSpecialList.setOrganizationnames(sytPermissionAccount.getOrganizationnames());
                        dataWarningSpecialList.setRuleid(ruleid);
                        this.save(dataWarningSpecialList);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(errorInfo)) {
                // 回写导入错误信息...
                String fileName = URLEncoder.encode("错误信息", "UTF-8");
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf8");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), ImportErrorInfo.class)
                        .excelType(ExcelTypeEnum.XLSX)
                        .sheet("sheet1")
                        .doWrite(errorInfo);
                return;
            }
            Resp.render(response, ErrorInfo.CODE_00000, ErrorInfo.CODE_MSG_00000);
        } catch (Exception e) {
            e.printStackTrace();
            Resp.render(response, ErrorInfo.CODE_00001, ErrorInfo.CODE_MSG_00001);
        }

    }

    @Override
    public List<String> getHumancodeList(String ruleid) {
        QueryWrapper<DataWarningSpecialList> wrapper = new QueryWrapper<>();
        wrapper.eq("ruleid", ruleid);
        List<DataWarningSpecialList> list = this.list(wrapper);
        List<String> result = new ArrayList<>();
        for(DataWarningSpecialList obj : list){
            result.add(obj.getHumancode());
        }
         return result;
    }

    private Wrapper<DataWarningSpecialList> buildWrapper(DataWarningSpecialList dto) {
        QueryWrapper<DataWarningSpecialList> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(dto.getHumancode()))
            wrapper.eq("humancode", dto.getHumancode());
        if (StringUtils.isNotBlank(dto.getHumanname()))
            wrapper.like("humanname", dto.getHumanname());
        if (StringUtils.isNotBlank(dto.getRuleid()))
            wrapper.eq("ruleid", dto.getRuleid());
        if (StringUtils.isNotBlank(dto.getOrgid())) {
            wrapper.apply("HUMANCODE in (select HUMANCODE from SYT_PERMISSION_ACCOUNT inner join SYT_SYS_ORGANIZATION_USER on ID=USER_ID " +
                    "where ORGANIZATION_ID in ('" + StringUtils.join(dto.getOrgid().split(","), "','") + "'))");
        }
        wrapper.orderByDesc("humancode");
        return wrapper;
    }
}
