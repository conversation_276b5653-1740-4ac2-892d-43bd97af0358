package com.sanyth.service.datawarning;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.Resp;
import com.sanyth.dto.DataWarningInfoDto;
import com.sanyth.model.datawarning.DataWarningInfo;

import java.util.List;

public interface DataWarningInfoService extends IService<DataWarningInfo> {

    IPage<DataWarningInfoDto> queryPage(BaseQuery<DataWarningInfoDto> query);

    Resp updateStatus(DataWarningInfoDto dto);

    Resp delete(String... id);

    List<DataWarningInfo> getList(DataWarningInfoDto query);

    DataWarningInfoDto findOne(JSONObject params);
}
