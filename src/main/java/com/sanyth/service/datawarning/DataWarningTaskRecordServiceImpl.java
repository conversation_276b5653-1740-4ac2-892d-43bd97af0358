package com.sanyth.service.datawarning;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.mapper.datawarning.DataWarningTaskRecordMapper;
import com.sanyth.model.datawarning.DataWarningTaskRecord;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 预警任务记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-23
 */
@Service
public class DataWarningTaskRecordServiceImpl extends ServiceImpl<DataWarningTaskRecordMapper, DataWarningTaskRecord> implements DataWarningTaskRecordService {

    @Override
    public Page<DataWarningTaskRecord> queryPage(BaseQuery<DataWarningTaskRecord> query) {
        Page<DataWarningTaskRecord> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataWarningTaskRecord> wrapper = buildWrapper(query.getQueryParam());
        return this.page(page, wrapper);
    }

    private Wrapper<DataWarningTaskRecord> buildWrapper(DataWarningTaskRecord query) {
        QueryWrapper<DataWarningTaskRecord> wrapper = new QueryWrapper<>();
        if (StringUtils.isNoneBlank(query.getTasksummarycode())) {
            wrapper.eq("tasksummarycode", query.getTasksummarycode());
        }
        if (StringUtils.isNoneBlank(query.getTasksummaryname())) {
            wrapper.like("tasksummaryname", query.getTasksummaryname());
        }
        if (StringUtils.isNoneBlank(query.getRulecode())) {
            wrapper.in("rulecode", query.getRulecode().split(","));
        }
        if (StringUtils.isNoneBlank(query.getRulename())) {
            wrapper.like("rulename", query.getRulename());
        }
        if (StringUtils.isNoneBlank(query.getStatus())) {
            wrapper.eq("status", query.getStatus());
        }
        if (StringUtils.isNoneBlank(query.getType())) {
            wrapper.eq("type", query.getType());
        }
        wrapper.orderByDesc("CREATEDATE");
        return wrapper;
    }
}
