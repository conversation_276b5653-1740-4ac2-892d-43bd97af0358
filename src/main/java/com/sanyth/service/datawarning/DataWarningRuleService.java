package com.sanyth.service.datawarning;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.CurrentUser;
import com.sanyth.core.common.Resp;
import com.sanyth.dto.DataSetDto;
import com.sanyth.dto.OriginalDataDto;
import com.sanyth.model.datawarning.DataWarningRule;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-23
 */
public interface DataWarningRuleService extends IService<DataWarningRule> {

    Page<DataSetDto> queryPage(BaseQuery<DataWarningRule> query);
    DataSetDto detailSet(DataSetDto dto);
    DataSetDto insertRule(DataSetDto dto);

    void removeBatch(List<String> asList);
    OriginalDataDto getData(DataSetDto setDto) throws Exception;

    void modifyEnableFlag(DataWarningRule rule, CurrentUser user);

    void execute(DataWarningRule rule);

    List<DataSetDto> queryList(DataWarningRule query);

    /**
     * 复制规则
     * @param ruleid 要复制的规则id
     */
    Resp replicateRule(String ruleid, String createUser);
}
