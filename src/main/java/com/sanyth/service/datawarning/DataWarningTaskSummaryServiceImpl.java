package com.sanyth.service.datawarning;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.enums.RunStatusEnum;
import com.sanyth.core.exception.BusinessException;
import com.sanyth.dto.QuartzDTO;
import com.sanyth.mapper.datawarning.DataWarningRuleMapper;
import com.sanyth.mapper.datawarning.DataWarningTaskSummaryMapper;
import com.sanyth.model.datawarning.DataWarningRule;
import com.sanyth.model.datawarning.DataWarningTaskSummary;
import com.sanyth.quartz.QuartzManage;
import com.sanyth.util.QuartzUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 预警汇总推送 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
@Service
public class DataWarningTaskSummaryServiceImpl extends ServiceImpl<DataWarningTaskSummaryMapper, DataWarningTaskSummary> implements DataWarningTaskSummaryService {

    @Autowired
    private DataWarningRuleService dataWarningRuleService;
    @Autowired
    private DataWarningTaskSummaryMapper dataWarningTaskSummaryMapper;
    @Autowired
    private DataWarningRuleMapper dataWarningRuleMapper;

    @Override
    public Page<DataWarningTaskSummary> queryPage(BaseQuery<DataWarningTaskSummary> query) {
        Page<DataWarningTaskSummary> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataWarningTaskSummary> wrapper = buildWrapper(query.getQueryParam());
        return this.page(page, wrapper);
    }

    @Transactional
    @Override
    public void modifyEnableFlag(DataWarningTaskSummary summary) {
        Integer enableFlag = summary.getEnableflag();
        summary.setEnableflag(null);
        Wrapper<DataWarningTaskSummary> wrapper = buildWrapper(summary);
        List<DataWarningTaskSummary> list = this.list(wrapper);
        list.forEach(r->{
            r.setEnableflag(enableFlag);
            this.saveOrUpdate(r);
            QueryWrapper<DataWarningRule> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", r.getRuleids().split(","));
            List<DataWarningRule> warningRuleList = dataWarningRuleService.list(queryWrapper);
            switch (r.getEnableflag()) {
                case 0: //启动
                    warningRuleList.forEach(rule -> {
                        // 获取定时任务需要的参数
//                        QuartzDTO quartzDTO = QuartzUtil.getQuartzDTO(r.getId(),rule.getId(), rule.getCheckCron(), rule.getRuleType());
                        //使用总任务的定时时间覆盖单个规则的定时时间
                        QuartzDTO quartzDTO = QuartzUtil.getQuartzDTO(r.getId(),rule.getId(), r.getExeccron(), rule.getRuleType());
                        // 根据定时策略添加任务
                        if (StringUtils.isNotBlank(r.getExeccron())) {
                            // 添加定时任务
                            QuartzManage.addCronJob(quartzDTO);
                        } else {
                            // 添加一次性任务
                            QuartzManage.addOnceJob(quartzDTO);
                        }
                    });
                    break;
                case 1: //停止
                    warningRuleList.forEach(rule -> {
                        QuartzManage.removeJob(QuartzUtil.getQuartzDTO(r.getId(),rule.getId(), "", rule.getRuleType()));
                    });
                    break;
                default:
                    break;
            }
        });
    }

    @Override
    public void execute(DataWarningTaskSummary summary) {
        // 查询转换信息
        DataWarningTaskSummary taskSummary = this.getById(summary.getId());
        if (taskSummary == null) {
            throw new BusinessException("当前任务不存在");
        }
        QueryWrapper<DataWarningRule> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", taskSummary.getRuleids().split(","));
        List<DataWarningRule> warningRuleList = dataWarningRuleService.list(queryWrapper);
        warningRuleList.forEach(rule -> {
//            QuartzManage.addOnceJob(QuartzUtil.getQuartzDTO(taskSummary.getId(),rule.getId(), rule.getCheckCron(), rule.getRuleType()));
            QuartzManage.addOnceJob(QuartzUtil.getQuartzDTO(summary.getId(),rule.getId(), summary.getExeccron(), rule.getRuleType()));
        });
    }

    @Override
    public void insertSummary(DataWarningTaskSummary summary) {
        summary.setEnableflag(Integer.valueOf(RunStatusEnum.STOP.getCode()));
        summary.setCreatetime(new Date());
        if (StringUtils.isNotBlank(summary.getId())) {
            QueryWrapper<DataWarningRule> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", summary.getRuleids().split(","));
            List<DataWarningRule> warningRuleList = dataWarningRuleService.list(queryWrapper);
            warningRuleList.forEach(rule -> {
//                QuartzDTO quartzDTO = QuartzUtil.getQuartzDTO(summary.getId(),rule.getId(), rule.getCheckCron(), rule.getRuleType());
                QuartzDTO quartzDTO = QuartzUtil.getQuartzDTO(summary.getId(), rule.getId(), "", rule.getRuleType());
                QuartzManage.removeJob(quartzDTO);
            });
        }
        saveOrUpdate(summary);
    }

    @Override
    public List<DataWarningTaskSummary> queryList(DataWarningTaskSummary taskSummary) {
        Wrapper<DataWarningTaskSummary> wrapper = buildWrapper(taskSummary);
        List<DataWarningTaskSummary> list = this.list(wrapper);
        return list;
    }

    @Transactional
    @Override
    public void removeBatch(List<String> asList) {
        QueryWrapper<DataWarningTaskSummary> summaryQueryWrapper = new QueryWrapper<>();
        summaryQueryWrapper.in("id", asList);
        List<DataWarningTaskSummary> dataWarningTaskSummaries = dataWarningTaskSummaryMapper.selectList(summaryQueryWrapper);
        dataWarningTaskSummaries.forEach(taskSummary->{
            QueryWrapper<DataWarningRule> ruleQueryWrapper = new QueryWrapper<>();
            ruleQueryWrapper.in("id", taskSummary.getRuleids().split(","));
            List<DataWarningRule> dataWarningRules = dataWarningRuleMapper.selectList(ruleQueryWrapper);
            dataWarningRules.forEach(dataWarningRule->{
                // 关闭定时任务
                QuartzManage.removeJob(QuartzUtil.getQuartzDTO(taskSummary.getId(),dataWarningRule.getId(), "", dataWarningRule.getRuleType()));
            });
        });


        removeByIds(asList);
    }

    public Wrapper buildWrapper(DataWarningTaskSummary query) {
        QueryWrapper<DataWarningTaskSummary> wrapper = new QueryWrapper<>();
        if (StringUtils.isNoneBlank(query.getTaskname())) {
            wrapper.like("taskname", query.getTaskname());
        }
        if (query.getEnableflag()!=null) {
            wrapper.eq("enableflag", query.getEnableflag());
        }
        return wrapper;
    }
}
