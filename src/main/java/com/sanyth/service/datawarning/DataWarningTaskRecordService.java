package com.sanyth.service.datawarning;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.datawarning.DataWarningTaskRecord;

/**
 * <p>
 * 预警任务记录 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-23
 */
public interface DataWarningTaskRecordService extends IService<DataWarningTaskRecord> {

    Page<DataWarningTaskRecord> queryPage(BaseQuery<DataWarningTaskRecord> query);
}
