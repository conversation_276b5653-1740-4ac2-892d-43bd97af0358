package com.sanyth.service.datawarning;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.ErrorInfo;
import com.sanyth.core.common.Resp;
import com.sanyth.dto.DataWarningInfoDto;
import com.sanyth.mapper.datawarning.DataWarningInfoMapper;
import com.sanyth.model.datawarning.DataWarningDealRecord;
import com.sanyth.model.datawarning.DataWarningInfo;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.service.system.ISytPermissionAccountService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

@Service
public class DataWarningInfoServiceImpl extends ServiceImpl<DataWarningInfoMapper, DataWarningInfo> implements DataWarningInfoService {

    @Resource
    DataWarningInfoMapper dataWarningInfoMapper;
    @Resource
    private ISytPermissionAccountService sytPermissionAccountService;
    @Resource
    private DataWarningDealRecordService dataWarningDealRecordService;

    @Override
    public IPage<DataWarningInfoDto> queryPage(BaseQuery<DataWarningInfoDto> query) {
        Page<DataWarningInfo> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataWarningInfo> wrapper = buildWrapper(query.getQueryParam());
//        return page(page, wrapper);
        IPage<DataWarningInfoDto> pageVo = dataWarningInfoMapper.selectPageVo(page, wrapper, DataWarningInfoDto.class);
        List<DataWarningInfoDto> list = pageVo.getRecords();
        list.forEach(info->{
            DataWarningDealRecord dealRecord = dataWarningDealRecordService.getByInfoId(info.getId());
            if (dealRecord != null) {
                info.setOperatorname(dealRecord.getOperatorname());
                info.setOperatorcomment(dealRecord.getOperatorcomment());
            }
            try {
                info.setTelmobile1(sytPermissionAccountService.getCleartextTelmobile(info.getTelmobile1()));
            } catch (Exception e) {
                info.setTelmobile1(info.getTelmobile1());
            }
        });
        return pageVo;
    }

    @Override
    public Resp updateStatus(DataWarningInfoDto dto) {
        try {
            if (StringUtils.isBlank(dto.getId()) || StringUtils.isBlank(dto.getStatus())) {
                return Resp.error(ErrorInfo.MSG_0003);
            }
            String[] ids = dto.getId().split(",");
            for (String id : ids) {
                DataWarningInfo dataWarningInfo = this.getById(id);
                dataWarningInfo.setStatus(dto.getStatus());
                this.updateById(dataWarningInfo);
            }
            return Resp.success();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Resp.error();
    }

    @Override
    public Resp delete(String... id) {
        if (id != null && id.length > 0) {
            removeByIds(Arrays.asList(id));
            return Resp.success();
        }
        return Resp.error(ErrorInfo.MSG_0003);
    }

    @Override
    public List<DataWarningInfo> getList(DataWarningInfoDto query) {
        Wrapper<DataWarningInfo> wrapper = buildWrapper(query);
        return this.list(wrapper);
    }

    @Override
    public DataWarningInfoDto findOne(JSONObject params) {
        QueryWrapper<DataWarningInfo> wrapper = new QueryWrapper<>();
        wrapper.eq("id", params.getString("id"));
        DataWarningInfoDto info = dataWarningInfoMapper.selectOneVo(wrapper, DataWarningInfoDto.class);
        DataWarningDealRecord dealRecord = dataWarningDealRecordService.getByInfoId(info.getId());
        if (dealRecord != null) {
            info.setOperatorname(dealRecord.getOperatorname());
            info.setOperatorcomment(dealRecord.getOperatorcomment());
        }
        SytPermissionAccount account = sytPermissionAccountService.getByHumancode(info.getHumanCode());
        if (ObjectUtils.isNotEmpty(account)) {
            try {
                account.setTelmobile1(sytPermissionAccountService.getCleartextTelmobile(account.getTelmobile1()));
            } catch (Exception e) {
                account.setTelmobile1(account.getTelmobile1());
            }

            BeanUtils.copyProperties(account, info, "id");
        }
        return info;
    }

    private Wrapper<DataWarningInfo> buildWrapper(DataWarningInfoDto dto) {
        QueryWrapper<DataWarningInfo> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(dto.getTaskRecordId()))
            wrapper.eq("taskRecordId", dto.getTaskRecordId());
        if (StringUtils.isNotBlank(dto.getRuleId()))
            wrapper.eq("ruleId", dto.getRuleId());
        if (StringUtils.isNotBlank(dto.getHumanCode()))
            wrapper.eq("humancode", dto.getHumanCode());
        if (StringUtils.isNotBlank(dto.getHumanName()))
            wrapper.like("humanName", dto.getHumanName());
        if (StringUtils.isNotBlank(dto.getOrganizationNames()))
            wrapper.like("organizationNames", dto.getOrganizationNames());
        if (StringUtils.isNotBlank(dto.getOrgid())) {
            wrapper.apply("HUMANCODE in (select HUMANCODE from SYT_PERMISSION_ACCOUNT inner join SYT_SYS_ORGANIZATION_USER on ID=USER_ID " +
                    "where ORGANIZATION_ID in ('" + StringUtils.join(dto.getOrgid().split(","), "','") + "'))");
        }
        if (StringUtils.isNotBlank(dto.getOperatorname())) {
            wrapper.apply("id in (select INFOID from DATA_WARNING_DEAL_RECORD where OPERATORNAME like '%" + dto.getOperatorname() + "%') ");
        }

        if (StringUtils.isNotBlank(dto.getWarnType()))
            wrapper.eq("warnType", dto.getWarnType());
        if (StringUtils.isNotBlank(dto.getWarnName()))
            wrapper.eq("warnName", dto.getWarnName());
        if (StringUtils.isNotBlank(dto.getWarnLevel()))
            wrapper.eq("warnLevel", dto.getWarnLevel());
        if (StringUtils.isNotBlank(dto.getStatus()))
            wrapper.eq("status", dto.getStatus());
        if (StringUtils.isNotBlank(dto.getStartTime())) {
            String formatStr = dto.getStartTime().length() == 7 ? "yyyy-MM" : "yyyy-MM-dd";
            Date beginTime = DateUtil.parse(dto.getStartTime(), formatStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(beginTime);
            if (dto.getStartTime().length() == 7) { // 日期格式为yyyy-MM
                calendar.set(Calendar.DAY_OF_MONTH, 1);
            }
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            wrapper.ge("warnTime", calendar.getTime());
        }
        if (StringUtils.isNotBlank(dto.getEndTime())) {
            String formatStr = dto.getEndTime().length() == 7 ? "yyyy-MM" : "yyyy-MM-dd";
            Date endTime = DateUtil.parse(dto.getEndTime(), formatStr);
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endTime);
            if (dto.getEndTime().length() == 7) { // 日期格式为yyyy-MM
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            }
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            wrapper.le("warnTime", calendar.getTime());
        }
        wrapper.orderByDesc("warnTime").orderByAsc("humancode");
        return wrapper;
    }

}
