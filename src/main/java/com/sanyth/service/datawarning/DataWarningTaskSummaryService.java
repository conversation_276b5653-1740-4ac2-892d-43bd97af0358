package com.sanyth.service.datawarning;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.datawarning.DataWarningTaskSummary;

import java.util.List;

/**
 * <p>
 * 预警汇总推送 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-02
 */
public interface DataWarningTaskSummaryService extends IService<DataWarningTaskSummary> {

    Page<DataWarningTaskSummary> queryPage(BaseQuery<DataWarningTaskSummary> query);

    void modifyEnableFlag(DataWarningTaskSummary summary);

    void execute(DataWarningTaskSummary summary);

    void insertSummary(DataWarningTaskSummary summary);

    List<DataWarningTaskSummary> queryList(DataWarningTaskSummary taskSummary);

    void removeBatch(List<String> asList);

}
