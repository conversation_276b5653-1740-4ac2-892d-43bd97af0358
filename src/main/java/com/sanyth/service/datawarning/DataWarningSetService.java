package com.sanyth.service.datawarning;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.dto.DataSetDto;
import com.sanyth.dto.OriginalDataDto;
import com.sanyth.model.datawarning.DataWarningSet;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-17
 */
public interface DataWarningSetService extends IService<DataWarningSet> {

    JSONObject testTransform(DataSetDto dto) throws Exception;

    DataSetDto insertSet(DataSetDto dto);

    DataSetDto detailSet(DataSetDto dto);

    OriginalDataDto getData(DataSetDto setDto) throws Exception;

    Page<DataSetDto> queryPage(BaseQuery<DataWarningSet> query);

    List<DataSetDto> queryList(DataWarningSet query);

    List<DataWarningSet> getAll(DataWarningSet query);

    void removeBatch(List<String> asList);
}
