package com.sanyth.service.datawarning;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.Constants;
import com.sanyth.dto.DataSetDto;
import com.sanyth.dto.DataSetParamDto;
import com.sanyth.dto.DataSetTransformDto;
import com.sanyth.dto.OriginalDataDto;
import com.sanyth.mapper.datawarning.DataWarningSetMapper;
import com.sanyth.model.datawarning.DataWarningSet;
import com.sanyth.model.datawarning.DataWarningSetParam;
import com.sanyth.model.datawarning.DataWarningSetTransform;
import com.sanyth.service.report.DataProcessingService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-17
 */
@Slf4j
@Service
public class DataWarningSetServiceImpl extends ServiceImpl<DataWarningSetMapper, DataWarningSet> implements DataWarningSetService {

    @Resource
    private DataWarningSetParamService dataWarningSetParamService;
    @Autowired
    private DataWarningSetTransformService dataWarningSetTransformService;
    @Resource
    private DataProcessingService dataProcessingService;

    @Override
    public JSONObject testTransform(DataSetDto dto) throws Exception {
        return dataProcessingService.testTransform(dto);
    }

    @Override
    @Transactional
    public DataSetDto insertSet(DataSetDto dto) {
        List<DataSetParamDto> dataSetParamDtoList = dto.getDataSetParamDtoList();
//        List<DataSetParamDto> dataSetDrillParamDtoList = dto.getDataSetDrillParamDtoList();
        List<DataSetTransformDto> dataSetTransformDtoList = dto.getDataSetTransformDtoList();

        //1.新增数据集
        DataWarningSet dataWarningSet = new DataWarningSet();
        BeanUtils.copyProperties(dto, dataWarningSet);
        dataWarningSet.setCreateTime(new Date());
        saveOrUpdate(dataWarningSet);
        //2.更新查询参数
        dto.setId(dataWarningSet.getId());
        dataSetParamBatch(dataSetParamDtoList, dto.getId(), Constants.DATA_SET_PARAMS_TYPE_SELECT);
//        dataSetParamBatch(dataSetDrillParamDtoList, dto.getId(), Constants.DATA_SET_PARAMS_TYPE_DRILL);

        //3.更新数据转换
        dataSetTransformBatch(dataSetTransformDtoList, dto.getId());
        return dto;
    }

    @Override
    public DataSetDto detailSet(DataSetDto dto) {
        String id = dto.getId();
        if (StringUtils.isBlank(id)) {
            return dto;
        }
        DataWarningSet dataWarningSet = this.getById(id);
        if (dataWarningSet == null) {
            return dto;
        }
        //查询参数
        List<DataWarningSetParam> dataSetParamList = dataWarningSetParamService.list(new QueryWrapper<DataWarningSetParam>().eq("SETCODE", id).eq("TYPE",Constants.DATA_SET_PARAMS_TYPE_SELECT));
        List<DataSetParamDto> dataSetParamDtoList = new ArrayList<>();
        dataSetParamList.forEach(dataSetParam -> {
            DataSetParamDto dataSetParamDto = new DataSetParamDto();
            BeanUtils.copyProperties(dataSetParam, dataSetParamDto);
            dataSetParamDtoList.add(dataSetParamDto);
        });
        dto.setDataSetParamDtoList(dataSetParamDtoList);
        //下钻参数
        /*List<DataWarningSetParam> dataSetDrillParamList = dataWarningSetParamService.list(new QueryWrapper<DataWarningSetParam>().eq("SETCODE", id).eq("TYPE",Constants.DATA_SET_PARAMS_TYPE_DRILL));
        List<DataSetParamDto> dataSetDrillParamDtoList = new ArrayList<>();
        dataSetDrillParamList.forEach(dataSetParam -> {
            DataSetParamDto dataSetParamDto = new DataSetParamDto();
            BeanUtils.copyProperties(dataSetParam, dataSetParamDto);
            dataSetDrillParamDtoList.add(dataSetParamDto);
        });
        dto.setDataSetDrillParamDtoList(dataSetDrillParamDtoList);
        Set<String> drillParamSet = new HashSet<>();
        dataSetDrillParamDtoList.stream().forEach(drillParam->{
            drillParamSet.add(drillParam.getParamName());
        });
        dto.setDrillParamList(drillParamSet);*/

        //数据转换
        List<DataWarningSetTransform> dataSetTransformList = dataWarningSetTransformService.list(new QueryWrapper<DataWarningSetTransform>().eq("SETCODE", id));
        List<DataSetTransformDto> dataSetTransformDtoList = new ArrayList<>();
        dataSetTransformList.forEach(dataSetTransform -> {
            DataSetTransformDto dataSetTransformDto = new DataSetTransformDto();
            BeanUtils.copyProperties(dataSetTransform, dataSetTransformDto);
            dataSetTransformDtoList.add(dataSetTransformDto);
        });
        dto.setDataSetTransformDtoList(dataSetTransformDtoList);
        if (StringUtils.isNotBlank(dataWarningSet.getCaseResult())) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(dataWarningSet.getCaseResult());
                if (jsonArray.size() > 0) {
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    dto.setSetParamList(jsonObject.keySet());
                }
            } catch (Exception e) {
                log.error("dataWarningSet.getCaseResult出现异常");
            }
        }

        BeanUtils.copyProperties(dataWarningSet, dto);
        return dto;
    }

    @Override
    public OriginalDataDto getData(DataSetDto dto) throws Exception {
        return dataProcessingService.getData(detailSet(dto));
    }

    @Override
    public Page<DataSetDto> queryPage(BaseQuery<DataWarningSet> query) {
        Page<DataWarningSet> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataWarningSet> wrapper = buildWrapper(query.getQueryParam());
        long count = this.count(wrapper);
        Page<DataWarningSet> dataSetPage = this.page(page, wrapper);
        Page<DataSetDto> dataSetDtoPage = new Page<>(query.getPage(), query.getPageSize());
        List<DataSetDto> dataSetDtos = new LinkedList<>();
        dataSetPage.getRecords().forEach(dataSet -> {
            DataSetDto dataSetDto = new DataSetDto();
            BeanUtils.copyProperties(dataSet, dataSetDto);
            detailSet(dataSetDto);
            dataSetDtos.add(dataSetDto);
        });
        dataSetDtoPage.setRecords(dataSetDtos);
        dataSetDtoPage.setTotal(count);
        return dataSetDtoPage;
    }

    @Override
    public List<DataSetDto> queryList(DataWarningSet query) {
        Wrapper<DataWarningSet> wrapper = buildWrapper(query);
        List<DataWarningSet> list = this.list(wrapper);
        List<DataSetDto> dataSetDtos = new LinkedList<>();
        list.forEach(dataSet -> {
            DataSetDto dataSetDto = new DataSetDto();
            BeanUtils.copyProperties(dataSet, dataSetDto);
            detailSet(dataSetDto);
            dataSetDtos.add(dataSetDto);
        });
        return dataSetDtos;
    }

    @Override
    public List<DataWarningSet> getAll(DataWarningSet query) {
        Wrapper<DataWarningSet> wrapper = buildWrapper(query);
        return this.list(wrapper);
    }

    @Override
    @Transactional
    public void removeBatch(List<String> asList) {
        removeByIds(asList);
        QueryWrapper<DataWarningSetParam> wrapper = new QueryWrapper<>();
        wrapper.in("setCode", asList);
        dataWarningSetParamService.remove(wrapper);
    }

    private Wrapper<DataWarningSet> buildWrapper(DataWarningSet query) {
        QueryWrapper<DataWarningSet> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNoneBlank(query.getSourceCode())) {
            wrapper.eq("sourceCode", query.getSourceCode());
        }
        if (StringUtils.isNoneBlank(query.getSetName())) {
            wrapper.like("setName", query.getSetName());
        }
        if (StringUtils.isNoneBlank(query.getSetType())) {
            wrapper.like("setType", query.getSetType());
        }
        if (StringUtils.isNoneBlank(query.getType())) {
            wrapper.eq("type", query.getType());
        }
        wrapper.orderByDesc("createTime");
        return wrapper;
    }

    public void dataSetParamBatch(List<DataSetParamDto> dataSetParamDtoList,String setId,String paramType){
        if (StringUtils.isNoneBlank(setId)) {
            dataWarningSetParamService.remove(new QueryWrapper<DataWarningSetParam>().eq("SETCODE", setId).eq("TYPE", paramType));
        }
        if (null == dataSetParamDtoList || dataSetParamDtoList.size() <= 0) {
            return;
        }
        List<DataWarningSetParam> dataSetParamList = new ArrayList<>();
        dataSetParamDtoList.forEach(dataSetParamDto -> {
            DataWarningSetParam dataWarningSetParam = new DataWarningSetParam();
            BeanUtils.copyProperties(dataSetParamDto, dataWarningSetParam);
            dataWarningSetParam.setSetCode(setId);
            dataWarningSetParam.setType(paramType);
            dataWarningSetParam.setCreateTime(new Date());
            dataSetParamList.add(dataWarningSetParam);
        });
        dataWarningSetParamService.saveBatch(dataSetParamList);
    }

    public void dataSetTransformBatch(List<DataSetTransformDto> dataSetTransformDtoList,String setId){
        if (StringUtils.isNoneBlank(setId)) {
            dataWarningSetTransformService.remove(new QueryWrapper<DataWarningSetTransform>().eq("SETCODE",setId));
        }
        if (null == dataSetTransformDtoList || dataSetTransformDtoList.size() <= 0) {
            return;
        }
        List<DataWarningSetTransform> dataSetTransformList = new ArrayList<>();
        for (int i = 0; i < dataSetTransformDtoList.size(); i++) {
            DataWarningSetTransform dataWarningSetTransform = new DataWarningSetTransform();
            BeanUtils.copyProperties(dataSetTransformDtoList.get(i), dataWarningSetTransform);
            dataWarningSetTransform.setOrderNum(i + 1);
            dataWarningSetTransform.setSetCode(setId);
            dataWarningSetTransform.setCreateTime(new Date());
            dataSetTransformList.add(dataWarningSetTransform);
        }
        dataWarningSetTransformService.saveBatch(dataSetTransformList);
    }
}
