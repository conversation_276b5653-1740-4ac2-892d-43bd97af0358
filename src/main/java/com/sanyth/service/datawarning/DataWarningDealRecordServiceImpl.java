package com.sanyth.service.datawarning;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.ErrorInfo;
import com.sanyth.core.common.Resp;
import com.sanyth.mapper.datawarning.DataWarningDealRecordMapper;
import com.sanyth.model.datawarning.DataWarningDealRecord;
import com.sanyth.model.datawarning.DataWarningInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * 预警信息处理记录
 * <AUTHOR>
 * @since 2022-04-25
 */
@Service
public class DataWarningDealRecordServiceImpl extends ServiceImpl<DataWarningDealRecordMapper, DataWarningDealRecord> implements DataWarningDealRecordService {

    @Resource
    DataWarningDealRecordMapper dataWarningDealRecordMapper;
    @Autowired
    private DataWarningInfoService dataWarningInfoService;

    @Override
    public IPage<DataWarningDealRecord> queryPage(BaseQuery<DataWarningDealRecord> query) {
        Page<DataWarningDealRecord> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataWarningDealRecord> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    @Override
    public Resp delete(String... id) {
        if (id != null && id.length > 0) {
            removeByIds(Arrays.asList(id));
            return Resp.success();
        }
        return Resp.error(ErrorInfo.MSG_0003);
    }

    @Override
    public List<DataWarningDealRecord> getList(DataWarningDealRecord query) {
        Wrapper<DataWarningDealRecord> wrapper = buildWrapper(query);
        return this.list(wrapper);
    }

    @Override
    public DataWarningDealRecord findOne(JSONObject params) {
        QueryWrapper<DataWarningDealRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("id", params.getString("id"));
        DataWarningDealRecord info = dataWarningDealRecordMapper.selectOneVo(wrapper, DataWarningDealRecord.class);
        return info;
    }

    @Override
    @Transactional
    public Resp edit(DataWarningDealRecord dto) {
        if(StringUtils.isNotBlank(dto.getInfoid())){
            dto.setOperatortime(new Date());
            boolean b = this.saveOrUpdate(dto);
            // 更新预警信息状态
            DataWarningInfo dataWarningInfo = dataWarningInfoService.getById(dto.getInfoid());
//            dataWarningInfo.setStatus(Constants.WARNING_INFO_STATUS_PROCESSED);     // 已处理
            dataWarningInfo.setStatus(dto.getStatus());
            dataWarningInfoService.saveOrUpdate(dataWarningInfo);
            if(b){
                return Resp.success();
            }
        }
        return Resp.error(ErrorInfo.CODE_MSG_00001);
    }

    @Override
    public DataWarningDealRecord getByInfoId(String infoId) {
        QueryWrapper<DataWarningDealRecord> wrapper = new QueryWrapper<>();
        wrapper.eq("infoid", infoId);
        List<DataWarningDealRecord> list = list(wrapper);
        return list.size() > 0 ? list.get(0) : null;
    }

    private Wrapper<DataWarningDealRecord> buildWrapper(DataWarningDealRecord dto) {
        QueryWrapper<DataWarningDealRecord> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNotBlank(dto.getInfoid()))
            wrapper.eq("infoid", dto.getInfoid());
        if (StringUtils.isNotBlank(dto.getOperatorcode()))
            wrapper.eq("operatorcode", dto.getOperatorcode());
        wrapper.orderByDesc("operatortime");
        return wrapper;
    }
}
