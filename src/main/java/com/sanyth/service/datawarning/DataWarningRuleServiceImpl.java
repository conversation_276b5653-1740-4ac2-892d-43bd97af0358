package com.sanyth.service.datawarning;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.CurrentUser;
import com.sanyth.core.common.Resp;
import com.sanyth.core.enums.DataScopeType;
import com.sanyth.core.enums.RunStatusEnum;
import com.sanyth.core.exception.BusinessException;
import com.sanyth.dto.DataSetDto;
import com.sanyth.dto.DataSetParamDto;
import com.sanyth.dto.OriginalDataDto;
import com.sanyth.dto.QuartzDTO;
import com.sanyth.mapper.datawarning.DataWarningRuleMapper;
import com.sanyth.model.datawarning.*;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.quartz.QuartzManage;
import com.sanyth.service.report.DataProcessingService;
import com.sanyth.util.QuartzUtil;
import com.sanyth.util.SecurityUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-12-23
 */
@Service
public class DataWarningRuleServiceImpl extends ServiceImpl<DataWarningRuleMapper, DataWarningRule> implements DataWarningRuleService {

    @Resource
    DataWarningRuleParamService dataWarningRuleParamService;
    @Resource
    private DataProcessingService dataProcessingService;
    @Autowired
    private DataWarningSpecialListService dataWarningSpecialListService;
    @Autowired
    private DataWarningInfoService dataWarningInfoService;
    @Autowired
    private DataWarningTaskRecordService dataWarningTaskRecordService;
    @Autowired
    private DataWarningLevelServiceImpl dataWarningLevelServiceImpl;
    @Autowired
    private DataWarningRuleMapper dataWarningRuleMapper;

    @Override
    public Page<DataSetDto> queryPage(BaseQuery<DataWarningRule> query) {
        Page<DataWarningRule> page = new Page<>(query.getPage(), query.getPageSize());
        QueryWrapper<DataWarningRule> dataWarningRuleWrapper = buildWrapper(query.getQueryParam());
        CurrentUser user = query.getCurrentUser();
        if (DataScopeType.OWN_AND_CHILD_DEPT.getType().equals(user.getRoletype()) || DataScopeType.OWN_ONLY_DEPT.getType().equals(user.getRoletype())) {
            dataWarningRuleWrapper.and(wrapper -> user.getOrganizations().forEach(organization -> wrapper.like("ORGANIZATIONS", organization.getId())));
        }

        long count = this.count(dataWarningRuleWrapper);
        Page<DataWarningRule> dataSetPage = this.page(page, dataWarningRuleWrapper);
        Page<DataSetDto> dataSetDtoPage = new Page<>(query.getPage(), query.getPageSize());
        List<DataSetDto> dataSetDtos = new LinkedList<>();
        dataSetPage.getRecords().forEach(dataSet -> {
            DataSetDto dataSetDto = new DataSetDto();
            BeanUtils.copyProperties(dataSet, dataSetDto);
            detailSet(dataSetDto);
            dataSetDtos.add(dataSetDto);
        });
        dataSetDtoPage.setRecords(dataSetDtos);
        dataSetDtoPage.setTotal(count);
        return dataSetDtoPage;
    }

    public DataSetDto detailSet(DataSetDto dto) {
        String id = dto.getId();
        if (StringUtils.isBlank(id)) {
            return dto;
        }
        DataWarningRule dataWarningRule = this.getById(id);
        if (dataWarningRule == null) {
            return dto;
        }
        //查询参数
        List<DataWarningRuleParam> dataSetParamList = dataWarningRuleParamService.list(new QueryWrapper<DataWarningRuleParam>().eq("RULECODE", id).orderByAsc("orderNum"));
        List<DataSetParamDto> dataSetParamDtoList = new ArrayList<>();
        dataSetParamList.forEach(dataSetParam -> {
            DataSetParamDto dataSetParamDto = new DataSetParamDto();
            BeanUtils.copyProperties(dataSetParam, dataSetParamDto);
            dataSetParamDtoList.add(dataSetParamDto);
        });
        dto.setDataSetParamDtoList(dataSetParamDtoList);

        //数据转换
        BeanUtils.copyProperties(dataWarningRule, dto);
        return dto;
    }

    @Override
    public DataSetDto insertRule(DataSetDto dto) {
        List<DataSetParamDto> dataSetParamDtoList = dto.getDataSetParamDtoList();

        //1.新增规则
        DataWarningRule dataWarningRule = new DataWarningRule();
        BeanUtils.copyProperties(dto, dataWarningRule);
        dataWarningRule.setEnableFlag(Integer.valueOf(RunStatusEnum.STOP.getCode()));
        dataWarningRule.setCreateTime(new Date());
        SytPermissionAccount user = SecurityUtils.getUser();
        dataWarningRule.setCreateUser(user.getHumanname());
        if (StringUtils.isNotBlank(dataWarningRule.getId())) {
            // 关闭定时任务
            QuartzManage.removeJob(QuartzUtil.getQuartzDTO(dataWarningRule.getId(), "", dataWarningRule.getRuleType()));
        }
        saveOrUpdate(dataWarningRule);
        //2.更新查询参数
        dto.setId(dataWarningRule.getId());
        dataSetParamBatch(dataSetParamDtoList, dto.getId());
        return dto;
    }

    @Override
    @Transactional
    public void removeBatch(List<String> asList) {
        QueryWrapper<DataWarningRule> ruleQueryWrapper = new QueryWrapper<>();
        ruleQueryWrapper.in("id", asList);
        List<DataWarningRule> dataWarningRules = dataWarningRuleMapper.selectList(ruleQueryWrapper);
        dataWarningRules.forEach(dataWarningRule->{
            // 关闭定时任务
            QuartzManage.removeJob(QuartzUtil.getQuartzDTO(dataWarningRule.getId(), "", dataWarningRule.getRuleType()));
        });
        removeByIds(asList);
        QueryWrapper<DataWarningRuleParam> wrapper = new QueryWrapper<>();
        wrapper.in("ruleCode", asList);
        dataWarningRuleParamService.remove(wrapper);
        // 删除预警信息名单、运行记录、特殊名单相关、预警级别
        QueryWrapper<DataWarningInfo> dataWarningInfoWrapper = new QueryWrapper<>();
        dataWarningInfoWrapper.in("ruleId", asList);
        dataWarningInfoService.remove(dataWarningInfoWrapper);
        QueryWrapper<DataWarningTaskRecord> dataWarningTaskRecordWrapper = new QueryWrapper<>();
        dataWarningTaskRecordWrapper.in("rulecode", asList);
        dataWarningTaskRecordService.remove(dataWarningTaskRecordWrapper);
        QueryWrapper<DataWarningSpecialList> dataWarningSpecialListWrapper = new QueryWrapper<>();
        dataWarningSpecialListWrapper.in("ruleid", asList);
        dataWarningSpecialListService.remove(dataWarningSpecialListWrapper);
        QueryWrapper<DataWarningLevel> dataWarningLevelWrapper = new QueryWrapper<>();
        dataWarningLevelWrapper.in("ruleId", asList);
        dataWarningLevelServiceImpl.remove(dataWarningLevelWrapper);
    }

    @Override
    public OriginalDataDto getData(DataSetDto dto) throws Exception {
        return dataProcessingService.getData(detailSet(dto));
    }

    @Override
    @Transactional
    public void modifyEnableFlag(DataWarningRule rule, CurrentUser user) {
        Integer enableFlag = rule.getEnableFlag();
        rule.setEnableFlag(null);
        QueryWrapper<DataWarningRule> dataWarningRuleQueryWrapper = buildWrapper(rule);
        if (DataScopeType.OWN_AND_CHILD_DEPT.getType().equals(user.getRoletype()) || DataScopeType.OWN_ONLY_DEPT.getType().equals(user.getRoletype())) {
            dataWarningRuleQueryWrapper.and(wrapper -> user.getOrganizations().forEach(organization -> wrapper.like("ORGANIZATIONS", organization.getId())));
        }
        List<DataWarningRule> list = this.list(dataWarningRuleQueryWrapper);
        list.forEach(r->{
            r.setEnableFlag(enableFlag);
            this.saveOrUpdate(r);
            switch (r.getEnableFlag()) {
                case 0: //启动
                    // 获取定时任务需要的参数
                    QuartzDTO quartzDTO = QuartzUtil.getQuartzDTO(r.getId(), r.getCheckCron(), r.getRuleType());
                    // 根据定时策略添加任务
                    if (StringUtils.isNotBlank(r.getCheckCron())) {
                        // 添加定时任务
                        QuartzManage.addCronJob(quartzDTO);
                    } else {
                        // 添加一次性任务
                        QuartzManage.addOnceJob(quartzDTO);
                    }
                    break;
                case 1: //停止
                    QuartzManage.removeJob(QuartzUtil.getQuartzDTO(r.getId(), "", r.getRuleType()));
                    break;
                default:
                    break;
            }
        });

    }

    @Override
    public void execute(DataWarningRule rule) {
        // 查询转换信息
        DataWarningRule warningRule = this.getById(rule.getId());
        if (warningRule == null) {
            throw new BusinessException("当前规则不存在");
        }
        QuartzDTO quartzDTO = QuartzUtil.getQuartzDTO(warningRule.getId(), warningRule.getCheckCron(), warningRule.getRuleType());
        QuartzManage.addOnceJob(quartzDTO);
    }

    @Override
    public List<DataSetDto> queryList(DataWarningRule query) {
        Wrapper<DataWarningRule> wrapper = buildWrapper(query);
        List<DataWarningRule> list = this.list(wrapper);
        List<DataSetDto> dataSetDtos = new LinkedList<>();
        list.forEach(rule -> {
            DataSetDto dataSetDto = new DataSetDto();
            BeanUtils.copyProperties(rule, dataSetDto);
            detailSet(dataSetDto);
            dataSetDtos.add(dataSetDto);
        });
        return dataSetDtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Resp replicateRule(String ruleid, String createUser) {
        // 查询到对应的规则
        DataWarningRule warningRule = this.getById(ruleid);
        if(warningRule == null){
            return Resp.error("规则不存在");
        }
        DataWarningRule dataWarningRule = new DataWarningRule();
        BeanUtils.copyProperties(warningRule, dataWarningRule, "id");
        dataWarningRule.setCreateUser(createUser);
        dataWarningRule.setCreateTime(new Date());
        save(dataWarningRule);
        // 复制特殊名单
        // 查询特殊名单
        DataWarningSpecialList dataWarningSpecialList = new DataWarningSpecialList();
        dataWarningSpecialList.setRuleid(ruleid);
        List<DataWarningSpecialList> list = dataWarningSpecialListService.getList(dataWarningSpecialList);
        List<DataWarningSpecialList> warningSpecialListData = new ArrayList<>();
        list.forEach(obj->{
            DataWarningSpecialList warningSpecialList = new DataWarningSpecialList();
            BeanUtils.copyProperties(obj, warningSpecialList, "id", "ruleid");
            warningSpecialList.setRuleid(dataWarningRule.getId());
            warningSpecialListData.add(warningSpecialList);
        });
        dataWarningSpecialListService.saveBatch(warningSpecialListData);

        return Resp.success();
    }

    public void dataSetParamBatch(List<DataSetParamDto> dataSetParamDtoList,String setId){
        if (StringUtils.isNoneBlank(setId)) {
            dataWarningRuleParamService.remove(new QueryWrapper<DataWarningRuleParam>().eq("RULECODE", setId));
        }
        if (null == dataSetParamDtoList || dataSetParamDtoList.size() <= 0) {
            return;
        }
        List<DataWarningRuleParam> dataSetParamList = new ArrayList<>();
        AtomicInteger i = new AtomicInteger();
        dataSetParamDtoList.forEach(dataSetParamDto -> {
            DataWarningRuleParam dataWarningRuleParam = new DataWarningRuleParam();
            BeanUtils.copyProperties(dataSetParamDto, dataWarningRuleParam);
            dataWarningRuleParam.setRuleCode(setId);
            dataWarningRuleParam.setCreateTime(new Date());
            dataWarningRuleParam.setOrderNum(i.getAndIncrement());
            dataSetParamList.add(dataWarningRuleParam);
        });
        dataWarningRuleParamService.saveBatch(dataSetParamList);
    }

    private QueryWrapper<DataWarningRule> buildWrapper(DataWarningRule query) {
        QueryWrapper<DataWarningRule> wrapper = new QueryWrapper<>();
        if (StringUtils.isNoneBlank(query.getSetName())) {
            wrapper.like("setName", query.getSetName());
        }
        if (StringUtils.isNoneBlank(query.getId())) {
            wrapper.eq("id", query.getId());
        }
        if (StringUtils.isNoneBlank(query.getRuleType())) {
            wrapper.eq("ruleType", query.getRuleType());
        }
        if (query.getEnableFlag()!=null) {
            wrapper.eq("enableFlag", query.getEnableFlag());
        }
//        wrapper.orderByDesc("createTime");
        wrapper.orderByAsc("enableFlag").orderByAsc("ruleType").orderByAsc("setName");

        return wrapper;
    }

}
