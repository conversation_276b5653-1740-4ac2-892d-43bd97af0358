package com.sanyth.service.ksgl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.base.CaseFormat;
import com.linuxense.javadbf.DBFField;
import com.linuxense.javadbf.DBFReader;
import com.sanyth.core.common.Constants;
import com.sanyth.core.support.Condition;
import com.sanyth.mapper.ksgl.DataImportsMapper;
import com.sanyth.model.ksgl.*;
import com.sanyth.model.zsxt.*;
import com.sanyth.service.zsxt.*;
import com.sanyth.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.text.SimpleDateFormat;
import java.util.*;


@Slf4j
@Service
public class DataImportsServiceImpl extends ServiceImpl<DataImportsMapper, DataImports> implements DataImportsService {

    @Resource
    private IBylbdmService bylbdmService;
    @Resource
    private ICjxdmService cjxdmService;
    @Resource
    private IDqdmService dqdmService;
    @Resource
    private IJhxzdmService jhxzdmService;
    @Resource
    private IJhlbdmService jhlbdmService ;
    @Resource
    private IKldmService kldmService;
    @Resource
    private IKslbdmService kslbdmService;
    @Resource
    private ILqfsdmService lqfsdmService;
    @Resource
    private IMzdmService mzdmService;
    @Resource
    private IPcdmService pcdmService;
    @Resource
    private IQbjhkService qbjhkService;
    @Resource
    private ITddwService tddwService;
    @Resource
    private ITdyydmService tdyydmService;
    @Resource
    private ITjjldmService tjjldmService;
    @Resource
    private IWyyzdmService wyyzdmService;
    @Resource
    private IXbdmService xbdmService;
    @Resource
    private IXtdwdmService xtdwdmService;
    @Resource
    private IZcdmService zcdmService;
    @Resource
    private IZytzdmService zytzdmService;
    @Resource
    private IZzmmdmService zzmmdmService;
    @Resource
    private ICcdmService ccdmService ;
    @Resource
    private IZydhService zydhService;
    @Resource
    private IJhkService jhkService;
    @Resource
    private IZykmxService zykmxService;
    @Resource
    private IZydhdmdzService zydhdmdzService;
    @Resource
    private IKsjlService ksjlService;
    @Resource
    private ITjxxService tjxxService;
    @Resource
    private IKstzdmService kstzdmService;
    @Resource
    private IKslxdmService kslxdmService;
    @Resource
    private ICjxglService cjxglService;
    @Resource
    private IZjlxdmService zjlxdmService;
    @Resource
    private ITdlxdmService tdlxdmService;
    @Resource
    private IZylbdmService zylbdmService;
    @Resource
    private IHjlbdmService hjlbdmService;
    @Resource
    private ICzlbdmService czlbdmService;
    @Resource
    private IBmdwdmService bmdwdmService;
    @Resource
    private ITddService tddService;
    @Resource
    private ITddGkcjxService gkcjxService;

    /**
     * 检查DBF记录是否为空行
     * @param rowValues DBF行数据
     * @return true 如果是空行，false 如果包含有效数据
     */
    private boolean isEmptyRow(Object[] rowValues) {
        if (rowValues == null || rowValues.length == 0) {
            log.debug("检测到空行：rowValues为null或长度为0");
            return true;
        }
        
        // 检查是否所有字段都为null或空字符串
        for (Object value : rowValues) {
            if (value != null && !value.toString().trim().isEmpty()) {
                return false;
            }
        }
        log.debug("检测到空行：所有字段都为null或空字符串");
        return true;
    }

    @Override
    public void clearCodeData(Tdd tdd) {
        Bylbdm bylbdm = new Bylbdm();
        bylbdm.setNf(tdd.getNf());
        bylbdmService.remove(Condition.getQueryWrapper(bylbdm));
        Cjxdm cjxdm = new Cjxdm();
        cjxdm.setNf(tdd.getNf());
        cjxdm.setSfdm(tdd.getSfdm());
        cjxdmService.remove(Condition.getQueryWrapper(cjxdm));
        Dqdm dqdm = new Dqdm();
        dqdm.setNf(tdd.getNf());
        dqdmService.remove(Condition.getQueryWrapper(dqdm));
        Jhxzdm jhxzdm = new Jhxzdm();
        jhxzdm.setNf(tdd.getNf());
        jhxzdm.setSfdm(tdd.getSfdm());
        jhxzdmService.remove(Condition.getQueryWrapper(jhxzdm));
        Jhlbdm jhlbdm = new Jhlbdm();
        jhlbdm.setNf(tdd.getNf());
        jhlbdm.setSfdm(tdd.getSfdm());
        jhlbdmService.remove(Condition.getQueryWrapper(jhlbdm));
        Kldm kldm = new Kldm();
        kldm.setNf(tdd.getNf());
        kldm.setSfdm(tdd.getSfdm());
        kldmService.remove(Condition.getQueryWrapper(kldm));
        Kslbdm kslbdm = new Kslbdm();
        kslbdm.setNf(tdd.getNf());
        kslbdmService.remove(Condition.getQueryWrapper(kslbdm));
        Kslxdm kslxdm = new Kslxdm();
        kslxdm.setNf(tdd.getNf());
        kslxdm.setSfdm(tdd.getSfdm());
        kslxdmService.remove(Condition.getQueryWrapper(kslxdm));
        Lqfsdm lqfsdm = new Lqfsdm();
        lqfsdm.setNf(tdd.getNf());
        lqfsdm.setSfdm(tdd.getSfdm());
        lqfsdmService.remove(Condition.getQueryWrapper(lqfsdm));
        Mzdm mzdm = new Mzdm();
        mzdm.setNf(tdd.getNf());
        mzdmService.remove(Condition.getQueryWrapper(mzdm));
        Pcdm pcdm = new Pcdm();
        pcdm.setNf(tdd.getNf());
        pcdm.setSfdm(tdd.getSfdm());
        pcdmService.remove(Condition.getQueryWrapper(pcdm));
        Tdyydm tdyydm = new Tdyydm();
        tdyydm.setNf(tdd.getNf());
        tdyydm.setSfdm(tdd.getSfdm());
        tdyydmService.remove(Condition.getQueryWrapper(tdyydm));
        Tdlxdm tdlxdm = new Tdlxdm();
        tdlxdm.setNf(tdd.getNf());
        tdlxdm.setSfdm(tdd.getSfdm());
        tdlxdmService.remove(Condition.getQueryWrapper(tdlxdm));
        Tjjldm tjjldm = new Tjjldm();
        tjjldm.setNf(tdd.getNf());
        tjjldmService.remove(Condition.getQueryWrapper(tjjldm));
        Wyyzdm wyyzdm = new Wyyzdm();
        wyyzdm.setNf(tdd.getNf());
        wyyzdmService.remove(Condition.getQueryWrapper(wyyzdm));
        Xbdm xbdm = new Xbdm();
        xbdm.setNf(tdd.getNf());
        xbdmService.remove(Condition.getQueryWrapper(xbdm));
        Xtdwdm xtdwdm = new Xtdwdm();
        xtdwdm.setNf(tdd.getNf());
        xtdwdmService.remove(Condition.getQueryWrapper(xtdwdm));
        Zcdm zcdm = new Zcdm();
        zcdm.setNf(tdd.getNf());
        zcdmService.remove(Condition.getQueryWrapper(zcdm));
        Zytzdm zytzdm = new Zytzdm();
        zytzdm.setNf(tdd.getNf());
        zytzdmService.remove(Condition.getQueryWrapper(zytzdm));
        Zzmmdm zzmmdm = new Zzmmdm();
        zzmmdm.setNf(tdd.getNf());
        zzmmdmService.remove(Condition.getQueryWrapper(zzmmdm));
        Ccdm ccdm = new Ccdm();
        ccdm.setNf(tdd.getNf());
        ccdm.setSfdm(tdd.getSfdm());
        ccdmService.remove(Condition.getQueryWrapper(ccdm));
        Zydh zydh = new Zydh();
        zydh.setNf(tdd.getNf());
        zydh.setSfdm(tdd.getSfdm());
        zydhService.remove(Condition.getQueryWrapper(zydh));
        Jhk jhk = new Jhk();
        jhk.setNf(tdd.getNf());
        jhk.setSfdm(tdd.getSfdm());
        jhkService.remove(Condition.getQueryWrapper(jhk));
        Zydhdmdz zydhdmdz = new Zydhdmdz();
        zydhdmdz.setNf(tdd.getNf());
        zydhdmdz.setSfdm(tdd.getSfdm());
        zydhdmdzService.remove(Condition.getQueryWrapper(zydhdmdz));
        Zylbdm zylbdm = new Zylbdm();
        zylbdm.setNf(tdd.getNf());
        zylbdm.setSfdm(tdd.getSfdm());
        zylbdmService.remove(Condition.getQueryWrapper(zylbdm));
        Zykmx zykmx = new Zykmx();
        zykmx.setNf(tdd.getNf());
        zykmx.setSfdm(tdd.getSfdm());
        zykmxService.remove(Condition.getQueryWrapper(zykmx));
        Kstzdm kstzdm = new Kstzdm();
        kstzdm.setNf(tdd.getNf());
        kstzdm.setSfdm(tdd.getSfdm());
        kstzdmService.remove(Condition.getQueryWrapper(kstzdm));
        Zjlxdm zjlxdm = new Zjlxdm();
        zjlxdm.setNf(tdd.getNf());
        zjlxdmService.remove(Condition.getQueryWrapper(zjlxdm));
        Hjlbdm hjlbdm = new Hjlbdm();
        hjlbdm.setNf(tdd.getNf());
        hjlbdm.setSfdm(tdd.getSfdm());
        hjlbdmService.remove(Condition.getQueryWrapper(hjlbdm));
        Czlbdm czlbdm = new Czlbdm();
        czlbdm.setNf(tdd.getNf());
        czlbdm.setSfdm(tdd.getSfdm());
        czlbdmService.remove(Condition.getQueryWrapper(czlbdm));
        Bmdwdm bmdwdm = new Bmdwdm();
        bmdwdm.setNf(tdd.getNf());
        bmdwdm.setSfdm(tdd.getSfdm());
        bmdwdmService.remove(Condition.getQueryWrapper(bmdwdm));
        Qbjhk qbjhk = new Qbjhk();
        qbjhk.setNf(tdd.getNf());
        qbjhk.setSfdm(tdd.getSfdm());
        qbjhkService.remove(Condition.getQueryWrapper(qbjhk));
        Tddw tddw = new Tddw();
        tddw.setNf(tdd.getNf());
        tddw.setSfdm(tdd.getSfdm());
        tddwService.remove(Condition.getQueryWrapper(tddw));
        Ksjl ksjl = new Ksjl();
        ksjl.setNf(tdd.getNf());
        ksjlService.remove(Condition.getQueryWrapper(ksjl));
        Tjxx tjxx = new Tjxx();
        tjxx.setNf(tdd.getNf());
        tjxxService.remove(Condition.getQueryWrapper(tjxx));
    }

    @Override
    public void importExcelData(DataImports imports,String key,List<LinkedHashMap<String, Object>> list) {
        if (StringUtils.isEmpty(key)) {
            throw new RuntimeException("获取sheet名失败");
        }
        switch (key.toUpperCase()) {
            case "TD_BYLBDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    bylbdmService.importData(imports, array);
                });
                break;
            case "TD_CJXDM":
                List<Cjxdm> cjxdms = new ArrayList<>();
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    cjxdmService.importData(imports, array,cjxdms);
                });
                //保存cjxgl
                Cjxgl cjxgl = new Cjxgl();
                cjxgl.setNf(imports.getNf());
                cjxgl.setSfdm(imports.getSfdm());
                cjxgl.setSfmc(imports.getSf());
                cjxgl.setCjzf("cj");
                cjxdms.forEach(cjxdm -> {
                    if (cjxdm.getName().contains("语文")) {
                        cjxgl.setCjyw("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("数学")) {
                        cjxgl.setCjsx("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("外语")) {
                        cjxgl.setCjwy("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("政治")) {
                        cjxgl.setCjzhzh("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("历史")) {
                        cjxgl.setCjls("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("地理")) {
                        cjxgl.setCjdl("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("物理")) {
                        cjxgl.setCjwl("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("化学")) {
                        cjxgl.setCjhx("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("生物")) {
                        cjxgl.setCjsw("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("技术")) {
                        cjxgl.setCjjsh("gkcjx" + cjxdm.getCode());
                    }
                });
                if (cjxglService.count(Condition.getQueryWrapper(cjxgl)) == 0) {
                    cjxglService.save(cjxgl);
                }
                break;
            case "TD_DQDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    dqdmService.importData(imports, array);
                });
                break;
            case "TD_JHXZDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    jhxzdmService.importData(imports, array);
                });
                break;
            case "TD_JHLBDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    jhlbdmService.importData(imports, array);
                });
                break;
            case "TD_KLDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    kldmService.importData(imports, array);
                });
                break;
            case "TD_KSLBDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    kslbdmService.importData(imports, array);
                });
                break;
            case "TD_KSLXDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    kslxdmService.importData(imports, array);
                });
                break;
            case "TD_LQFSDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    lqfsdmService.importData(imports, array);
                });
                break;
            case "TD_MZDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    mzdmService.importData(imports, array);
                });
                break;
            case "TD_PCDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    pcdmService.importData(imports, array);
                });
                break;
            case "TD_TDYYDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    tdyydmService.importData(imports, array);
                });
                break;
            case "TD_TDLXDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    tdlxdmService.importData(imports, array);
                });
                break;
            case "TD_TJJLDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    tjjldmService.importData(imports, array);
                });
                break;
            case "TD_WYYZDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    wyyzdmService.importData(imports, array);
                });
                break;
            case "TD_XBDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    xbdmService.importData(imports, array);
                });
                break;
            case "TD_XTDWDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    xtdwdmService.importData(imports, array);
                });
                break;
            case "TD_ZCDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    zcdmService.importData(imports, array);
                });
                break;
            case "TD_ZYTZDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    zytzdmService.importData(imports, array);
                });
                break;
            case "TD_ZZMMDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    zzmmdmService.importData(imports, array);
                });
                break;
            case "TD_CCDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    ccdmService.importData(imports, array);
                });
                break;
            case "TD_ZYDH":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    zydhService.importData(imports, array);
                });
                break;
            case "T_JHK":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    jhkService.importData(imports, array);
                });
                break;
            case "TD_ZYDHDMDZ":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    zydhdmdzService.importData(imports, array);
                });
                break;
            case "TD_ZYLBDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    zylbdmService.importData(imports, array);
                });
                break;
            case "TD_KSTZDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    kstzdmService.importData(imports, array);
                });
                break;
            case "TD_ZJLXDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    zjlxdmService.importData(imports, array);
                });
                break;
            case "TD_HJLBDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    hjlbdmService.importData(imports, array);
                });
                break;
            case "TD_CZLBDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    czlbdmService.importData(imports, array);
                });
                break;
            case "TD_BMDWDM":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    bmdwdmService.importData(imports, array);
                });
                break;
            case "T_QBJHK":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    qbjhkService.importData(imports,array);
                });
                break;
            case "T_TDDW":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    tddwService.importData(imports, array);
                });
                break;
            case "T_ZYKMX":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    zykmxService.importData(imports, array);
                });
                break;
            case "T_KSJL":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    ksjlService.importData(imports, array);
                });
                break;
            case "T_TJXX":
                list.forEach(map->{
                    Object[] array = map.values().toArray();
                    tjxxService.importData(imports, array);
                });
                break;
            default:
                break;
        }
    }

    @Override
    public void importExcelTDDData(DataImports imports,List<LinkedHashMap<String, Object>> list, Map<String, List<?>> tdMap) {
        if (CollectionUtils.isEmpty(list)) {
            throw new RuntimeException("获取数据失败");
        }
        Map<String, Object> oneaMap = list.get(0);
        Result result = getResult(imports, tdMap);
        list.forEach(row -> {
            Tdd tdd = new Tdd();
            for (String name : oneaMap.keySet()) {
                String value = row.get(name) != null ? row.get(name).toString() : "";
                name = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, name);
                if (name.contains("gkcjx")) {
                    TddGkcjx cjx = new TddGkcjx();
                    cjx.setKsh(tdd.getKsh());
                    cjx.setCode(name.substring(5));
                    cjx.setNf(String.valueOf(DateUtil.thisYear()));
                    cjx.setSfdm(imports.getSfdm());
                    cjx.setSfmc(imports.getSf());
                    cjx.setName(result.cjxdmMap.get(cjx.getCode() + "," + imports.getSfdm()));
                    cjx.setKldm(tdd.getKldm());
                    cjx.setFielden(name);
                    cjx.setValue(value);
                    TddGkcjx s=new TddGkcjx();
                    s.setCode(cjx.getCode());
                    s.setKsh(cjx.getKsh());
                    s.setKldm(cjx.getKldm());
                    s.setSfdm(cjx.getSfdm());
                    s.setSfmc(cjx.getSfmc());
                    if (gkcjxService.count(Condition.getQueryWrapper(s)) == 0) {
                        gkcjxService.save(cjx);
                    }
                    String skkm = tdd.getSkkm();
                    String skkmmc = "";
                    if (StringUtils.isNotBlank(skkm)) {
                        String[] split = skkm.split("\\*");
                        for (String s1 : split) {
                            String s2 = result.cjxdmMap.get(s1 + "," + imports.getSfdm());
                            skkmmc += s2 + "*";
                        }
                        skkmmc = skkmmc.substring(0, skkmmc.length() - 1);
                        tdd.setXkkm(skkmmc);
                    }
                } else {
                    try {
                        BeanUtil.setProperty(tdd, name, value);

                        if("csny".equals(name) && StringUtils.isNotBlank(tdd.getCsny())){
                            tdd.setCsny(DateUtil.format(DateUtils.parseDate(tdd.getCsny()), "yyyy-MM-dd"));
                        } else if("csrq".equals(name) && StringUtils.isNotBlank(tdd.getCsrq())){
                            tdd.setCsrq(DateUtil.format(DateUtils.parseDate(tdd.getCsrq()), "yyyy-MM-dd"));
                        } else if("tdsj".equals(name) && StringUtils.isNotBlank(tdd.getTdsj())){
                            tdd.setTdsj(DateUtil.format(DateUtils.parseDate(tdd.getTdsj()), "yyyy-MM-dd HH:mm:ss"));
                        } else if("lqsj".equals(name) && StringUtils.isNotBlank(tdd.getLqsj())){
                            tdd.setLqsj(DateUtil.format(DateUtils.parseDate(tdd.getLqsj()), "yyyy-MM-dd HH:mm:ss"));
                        }
                        //lqzy匹配td_zydh.dbf中的zydh和ccdm， ccdm匹配td_ccdm.dbf中的ccmc,zkfx存tdd里的lqzkfx
                        else if ("lqzy".equals(name)) {
                            Zydh zydh = result.zydhMap.get(tdd.getLqzy()+tdd.getKldm());
                            Jhk jhk = result.jhkMap.get(tdd.getLqzy() + tdd.getKldm());
                            String zkfx = "";
                            String zydhStr = "";
                            String zymcStr = "";
                            String ccdm = "";
                            if (zydh != null) {
                                zkfx = zydh.getZkfx();
                                zydhStr = zydh.getZydh();
                                zymcStr = zydh.getZymc();
                                ccdm = zydh.getCcdm();
                            } else if (jhk != null) {
                                zydhStr = jhk.getZydh();
                                zymcStr = jhk.getZymc();
                            }
                            tdd.setLqzkfx(zkfx);
                            tdd.setLqzydm(result.zydhdmdzMap.get(zydhStr));
                            tdd.setZydm(result.zydhdmdzMap.get(zydhStr));
                            tdd.setLqzymc(zymcStr);

                            if (StringUtils.isNotEmpty(imports.getCcmc())) {
                                tdd.setCc(imports.getCcmc());
                            } else {
                                tdd.setCcdm(ccdm);
                                tdd.setCc(result.ccMap.get(ccdm));
                            }
//                                tdd.setXyxz(zydh.getXzdm());
                            try {
                                result.zykmxMap.get(tdd.getKsh()).forEach(zykmx -> {
                                    if (zykmx != null) {
                                        //根据lqzy从专业志愿卡得到录取第几志愿录取
                                        if (Objects.equals(tdd.getLqzy(), zykmx.getZydh())) {
                                            tdd.setKslqzy(zykmx.getZyxh());
                                        }
                                        //根据ytzy从专业志愿卡得到报考第几志愿录取
                                        if (Objects.equals(tdd.getLqzy(), zykmx.getZydh())) {
                                            tdd.setKsytzy(zykmx.getZyxh());
                                        }
                                        Zydh zydh1 = result.zydhMap.get(zykmx.getZydh() + zykmx.getKldm());
                                        String zydhh = "";
                                        String zymc = "";
                                        if (zydh1 != null) {
                                            zydhh = zydh1.getZydh();
                                            zymc = zydh1.getZymc();
                                        }
                                        BeanUtil.setProperty(tdd, "zydh" + zykmx.getZyxh().replaceAll("\\..*", ""), zydhh);
                                        BeanUtil.setProperty(tdd, "zyzy" + zykmx.getZyxh().replaceAll("\\..*", ""), zymc);
                                    }

                                });
                            } catch (Exception e) {
                                log.info("zykmx异常");
                            }

                            if (Constants.HAS_YES.equals(imports.getZdxz())) {
                                String key = StringUtils.isEmpty(tdd.getCc()) ?
                                        tdd.getLqzymc() + ("null".equals(tdd.getLqzkfx()) ? "" : "," + tdd.getLqzkfx()) :
                                        tdd.getLqzymc() + "," + tdd.getCc() + "," + ("null".equals(tdd.getLqzkfx()) ? "" : tdd.getLqzkfx());
                                XyzyRules xyzyRules = null;
                                for (Map.Entry<String, XyzyRules> rulesEntry : result.xyzyRulesMap.entrySet()) {
                                    if (rulesEntry.getKey().startsWith(key)) {
                                        xyzyRules = rulesEntry.getValue();
                                    }
                                }

                                if (xyzyRules != null) {
                                    tdd.setXydm(xyzyRules.getXydm());
                                    tdd.setXymc(xyzyRules.getXymc());
                                    tdd.setZydm(xyzyRules.getGjzydm());
                                    tdd.setZymc(xyzyRules.getXnzymc());
                                    tdd.setGjzydm(xyzyRules.getGjzydm());
                                    tdd.setZyxq(xyzyRules.getXq());
                                    tdd.setZysfsf(xyzyRules.getSfsf());
                                    tdd.setZytjsx(xyzyRules.getTjsx());
                                    tdd.setZybddd(xyzyRules.getBddd());
                                    tdd.setZyrxrq(xyzyRules.getRxrq());
                                    tdd.setXyxz(xyzyRules.getXz());
                                }
                            }

                            Zydh ytzydh = result.zydhMap.get(tdd.getYtzy()+tdd.getKldm());
                            Jhk ytjhk = result.jhkMap.get(tdd.getYtzy() + tdd.getKldm());
                            if (ytzydh != null) {
                                tdd.setYtzymc(zydh.getZymc());
                            } else if (ytjhk != null) {
                                tdd.setYtzymc(jhk.getZymc());
                            }
                        }
                        else if ("tddw".equals(name) || "tddwdm".equals(name)) {
//                            tdd.setTddwmc(result.tddwMap.get(tdd.getPcdm() + "," + tdd.getKldm() + "," + tdd.getTddwdm()));
                            tdd.setTddwmc(result.tddwMap.get(tdd.getTddwdm()));
                        } else if ("kldm".equals(name) && imports.getFileName().contains("T_TDD")) {
                            tdd.setKlmc(result.kldmMap.get(tdd.getKldm()));
                        } else if("pcdm".equals(name)){
                            tdd.setPcmc(result.pcdmMap.get(tdd.getPcdm()));
                        } else if ("kslbdm".equals(name)) {
                            tdd.setKslbmc(result.kslbMap.get(tdd.getKslbdm()));
                        } else if ("bylbdm".equals(name)) {
                            tdd.setBylbmc(result.bylbMap.get(tdd.getBylbdm()));
                        }
                        /*else if ("byxxdm".equals(name)) {
                            tdd.setByxxdz(byxxMap.get(tdd.getByxxdm()));
                        }*/
                        else if ("jhlbdm".equals(name)) {
                            tdd.setJhlbmc(result.jhlbMap.get(tdd.getJhlbdm()));
                        } else if ("jhxzdm".equals(name)) {
                            tdd.setJhxz(result.jhxzdmMap.get(tdd.getJhxzdm()));
                        } else if ("dqdm".equals(name)) {
                            tdd.setDqmc(result.dqdmMap.get(tdd.getDqdm()));
                        } else if ("hjdm".equals(name)) {
                            tdd.setHjmc(result.dqdmMap.get(tdd.getHjdm()));
                        } else if ("xbdm".equals(name)) {
                            tdd.setXbmc(result.xbdmMap.get(tdd.getXbdm()));
                        } else if ("zzmmdm".equals(name)) {
                            tdd.setZzmmmc(result.zzmmdmMap.get(tdd.getZzmmdm()));
                        } else if ("mzdm".equals(name)) {
                            tdd.setMzmc(result.mzdmMap.get(tdd.getMzdm()));
                        } else if ("kstz".equals(name)) {
                            tdd.setKstzmc(result.kstzdmMap.get(tdd.getKstz()));
                        } else if ("kslxdm".equals(name)) {
                            tdd.setKslxmc(result.kslxdmMap.get(tdd.getKslxdm()));
                        } else if ("lqlxdm".equals(name)) {
                            tdd.setLqlxmc(result.kslxdmMap.get(tdd.getLqlxdm()));
                        } else if ("zjlxdm".equals(name)) {
                            tdd.setZjlxmc(result.zjlxdmMap.get(tdd.getZjlxdm()));
                        } else if ("tdyydm".equals(name)) {
                            tdd.setTdyymc(result.tdyydmMap.get(tdd.getTdyydm()));
                        } else if ("tdlxdm".equals(name)) {
                            tdd.setTdlxmc(result.tdlxdmMap.get(tdd.getTdlxdm()));
                        } else if ("wyyzdm".equals(name)) {
                            tdd.setWyyzmc(result.wyyzdmMap.get(tdd.getWyyzdm()));
                        } else if ("hjlbdm".equals(name)) {
                            tdd.setHjlbmc(result.hjlbdmMap.get(tdd.getHjlbdm()));
                        } else if ("czlbdm".equals(name)) {
                            tdd.setCzlbmc(result.czlbdmMap.get(tdd.getCzlbdm()));
                        } else if ("ysjzdm".equals(name)) {
                            tdd.setYsjzmc(result.mzdmMap.get(tdd.getYsjzdm()));
                        } else if ("bmdw".equals(name)) {
                            tdd.setBmdwmc(result.bmdwdmMap.get(tdd.getBmdw()));
                        } else if ("txdz".equals(name)) {
                            tdd.setLqtzsyjdz(tdd.getTxdz());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }

            tdd.setNf(imports.getNf());
            tdd.setSfdm(imports.getSfdm());
            tdd.setSfmc(imports.getSf());
            tdd.setZslx(imports.getZslx());
            tdd.setBdzt(Constants.BDZT_DQR);

            Tdd one = new Tdd();
            one.setNf(imports.getNf());
            one.setKsh(tdd.getKsh());
            one.setSfdm(imports.getSfdm());
            one.setSfmc(imports.getSf());
            if (tddService.count(Condition.getQueryWrapper(one)) > 0) {
                Tdd tdd1 = tddService.getOne(Condition.getQueryWrapper(one));
                BeanUtils.copyProperties(tdd, tdd1, "id");
                tddService.saveOrUpdate(tdd1);
            } else {
                tddService.save(tdd);
            }
        });

    }

    @Override
    public void importDBFData(DataImports imports, File file) throws Exception {
        String nf = imports.getNf();
        // 根据输入流初始化一个DBFReader实例，用来读取DBF文件信息
        DBFReader reader = new DBFReader(new FileInputStream(file));
        reader.setCharactersetName("GBK");
        Object[] rowValues=null;
        String tableName = file.getName().split("\\.")[0];
        if (StringUtils.isEmpty(tableName)) {
            throw new RuntimeException("dbf文件名获取失败");
        }

        switch (tableName.toUpperCase()) {
            case "TD_BYLBDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    bylbdmService.importData(imports, rowValues);
                }
                break;
            case "TD_CJXDM":
                List<Cjxdm> cjxdms = new ArrayList<>();
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    cjxdmService.importData(imports, rowValues,cjxdms);
                }
                //保存cjxgl
                Cjxgl cjxgl = new Cjxgl();
                cjxgl.setNf(imports.getNf());
                cjxgl.setSfdm(imports.getSfdm());
                cjxgl.setSfmc(imports.getSf());
                cjxgl.setCjzf("cj");
                cjxdms.forEach(cjxdm -> {
                    if (cjxdm.getName().contains("语文")) {
                        cjxgl.setCjyw("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("数学")) {
                        cjxgl.setCjsx("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("外语")) {
                        cjxgl.setCjwy("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("政治")) {
                        cjxgl.setCjzhzh("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("历史")) {
                        cjxgl.setCjls("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("地理")) {
                        cjxgl.setCjdl("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("物理")) {
                        cjxgl.setCjwl("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("化学")) {
                        cjxgl.setCjhx("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("生物")) {
                        cjxgl.setCjsw("gkcjx" + cjxdm.getCode());
                    }
                    if (cjxdm.getName().contains("技术")) {
                        cjxgl.setCjjsh("gkcjx" + cjxdm.getCode());
                    }
                });
                if (cjxglService.count(Condition.getQueryWrapper(cjxgl)) == 0) {
                    cjxglService.save(cjxgl);
                }
                break;
            case "TD_DQDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    dqdmService.importData(imports, rowValues);
                }
                break;
            case "TD_JHXZDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    jhxzdmService.importData(imports, rowValues);
                }
                break;
            case "TD_JHLBDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    jhlbdmService.importData(imports, rowValues);
                }
                break;
            case "TD_KLDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    kldmService.importData(imports, rowValues);
                }
                break;
            case "TD_KSLBDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    kslbdmService.importData(imports, rowValues);
                }
                break;
            case "TD_KSLXDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    kslxdmService.importData(imports, rowValues);
                }
                break;
            case "TD_LQFSDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    lqfsdmService.importData(imports, rowValues);
                }
                break;
            case "TD_MZDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    mzdmService.importData(imports, rowValues);
                }
                break;
            case "TD_PCDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    pcdmService.importData(imports, rowValues);
                }
                break;
            case "TD_TDYYDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    tdyydmService.importData(imports, rowValues);
                }
                break;
            case "TD_TDLXDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    tdlxdmService.importData(imports, rowValues);
                }
                break;
            case "TD_TJJLDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    tjjldmService.importData(imports, rowValues);
                }
                break;
            case "TD_WYYZDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    wyyzdmService.importData(imports, rowValues);
                }
                break;
            case "TD_XBDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    xbdmService.importData(imports, rowValues);
                }
                break;
            case "TD_XTDWDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    xtdwdmService.importData(imports, rowValues);
                }
                break;
            case "TD_ZCDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    zcdmService.importData(imports, rowValues);
                }
                break;
            case "TD_ZYTZDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    zytzdmService.importData(imports, rowValues);
                }
                break;
            case "TD_ZZMMDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    zzmmdmService.importData(imports, rowValues);
                }
                break;
            case "TD_CCDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    ccdmService.importData(imports, rowValues);
                }
                break;
            case "TD_ZYDH":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    zydhService.importData(imports, rowValues);
                }
                break;
            case "T_JHK":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    jhkService.importData(imports, rowValues);
                }
                break;
            case "TD_ZYDHDMDZ":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    zydhdmdzService.importData(imports, rowValues);
                }
                break;
            case "TD_ZYLBDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    zylbdmService.importData(imports, rowValues);
                }
                break;
            case "TD_KSTZDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    kstzdmService.importData(imports, rowValues);
                }
                break;
            case "TD_ZJLXDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    zjlxdmService.importData(imports, rowValues);
                }
                break;
            case "TD_HJLBDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    hjlbdmService.importData(imports, rowValues);
                }
                break;
            case "TD_CZLBDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    czlbdmService.importData(imports, rowValues);
                }
                break;
            case "TD_BMDWDM":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    bmdwdmService.importData(imports, rowValues);
                }
                break;
            case "T_QBJHK":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    qbjhkService.importData(imports,rowValues);
                }
                break;
            case "T_TDDW":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    tddwService.importData(imports, rowValues);
                }
                break;
            case "T_ZYKMX":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    zykmxService.importData(imports, rowValues);
                }
                break;
            case "T_KSJL":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    ksjlService.importData(imports, rowValues);
                }
                break;
            case "T_TJXX":
                while ((rowValues = reader.nextRecord()) != null) {
                    if (isEmptyRow(rowValues)) {
                        continue;
                    }
                    tjxxService.importData(imports, rowValues);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void importDBFTDDData(DataImports imports, File file, Map<String, List<?>> tdMap) throws Exception {
        Result result = getResult(imports, tdMap);
        // 根据输入流初始化一个DBFReader实例，用来读取DBF文件信息
        DBFReader reader = new DBFReader(new FileInputStream(file));
        reader.setCharactersetName("GBK");
        // 调用DBFReader对实例方法得到path文件中字段的个数
        int fieldsCount = reader.getFieldCount();
        Object[] rowValues=null;
        while ((rowValues = reader.nextRecord()) != null) {
            if (isEmptyRow(rowValues)) {
                continue;
            }
            Tdd tdd = new Tdd();
            for (int i = 0; i < fieldsCount; i++) {
                DBFField field = reader.getField(i);
                String name = field.getName().toLowerCase();
                name = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL, name);
                if (name.contains("gkcjx")) {
                    TddGkcjx cjx = new TddGkcjx();
                    cjx.setKsh(tdd.getKsh());
                    cjx.setCode(name.substring(5));
                    cjx.setNf(String.valueOf(DateUtil.thisYear()));
                    cjx.setSfdm(imports.getSfdm());
                    cjx.setSfmc(imports.getSf());
                    cjx.setName(result.cjxdmMap.get(cjx.getCode() + "," + imports.getSfdm()));
                    cjx.setKldm(tdd.getKldm());
                    cjx.setFielden(name);
                    String value=rowValues[i]!=null?rowValues[i].toString():"";
                    cjx.setValue(value);
                    TddGkcjx s=new TddGkcjx();
                    s.setCode(cjx.getCode());
                    s.setKsh(cjx.getKsh());
                    s.setKldm(cjx.getKldm());
                    s.setSfdm(cjx.getSfdm());
                    s.setSfmc(cjx.getSfmc());
                    if (gkcjxService.count(Condition.getQueryWrapper(s)) == 0) {
                        gkcjxService.save(cjx);
                    }
                    String skkm = tdd.getSkkm();
                    String skkmmc = "";
                    if (StringUtils.isNotBlank(skkm)) {
                        String[] split = skkm.split("\\*");
                        for (String s1 : split) {
                            String s2 = result.cjxdmMap.get(s1 + "," + imports.getSfdm());
                            skkmmc += s2 + "*";
                        }
                        skkmmc = skkmmc.substring(0, skkmmc.length() - 1);
                        tdd.setXkkm(skkmmc);
                    }
                } else {
                    try {
                        BeanUtil.setProperty(tdd, name, rowValues[i] != null ? rowValues[i].toString().trim() : "");

                        if ("sfzh".equals(name)) {
                            tdd.setZjhm(tdd.getSfzh());
                        } else if("csny".equals(name)){
                            SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
                            Date parse = sdf.parse(tdd.getCsny());
                            tdd.setCsny(DateUtil.format(parse, "yyyy-MM-dd"));
                        } else if("csrq".equals(name)){
                            SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
                            Date parse = sdf.parse(tdd.getCsrq());
                            tdd.setCsrq(DateUtil.format(parse, "yyyy-MM-dd"));
                        } else if("tdsj".equals(name)){
                            SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
                            Date parse = sdf.parse(tdd.getTdsj());
                            tdd.setTdsj(DateUtil.format(parse, "yyyy-MM-dd HH:mm:ss"));
                        } else if("lqsj".equals(name)){
                            SimpleDateFormat sdf = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.US);
                            Date parse = sdf.parse(tdd.getLqsj());
                            tdd.setLqsj(DateUtil.format(parse, "yyyy-MM-dd HH:mm:ss"));
                        }
                        //lqzy匹配td_zydh.dbf中的zydh和ccdm， ccdm匹配td_ccdm.dbf中的ccmc,zkfx存tdd里的lqzkfx
                        else if ("lqzy".equals(name)) {
                            Zydh zydh = result.zydhMap.get(tdd.getLqzy()+tdd.getKldm());
                            Jhk jhk = result.jhkMap.get(tdd.getLqzy() + tdd.getKldm());
                            String zkfx = "";
                            String zydhStr = "";
                            String zymcStr = "";
                            String ccdm = "";
                            if (zydh != null) {
                                zkfx = zydh.getZkfx();
                                zydhStr = zydh.getZydh();
                                zymcStr = zydh.getZymc();
                                ccdm = zydh.getCcdm();
                            } else if (jhk != null) {
                                zydhStr = jhk.getZydh();
                                zymcStr = jhk.getZymc();
                            }
                            tdd.setLqzkfx(zkfx);
                            tdd.setLqzydm(result.zydhdmdzMap.get(zydhStr));
                            tdd.setZydm(result.zydhdmdzMap.get(zydhStr));
                            tdd.setLqzymc(zymcStr);
                            if (StringUtils.isNotEmpty(imports.getCcmc())) {
                                tdd.setCc(imports.getCcmc());
                            } else {
                                tdd.setCcdm(ccdm);
                                tdd.setCc(result.ccMap.get(ccdm));
                            }
//                                tdd.setXyxz(zydh.getXzdm());
                            try {
                                result.zykmxMap.get(tdd.getKsh()).forEach(zykmx -> {
                                    String zymc = result.zydhMap.get(zykmx.getZydh() + zykmx.getKldm()).getZymc();
                                    BeanUtil.setProperty(tdd, "zydh" + zykmx.getZyxh().replaceAll("\\..*", ""), zymc);
                                });
                            } catch (Exception e) {
                                log.info("zykmx异常");
                            }

                            if (Constants.HAS_YES.equals(imports.getZdxz())) {
                                String key = StringUtils.isEmpty(tdd.getCc()) ?
                                        tdd.getLqzymc() + ("null".equals(tdd.getLqzkfx()) ? "" : "," + tdd.getLqzkfx()) :
                                        tdd.getLqzymc() + "," + tdd.getCc() + "," + ("null".equals(tdd.getLqzkfx()) ? "" : tdd.getLqzkfx());
                                XyzyRules xyzyRules = null;
                                for (Map.Entry<String, XyzyRules> rulesEntry : result.xyzyRulesMap.entrySet()) {
                                    if (rulesEntry.getKey().startsWith(key)) {
                                        xyzyRules = rulesEntry.getValue();
                                        break;
                                    }
                                }
                                if (xyzyRules != null) {
                                    tdd.setXydm(xyzyRules.getXydm());
                                    tdd.setXymc(xyzyRules.getXymc());
                                    tdd.setZydm(xyzyRules.getGjzydm());
                                    tdd.setZymc(xyzyRules.getXnzymc());
                                    tdd.setGjzydm(xyzyRules.getGjzydm());
                                    tdd.setZyxq(xyzyRules.getXq());
                                    tdd.setZysfsf(xyzyRules.getSfsf());
                                    tdd.setZytjsx(xyzyRules.getTjsx());
                                    tdd.setZybddd(xyzyRules.getBddd());
                                    tdd.setZyrxrq(xyzyRules.getRxrq());
                                    tdd.setXyxz(xyzyRules.getXz());
                                }
                            }

                            Zydh ytzydh = result.zydhMap.get(tdd.getYtzy()+tdd.getKldm());
                            Jhk ytjhk = result.jhkMap.get(tdd.getYtzy() + tdd.getKldm());
                            if (ytzydh != null) {
                                tdd.setYtzymc(zydh.getZymc());
                            } else if (ytjhk != null) {
                                tdd.setYtzymc(jhk.getZymc());
                            }
                        }
                        else if ("tddw".equals(name)) {
//                            tdd.setTddwmc(result.tddwMap.get(tdd.getPcdm() + "," + tdd.getKldm() + "," + tdd.getTddwdm()));
                            tdd.setTddwmc(result.tddwMap.get(tdd.getTddwdm()));
                        } else if("kldm".equals(name) && imports.getFileName().contains("T_TDD")){
                            tdd.setKlmc(result.kldmMap.get(tdd.getKldm()));
                        } else if("pcdm".equals(name)){
                            tdd.setPcmc(result.pcdmMap.get(tdd.getPcdm()));
                        } else if ("kslbdm".equals(name)) {
                            tdd.setKslbmc(result.kslbMap.get(tdd.getKslbdm()));
                        } else if ("bylbdm".equals(name)) {
                            tdd.setBylbmc(result.bylbMap.get(tdd.getBylbdm()));
                        }
                        /*else if ("byxxdm".equals(name)) {
                            tdd.setByxxdz(byxxMap.get(tdd.getByxxdm()));
                        }*/
                        else if ("jhlbdm".equals(name)) {
                            tdd.setJhlbmc(result.jhlbMap.get(tdd.getJhlbdm()));
                        } else if ("jhxzdm".equals(name)) {
                            tdd.setJhxz(result.jhlbMap.get(tdd.getJhlbdm()));
                        } else if ("dqdm".equals(name)) {
                            tdd.setDqmc(result.dqdmMap.get(tdd.getDqdm()));
                        } else if ("hjdm".equals(name)) {
                            tdd.setHjmc(result.dqdmMap.get(tdd.getHjdm()));
                        } else if ("xbdm".equals(name)) {
                            tdd.setXbmc(result.xbdmMap.get(tdd.getXbdm()));
                        } else if ("zzmmdm".equals(name)) {
                            tdd.setZzmmmc(result.zzmmdmMap.get(tdd.getZzmmdm()));
                        } else if ("mzdm".equals(name)) {
                            tdd.setMzmc(result.mzdmMap.get(tdd.getMzdm()));
                        } else if ("kstz".equals(name)) {
                            tdd.setKstzmc(result.kstzdmMap.get(tdd.getKstz()));
                        } else if ("kslxdm".equals(name)) {
                            tdd.setKslxmc(result.kslxdmMap.get(tdd.getKslxdm()));
                        } else if ("lqlxdm".equals(name) || "lqfs".equals(name)) {
                            tdd.setLqlxmc(result.kslxdmMap.get(tdd.getLqlxdm()));
                        } else if ("zjlxdm".equals(name)) {
                            tdd.setZjlxmc(result.zjlxdmMap.get(tdd.getZjlxdm()));
                        } else if ("tdyydm".equals(name)) {
                            tdd.setTdyymc(result.tdyydmMap.get(tdd.getTdyydm()));
                        } else if ("tdlxdm".equals(name)) {
                            tdd.setTdlxmc(result.tdlxdmMap.get(tdd.getTdlxdm()));
                        } else if ("wyyzdm".equals(name)) {
                            tdd.setWyyzmc(result.wyyzdmMap.get(tdd.getWyyzdm()));
                        } else if ("hjlbdm".equals(name)) {
                            tdd.setHjlbmc(result.hjlbdmMap.get(tdd.getHjlbdm()));
                        } else if ("czlbdm".equals(name)) {
                            tdd.setCzlbmc(result.czlbdmMap.get(tdd.getCzlbdm()));
                        } else if ("ysjzdm".equals(name)) {
                            tdd.setYsjzmc(result.mzdmMap.get(tdd.getYsjzdm()));
                        } else if ("bmdw".equals(name)) {
                            tdd.setBmdwmc(result.bmdwdmMap.get(tdd.getBmdw()));
                        } else if ("txdz".equals(name)) {
                            tdd.setLqtzsyjdz(tdd.getTxdz());
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
            tdd.setNf(imports.getNf());
            tdd.setSfdm(imports.getSfdm());
            tdd.setSfmc(imports.getSf());
            tdd.setZslx(imports.getZslx());
            tdd.setBdzt(Constants.BDZT_DQR);

            Tdd one = new Tdd();
            one.setNf(imports.getNf());
            one.setKsh(tdd.getKsh());
            one.setSfdm(imports.getSfdm());
            one.setSfmc(imports.getSf());
            if (tddService.count(Condition.getQueryWrapper(one)) > 0) {
                Tdd tdd1 = tddService.getOne(Condition.getQueryWrapper(one));
                BeanUtils.copyProperties(tdd, tdd1, "id");
                tddService.saveOrUpdate(tdd1);
            } else {
                tddService.save(tdd);
            }
        }
    }

    private static Result getResult(DataImports imports, Map<String, List<?>> tdMap) {
        Map<String, Zydh> zydhMap = new HashMap<>();
        Map<String, Jhk> jhkMap = new HashMap<>();
        Map<String, String> ccMap = new HashMap<>();
        Map<String, List<Zykmx>> zykmxMap = new HashMap<>();
        Map<String, String> zydhdmdzMap = new HashMap<>();
        Map<String, String> cjxdmMap = new HashMap<>();
        Map<String, String> tddwMap = new HashMap<>();
        Map<String, String> jhlbMap = new HashMap<>();
        Map<String, String> jhxzMap = new HashMap<>();
        Map<String, String> dqdmMap = new HashMap<>();
        Map<String, String> xbdmMap = new HashMap<>();
        Map<String, String> zzmmdmMap = new HashMap<>();
        Map<String, String> mzdmMap = new HashMap<>();
        Map<String, String> kstzdmMap = new HashMap<>();
        Map<String, String> kslxdmMap = new HashMap<>();
        Map<String, String> lqfsdmMap = new HashMap<>();
        Map<String, String> zjlxdmMap = new HashMap<>();
        Map<String, String> tdyydmMap = new HashMap<>();
        Map<String, String> tdlxdmMap = new HashMap<>();
        Map<String, String> zylbdmMap = new HashMap<>();
        Map<String, String> wyyzdmMap = new HashMap<>();
        Map<String, String> hjlbdmMap = new HashMap<>();
        Map<String, String> czlbdmMap = new HashMap<>();
        Map<String, String> bmdwdmMap = new HashMap<>();
        //自动修正
        //学院专业
        Map<String, XyzyRules> xyzyRulesMap = new HashMap<>();
        //科类
        Map<String, String> kldmMap = new HashMap<>();
        //批次
        Map<String, String> pcdmMap = new HashMap<>();
        //考生类别
        Map<String, String> kslbMap = new HashMap<>();
        //毕业类别
        Map<String, String> bylbMap = new HashMap<>();
        //高中信息
        Map<String, String> byxxMap = new HashMap<>();

        List<?> zydhList = tdMap.get("zydhList");
        for (Object o : zydhList) {
            Zydh zydh = (Zydh) o;
            zydhMap.put(zydh.getZydh()+zydh.getKldm(), zydh);
        }
        List<?> jhkList = tdMap.get("jhkList");
        for (Object o : jhkList) {
            Jhk jhk = (Jhk) o;
            jhkMap.put(jhk.getZydh()+jhk.getKldm(), jhk);
        }
        List<?> ccdmList = tdMap.get("ccdmList");
        for (Object o : ccdmList) {
            Ccdm ccdm = (Ccdm) o;
            ccMap.put(ccdm.getCode(), ccdm.getName());
        }
        List<?> zykmxList = tdMap.get("zykmxList");
        for (Object o : zykmxList) {
            Zykmx zykmx = (Zykmx) o;
            if (zykmxMap.containsKey(zykmx.getKsh())) {
                zykmxMap.get(zykmx.getKsh()).add(zykmx);
            } else {
                List<Zykmx> list = new ArrayList<>();
                list.add(zykmx);
                zykmxMap.put(zykmx.getKsh(), list);
            }
        }
        List<?> zydhdmdzList = tdMap.get("zydhdmdzList");
        for (Object o : zydhdmdzList) {
            Zydhdmdz zydhdmdz = (Zydhdmdz) o;
            zydhdmdzMap.put(zydhdmdz.getZydh(), zydhdmdz.getZydm());
        }
        List<?> cjxdmList = tdMap.get("cjxdmList");
        for (Object o : cjxdmList) {
            Cjxdm cjxdm = (Cjxdm) o;
            cjxdmMap.put(cjxdm.getCode() + "," + cjxdm.getSfdm(), cjxdm.getName());
        }
        List<?> tddwList = tdMap.get("tddwList");
        for (Object o : tddwList) {
            Tddw tddw = (Tddw) o;
//            tddwMap.put(tddw.getPcdm() + "," + tddw.getKldm() + "," + tddw.getCode(), tddw.getName());
            tddwMap.put(tddw.getCode(), tddw.getName());
        }
        List<?> jhlbList = tdMap.get("jhlbList");
        for (Object o : jhlbList) {
            Jhlbdm jhlbdm = (Jhlbdm) o;
            jhlbMap.put(jhlbdm.getCode(), jhlbdm.getName());
        }
        List<?> jhxzList = tdMap.get("jhxzList");
        for (Object o : jhxzList) {
            Jhxzdm jhxzdm = (Jhxzdm) o;
            jhxzMap.put(jhxzdm.getCode(), jhxzdm.getName());
        }

        //自动修正
        if (Constants.HAS_YES.equals(imports.getZdxz())) {
            List<?> xyzyRulesList = tdMap.get("xyzyRulesList");
            for (Object o : xyzyRulesList) {
                XyzyRules xyzyRules = (XyzyRules) o;
                xyzyRulesMap.put(xyzyRules.getZymc() + "," + xyzyRules.getCcmc() + "," + ("null".equals(xyzyRules.getZkfx()) ? "" : xyzyRules.getZkfx()), xyzyRules);
            }
        }
        List<?> kldmList = tdMap.get("kldmList");
        for (Object o : kldmList) {
            Kldm kldm = (Kldm) o;
            kldmMap.put(kldm.getCode(), kldm.getName());
        }
        List<?> pcdmList = tdMap.get("pcdmList");
        for (Object o : pcdmList) {
            Pcdm pcdm = (Pcdm) o;
            pcdmMap.put(pcdm.getCode(), pcdm.getName());
        }

        List<?> kslbList = tdMap.get("kslbList");
        for (Object o : kslbList) {
            Kslbdm kslbdm = (Kslbdm) o;
            kslbMap.put(kslbdm.getCode(), kslbdm.getName());
        }
        List<?> bylbList = tdMap.get("bylbList");
        for (Object o : bylbList) {
            Bylbdm bylbdm = (Bylbdm) o;
            bylbMap.put(bylbdm.getCode(), bylbdm.getName());
        }
       /* List<?> dqbList = tdMap.get("dqbList");
        for (Object o : dqbList) {
            CodeDqb dqb = (CodeDqb) o;
            byxxMap.put(dqb.getCode(), dqb.getName());
        }*/
        List<?> dqdmList = tdMap.get("dqdmList");
        for (Object o : dqdmList) {
            Dqdm dqdm = (Dqdm) o;
            dqdmMap.put(dqdm.getCode(), dqdm.getName());
        }
        List<?> xbdmList = tdMap.get("xbdmList");
        for (Object o : xbdmList) {
            Xbdm xbdm = (Xbdm) o;
            xbdmMap.put(xbdm.getCode(), xbdm.getName());
        }
        List<?> zzmmdmList = tdMap.get("zzmmdmList");
        for (Object o : zzmmdmList) {
            Zzmmdm zzmmdm = (Zzmmdm) o;
            zzmmdmMap.put(zzmmdm.getCode(), zzmmdm.getName());
        }
        List<?> mzdmList = tdMap.get("mzdmList");
        for (Object o : mzdmList) {
            Mzdm mzdm = (Mzdm) o;
            mzdmMap.put(mzdm.getCode(), mzdm.getName());
        }
        List<?> kstzdmList = tdMap.get("kstzdmList");
        for (Object o : kstzdmList) {
            Kstzdm kstzdm = (Kstzdm) o;
            kstzdmMap.put(kstzdm.getCode(), kstzdm.getName());
        }
        List<?> kslxdmList = tdMap.get("kslxdmList");
        for (Object o : kslxdmList) {
            Kslxdm kslxdm = (Kslxdm) o;
            kslxdmMap.put(kslxdm.getCode(), kslxdm.getName());
        }
        List<?> lqfsdmList = tdMap.get("lqfsdmList");
        for (Object o : lqfsdmList) {
            Lqfsdm lqfsdm = (Lqfsdm) o;
            lqfsdmMap.put(lqfsdm.getCode(), lqfsdm.getName());
        }
        List<?> zjlxdmList = tdMap.get("zjlxdmList");
        for (Object o : zjlxdmList) {
            Zjlxdm zjlxdm = (Zjlxdm) o;
            zjlxdmMap.put(zjlxdm.getCode(), zjlxdm.getName());
        }
        List<?> tdyydmList = tdMap.get("tdyydmList");
        for (Object o : tdyydmList) {
            Tdyydm tdyydm = (Tdyydm) o;
            tdyydmMap.put(tdyydm.getCode(), tdyydm.getName());
        }
        List<?> tdlxdmList = tdMap.get("tdlxdmList");
        for (Object o : tdlxdmList) {
            Tdlxdm tdlxdm = (Tdlxdm) o;
            tdlxdmMap.put(tdlxdm.getCode(), tdlxdm.getName());
        }
        List<?> zylbdmList = tdMap.get("zylbdmList");
        for (Object o : zylbdmList) {
            Zylbdm zylbdm = (Zylbdm) o;
            zylbdmMap.put(zylbdm.getCode(), zylbdm.getName());
        }
        List<?> wyyzdmList = tdMap.get("wyyzdmList");
        for (Object o : wyyzdmList) {
            Wyyzdm wyyzdm = (Wyyzdm) o;
            wyyzdmMap.put(wyyzdm.getCode(), wyyzdm.getName());
        }
        List<?> hjlbdmList = tdMap.get("hjlbdmList");
        for (Object o : hjlbdmList) {
            Hjlbdm hjlbdm = (Hjlbdm) o;
            hjlbdmMap.put(hjlbdm.getCode(), hjlbdm.getName());
        }
        List<?> czlbdmList = tdMap.get("czlbdmList");
        for (Object o : czlbdmList) {
            Czlbdm czlbdm = (Czlbdm) o;
            czlbdmMap.put(czlbdm.getCode(), czlbdm.getName());
        }
        List<?> bmdwdmList = tdMap.get("bmdwdmList");
        for (Object o : bmdwdmList) {
            Bmdwdm bmdwdm = (Bmdwdm) o;
            bmdwdmMap.put(bmdwdm.getCode(), bmdwdm.getName());
        }
        Result result = new Result(zydhMap,jhkMap, ccMap, zykmxMap, zydhdmdzMap, cjxdmMap, tddwMap, jhlbMap,jhxzMap, dqdmMap, xbdmMap, zzmmdmMap, mzdmMap, kstzdmMap, kslxdmMap, zjlxdmMap, tdyydmMap, tdlxdmMap, wyyzdmMap, hjlbdmMap, czlbdmMap, bmdwdmMap, xyzyRulesMap, kldmMap, pcdmMap, kslbMap, bylbMap);
        return result;
    }

    private static class Result {
        public final Map<String, Zydh> zydhMap;
        public final Map<String, Jhk> jhkMap;
        public final Map<String, String> ccMap;
        public final Map<String, List<Zykmx>> zykmxMap;
        public final Map<String, String> zydhdmdzMap;
        public final Map<String, String> cjxdmMap;
        public final Map<String, String> tddwMap;
        public final Map<String, String> jhlbMap;
        public final Map<String, String> jhxzdmMap;
        public final Map<String, String> dqdmMap;
        public final Map<String, String> xbdmMap;
        public final Map<String, String> zzmmdmMap;
        public final Map<String, String> mzdmMap;
        public final Map<String, String> kstzdmMap;
        public final Map<String, String> kslxdmMap;
        public final Map<String, String> zjlxdmMap;
        public final Map<String, String> tdyydmMap;
        public final Map<String, String> tdlxdmMap;
        public final Map<String, String> wyyzdmMap;
        public final Map<String, String> hjlbdmMap;
        public final Map<String, String> czlbdmMap;
        public final Map<String, String> bmdwdmMap;
        public final Map<String, XyzyRules> xyzyRulesMap;
        public final Map<String, String> kldmMap;
        public final Map<String, String> pcdmMap;
        public final Map<String, String> kslbMap;
        public final Map<String, String> bylbMap;

        public Result(Map<String, Zydh> zydhMap,Map<String, Jhk> jhkMap, Map<String, String> ccMap, Map<String, List<Zykmx>> zykmxMap, Map<String, String> zydhdmdzMap, Map<String, String> cjxdmMap, Map<String, String> tddwMap, Map<String, String> jhlbMap,Map<String, String> jhxzdmMap, Map<String, String> dqdmMap, Map<String, String> xbdmMap, Map<String, String> zzmmdmMap, Map<String, String> mzdmMap, Map<String, String> kstzdmMap, Map<String, String> kslxdmMap, Map<String, String> zjlxdmMap, Map<String, String> tdyydmMap, Map<String, String> tdlxdmMap, Map<String, String> wyyzdmMap, Map<String, String> hjlbdmMap, Map<String, String> czlbdmMap, Map<String, String> bmdwdmMap, Map<String, XyzyRules> xyzyRulesMap, Map<String, String> kldmMap, Map<String, String> pcdmMap, Map<String, String> kslbMap, Map<String, String> bylbMap) {
            this.zydhMap = zydhMap;
            this.jhkMap = jhkMap;
            this.ccMap = ccMap;
            this.zykmxMap = zykmxMap;
            this.zydhdmdzMap = zydhdmdzMap;
            this.cjxdmMap = cjxdmMap;
            this.tddwMap = tddwMap;
            this.jhlbMap = jhlbMap;
            this.jhxzdmMap = jhxzdmMap;
            this.dqdmMap = dqdmMap;
            this.xbdmMap = xbdmMap;
            this.zzmmdmMap = zzmmdmMap;
            this.mzdmMap = mzdmMap;
            this.kstzdmMap = kstzdmMap;
            this.kslxdmMap = kslxdmMap;
            this.zjlxdmMap = zjlxdmMap;
            this.tdyydmMap = tdyydmMap;
            this.tdlxdmMap = tdlxdmMap;
            this.wyyzdmMap = wyyzdmMap;
            this.hjlbdmMap = hjlbdmMap;
            this.czlbdmMap = czlbdmMap;
            this.bmdwdmMap = bmdwdmMap;
            this.xyzyRulesMap = xyzyRulesMap;
            this.kldmMap = kldmMap;
            this.pcdmMap = pcdmMap;
            this.kslbMap = kslbMap;
            this.bylbMap = bylbMap;
        }
    }
}
