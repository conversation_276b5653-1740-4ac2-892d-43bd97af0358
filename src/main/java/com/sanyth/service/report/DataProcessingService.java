package com.sanyth.service.report;

import com.alibaba.fastjson.JSONObject;
import com.sanyth.dto.DataSetDto;
import com.sanyth.dto.DataSetParamDto;
import com.sanyth.dto.DataSetTransformDto;
import com.sanyth.dto.OriginalDataDto;
import net.sf.jsqlparser.JSQLParserException;

import java.util.List;
import java.util.Map;

public interface DataProcessingService {

    boolean verification(DataSetParamDto dataSetParamDto);
    boolean verification(List<DataSetParamDto> dataSetParamDtoList, Map<String, Object> contextData);

    public JSONObject testTransform(DataSetDto dto) throws Exception;

    public String transformParam(Map<String, Object> contextData, DataSetDto dto) throws JSQLParserException;

    public String transformParam(List<DataSetParamDto> dataSetParamDtoList, DataSetDto dto) throws JSQLParserException;

    public List<JSONObject> transformOf(List<DataSetTransformDto> dataSetTransformDtoList, List<JSONObject> data);

    public OriginalDataDto getData(DataSetDto dataSetDto) throws Exception;
}
