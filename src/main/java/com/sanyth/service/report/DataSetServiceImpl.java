package com.sanyth.service.report;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.Constants;
import com.sanyth.dto.DataSetDto;
import com.sanyth.dto.DataSetParamDto;
import com.sanyth.dto.DataSetTransformDto;
import com.sanyth.dto.OriginalDataDto;
import com.sanyth.mapper.report.DataSetMapper;
import com.sanyth.model.report.DataSet;
import com.sanyth.model.report.DataSetParam;
import com.sanyth.model.report.DataSetTransform;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-26
 */
@Slf4j
@Service
public class DataSetServiceImpl extends ServiceImpl<DataSetMapper, DataSet> implements IDataSetService {

    @Resource
    private DataSourceService dataSourceService;
    @Resource
    private IDataSetParamService dataSetParamService;
    @Autowired
    private IDataSetTransformService dataSetTransformService;
    @Resource
    private DataProcessingService dataProcessingService;

    @Override
    public JSONObject testTransform(DataSetDto dto) throws Exception {
        return dataProcessingService.testTransform(dto);
    }

    @Override
    @Transactional
    public DataSetDto insertSet(DataSetDto dto) {
        List<DataSetParamDto> dataSetParamDtoList = dto.getDataSetParamDtoList();
        List<DataSetParamDto> dataSetDrillParamDtoList = dto.getDataSetDrillParamDtoList();
        List<DataSetTransformDto> dataSetTransformDtoList = dto.getDataSetTransformDtoList();

        //1.新增数据集
        DataSet dataSet = new DataSet();
        BeanUtils.copyProperties(dto, dataSet);
        saveOrUpdate(dataSet);
        //2.更新查询参数
        dto.setId(dataSet.getId());
        dataSetParamBatch(dataSetParamDtoList, dto.getId(), Constants.DATA_SET_PARAMS_TYPE_SELECT);
        dataSetParamBatch(dataSetDrillParamDtoList, dto.getId(), Constants.DATA_SET_PARAMS_TYPE_DRILL);

        //3.更新数据转换
        dataSetTransformBatch(dataSetTransformDtoList, dto.getId());
        return dto;
    }

    @Override
    public DataSetDto detailSet(DataSetDto dto) {
        String id = dto.getId();
        if (StringUtils.isBlank(id)) {
            return dto;
        }
        DataSet dataSet = this.getById(id);
        if (dataSet == null) {
            return dto;
        }
        //查询参数 --报表未用到查询参数，使用的下钻参数，预警功能使用了查询参数
        /*List<DataSetParam> dataSetParamList = dataSetParamService.list(new QueryWrapper<DataSetParam>().eq("SETCODE", id).eq("TYPE",Constants.DATA_SET_PARAMS_TYPE_SELECT));
        List<DataSetParamDto> dataSetParamDtoList = new ArrayList<>();
        dataSetParamList.forEach(dataSetParam -> {
            DataSetParamDto dataSetParamDto = new DataSetParamDto();
            BeanUtils.copyProperties(dataSetParam, dataSetParamDto);
            dataSetParamDtoList.add(dataSetParamDto);
        });
        dto.setDataSetParamDtoList(dataSetParamDtoList);*/
        //下钻参数
        List<DataSetParam> dataSetDrillParamList = dataSetParamService.list(new QueryWrapper<DataSetParam>().eq("SETCODE", id).eq("TYPE",Constants.DATA_SET_PARAMS_TYPE_DRILL));
        List<DataSetParamDto> dataSetDrillParamDtoList = new ArrayList<>();
        dataSetDrillParamList.forEach(dataSetParam -> {
            DataSetParamDto dataSetParamDto = new DataSetParamDto();
            BeanUtils.copyProperties(dataSetParam, dataSetParamDto);
            dataSetDrillParamDtoList.add(dataSetParamDto);
        });
        dto.setDataSetDrillParamDtoList(dataSetDrillParamDtoList);
        Set<String> drillParamSet = new HashSet<>();
        dataSetDrillParamDtoList.stream().forEach(drillParam->{
            drillParamSet.add(drillParam.getParamName());
        });
        dto.setDrillParamList(drillParamSet);//下钻参数set

        //数据转换
        List<DataSetTransform> dataSetTransformList = dataSetTransformService.list(new QueryWrapper<DataSetTransform>().eq("SETCODE", id));
        List<DataSetTransformDto> dataSetTransformDtoList = new ArrayList<>();
        dataSetTransformList.forEach(dataSetTransform -> {
            DataSetTransformDto dataSetTransformDto = new DataSetTransformDto();
            BeanUtils.copyProperties(dataSetTransform, dataSetTransformDto);
            dataSetTransformDtoList.add(dataSetTransformDto);
        });
        dto.setDataSetTransformDtoList(dataSetTransformDtoList);
        if (StringUtils.isNotBlank(dataSet.getCaseResult())) {
            try {
                JSONArray jsonArray = JSONArray.parseArray(dataSet.getCaseResult());
                if (jsonArray.size() > 0) {
                    JSONObject jsonObject = jsonArray.getJSONObject(0);
                    dto.setSetParamList(jsonObject.keySet());
                }
            } catch (Exception e) {
                log.error("dataSet.getCaseResult出现异常");
            }
        }

        BeanUtils.copyProperties(dataSet, dto);
        return dto;
    }

    @Override
    public OriginalDataDto getData(DataSetDto dto) throws Exception {
        return dataProcessingService.getData(detailSet(dto));
    }

    @Override
    public Page<DataSetDto> queryPage(BaseQuery<DataSet> query) {
        Page<DataSet> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataSet> wrapper = buildWrapper(query.getQueryParam());
        long count = this.count(wrapper);
        Page<DataSet> dataSetPage = this.page(page, wrapper);
        Page<DataSetDto> dataSetDtoPage = new Page<>(query.getPage(), query.getPageSize());
        List<DataSetDto> dataSetDtos = new LinkedList<>();
        dataSetPage.getRecords().forEach(dataSet -> {
            DataSetDto dataSetDto = new DataSetDto();
            BeanUtils.copyProperties(dataSet, dataSetDto);
            detailSet(dataSetDto);
            dataSetDtos.add(dataSetDto);
        });
        dataSetDtoPage.setRecords(dataSetDtos);
        dataSetDtoPage.setTotal(count);
        return dataSetDtoPage;
    }

    @Override
    @Transactional
    public void removeBatch(List<String> asList) {
        removeByIds(asList);
        QueryWrapper<DataSetParam> wrapper = new QueryWrapper<>();
        wrapper.in("setCode", asList);
        dataSetParamService.remove(wrapper);
    }

    private Wrapper<DataSet> buildWrapper(DataSet query) {
        QueryWrapper<DataSet> wrapper = new QueryWrapper<>();
        // Query condition...
        if (StringUtils.isNoneBlank(query.getSourceCode())) {
            wrapper.eq("sourceCode", query.getSourceCode());
        }
        if (StringUtils.isNoneBlank(query.getSetName())) {
            wrapper.like("setName", query.getSetName());
        }
        if (StringUtils.isNoneBlank(query.getSetType())) {
            wrapper.like("setType", query.getSetType());
        }
        if (StringUtils.isNoneBlank(query.getType())) {
            wrapper.eq("type", query.getType());
        }
        if (query.getEnableFlag() != null) {
            wrapper.eq("enableFlag", query.getEnableFlag());
        }
        wrapper.orderByDesc("createTime");
        return wrapper;
    }

    public void dataSetParamBatch(List<DataSetParamDto> dataSetParamDtoList,String setId,String paramType){
        if (StringUtils.isNoneBlank(setId)) {
            dataSetParamService.remove(new QueryWrapper<DataSetParam>().eq("SETCODE", setId).eq("TYPE", paramType));
        }
        if (null == dataSetParamDtoList || dataSetParamDtoList.size() <= 0) {
            return;
        }
        List<DataSetParam> dataSetParamList = new ArrayList<>();
        dataSetParamDtoList.forEach(dataSetParamDto -> {
            DataSetParam dataSetParam = new DataSetParam();
            BeanUtils.copyProperties(dataSetParamDto, dataSetParam);
            dataSetParam.setSetCode(setId);
            dataSetParam.setType(paramType);
            dataSetParamList.add(dataSetParam);
        });
        dataSetParamService.saveBatch(dataSetParamList);
    }

    public void dataSetTransformBatch(List<DataSetTransformDto> dataSetTransformDtoList,String setId){
        if (StringUtils.isNoneBlank(setId)) {
            dataSetTransformService.remove(new QueryWrapper<DataSetTransform>().eq("SETCODE",setId));
        }
        if (null == dataSetTransformDtoList || dataSetTransformDtoList.size() <= 0) {
            return;
        }
        List<DataSetTransform> dataSetTransformList = new ArrayList<>();
        for (int i = 0; i < dataSetTransformDtoList.size(); i++) {
            DataSetTransform dataSetTransform = new DataSetTransform();
            BeanUtils.copyProperties(dataSetTransformDtoList.get(i), dataSetTransform);
            dataSetTransform.setOrderNum(i + 1);
            dataSetTransform.setSetCode(setId);
            dataSetTransformList.add(dataSetTransform);
        }
        dataSetTransformService.saveBatch(dataSetTransformList);
    }

}
