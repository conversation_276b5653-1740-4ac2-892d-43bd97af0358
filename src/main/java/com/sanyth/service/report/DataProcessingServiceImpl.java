package com.sanyth.service.report;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sanyth.dto.*;
import com.sanyth.model.report.DataSource;
import com.sanyth.util.SQLParseUtil;
import net.sf.jsqlparser.JSQLParserException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DataProcessingServiceImpl implements DataProcessingService, InitializingBean, ApplicationContextAware {

    private final Map<String, TransformStrategy> queryServiceImplMap = new HashMap<>();
    private ApplicationContext applicationContext;

    public TransformStrategy getTarget(String type) {
        return queryServiceImplMap.get(type);
    }

    @Autowired
    private DataSourceService dataSourceService;

    private ScriptEngine engine;
    {
        ScriptEngineManager manager = new ScriptEngineManager();
        engine = manager.getEngineByName("JavaScript");
    }

    @Override
    public boolean verification(DataSetParamDto dataSetParamDto) {
        String sampleItem = dataSetParamDto.getSampleItem();
        String validationRules = dataSetParamDto.getValidationRules();
        if (StringUtils.isNotBlank(validationRules)) {
            validationRules = validationRules + "\nvar result = verification('" + sampleItem + "');";
            try {
                engine.eval(validationRules);
                return Boolean.parseBoolean(engine.get("result").toString());

            } catch (Exception ex) {
                ex.printStackTrace();
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean verification(List<DataSetParamDto> dataSetParamDtoList, Map<String, Object> contextData) {
        if (null == dataSetParamDtoList || dataSetParamDtoList.size() == 0) {
            return true;
        }

        for (DataSetParamDto dataSetParamDto : dataSetParamDtoList) {
            if (null != contextData) {
                String value = contextData.getOrDefault(dataSetParamDto.getParamName(), "").toString();
                dataSetParamDto.setSampleItem(value);
            }
            if (!verification(dataSetParamDto)) {
                return false;
            }
        }
        return true;
    }

    @Override
    public JSONObject testTransform(DataSetDto dto) throws Exception {
        JSONObject object = new JSONObject();
        String sourceCode = dto.getSourceCode();
        //1.获取数据源
        QueryWrapper<DataSource> sourceWrapper = new QueryWrapper<>();
        sourceWrapper.eq("ID", sourceCode);
        DataSource dataSource = dataSourceService.getOne(sourceWrapper);
        dto.setSourceType(dataSource.getSourceType());

        //3.参数替换
        //3.1参数校验
//        boolean verification = dataSetParamService.verification(dto.getDataSetParamDtoList(), null);
//        if (!verification) {
//            return object;
//        }
        //查询参数 下钻参数处理
        List<DataSetParamDto> dataSetParamDtoList = new ArrayList<>();
        List<DataSetParamDto> paramDtoList = dto.getDataSetParamDtoList();
        List<DataSetParamDto> drillParamDtoList = dto.getDataSetDrillParamDtoList();
        if (CollectionUtils.isNotEmpty(paramDtoList)) {
            dataSetParamDtoList.addAll(paramDtoList);
        }
        if ((CollectionUtils.isNotEmpty(drillParamDtoList))) {
            dataSetParamDtoList.addAll(drillParamDtoList);
        }
        /*
        * 1.sql类型 把模板sql转换为具体的sql,并增加权限过滤
        * */
        String dynSentence = transformParam(dataSetParamDtoList, dto);
        //4.获取数据
        DataSourceDto dataSourceDto = new DataSourceDto();
        BeanUtils.copyProperties(dataSource, dataSourceDto);
        dataSourceDto.setDynSentence(dynSentence);
        dataSourceDto.setContextData(dto.getContextData());

        //获取total,判断DataSetParamDtoList中是否传入分页参数
        Map<String, Object> collect = dto.getDataSetParamDtoList().stream().collect(Collectors.toMap(DataSetParamDto::getParamName, DataSetParamDto::getSampleItem));
        if (collect.containsKey("pageNumber") && collect.containsKey("pageSize")) {
            dto.setContextData(collect);
            long total = dataSourceService.total(dataSourceDto, dto.getContextData());
            object.put("total", total);
        } else if (CollectionUtils.isNotEmpty(dto.getDataSetDrillParamDtoList())) {
            collect.put("pageNumber", "1");
            collect.put("pageSize", "5");
            dto.setContextData(collect);
            long total = dataSourceService.total(dataSourceDto, dto.getContextData());
            object.put("total", total);
        }

        List<JSONObject> data = dataSourceService.execute(dataSourceDto);
        //5.数据转换
        List<JSONObject> transform = transformOf(dto.getDataSetTransformDtoList(), data);

        object.put("data", transform);
        return object;
    }

    @Override
    public String transformParam(Map<String, Object> contextData, DataSetDto dto) throws JSQLParserException {
        if (StringUtils.isBlank(dto.getDynSentence())) {
            return dto.getDynSentence();
        }

//        return SQLParseUtil.parse(dto.getDynSentence(), contextData);
        String sourceType = dto.getSourceType();
//        switch (sourceType) {
//            case JdbcConstants.MYSQL:
//            case JdbcConstants.ORACLE:
//            case JdbcConstants.SQL_SERVER:
                String parse = SQLParseUtil.parse(dto.getDynSentence(), contextData);
                return StringUtils.isNotBlank(dto.getFilterField()) ?
                        SQLParseUtil.parseJSQL(parse, dto.getTableAlias(), dto.getFilterField(), dto.getOrganizations()) : parse;
            //TODO http接口的暂时不做，考虑到http接口抽取的可能是明细数据，没有做统计分析逻辑，暂时搁置
            /*case JdbcConstants.HTTP:
                String dynSentence = dto.getApiUrl() + dto.getDynSentence();
                if (dynSentence.contains("${")) {
                    dynSentence = ParamsResolverHelper.resolveParams(contextData, dynSentence);
                }
                return dynSentence;*/
//            default:
//                return null;
//        }
    }
    @Override
    public String transformParam(List<DataSetParamDto> dataSetParamDtoList, DataSetDto dto) throws JSQLParserException {
        Map<String, Object> contextData = new HashMap<>();
        if (dataSetParamDtoList.size() > 0) {
            dataSetParamDtoList.forEach(dataSetParamDto -> {
                contextData.put(dataSetParamDto.getParamName(), dataSetParamDto.getSampleItem());
            });
        }
        return transformParam(contextData, dto);
    }

    @Override
    public List<JSONObject> transformOf(List<DataSetTransformDto> dataSetTransformDtoList, List<JSONObject> data) {
        data.forEach(d->{
            d.remove("SYTNUM");
        });
        if (dataSetTransformDtoList == null || dataSetTransformDtoList.size() <= 0) {
            return data;
        }
        for (DataSetTransformDto dto : dataSetTransformDtoList) {
            data = getTarget(dto.getTransformType()).transform(dto, data);
        }
        return data;
    }

    @Override
    public OriginalDataDto getData(DataSetDto dataSetDto) throws Exception {
        OriginalDataDto originalDataDto = new OriginalDataDto();
        if (StringUtils.isBlank(dataSetDto.getSourceCode())) {
            return originalDataDto;
        }
        //2.获取数据源
        DataSource dataSource = dataSourceService.getOne(new QueryWrapper<DataSource>().eq("ID", dataSetDto.getSourceCode()));
        //查询参数 下钻参数处理
        List<DataSetParamDto> dataSetParamDtoList = new ArrayList<>();
        List<DataSetParamDto> paramDtoList = dataSetDto.getDataSetParamDtoList();
        List<DataSetParamDto> drillParamDtoList = dataSetDto.getDataSetDrillParamDtoList();
        if (CollectionUtils.isNotEmpty(paramDtoList)) {
            dataSetParamDtoList.addAll(paramDtoList);
        }
        if ((CollectionUtils.isNotEmpty(drillParamDtoList))) {
            dataSetParamDtoList.addAll(drillParamDtoList);
        }
        if (MapUtils.isNotEmpty(dataSetDto.getContextData())) {
            dataSetParamDtoList.forEach(dataSetParamDto -> {
                Object o = dataSetDto.getContextData().get(dataSetParamDto.getParamName().toLowerCase());
                if (o == null) {
                    o = dataSetDto.getContextData().get(dataSetParamDto.getParamName().toUpperCase());
                }
                dataSetParamDto.setSampleItem(o==null?"": String.valueOf(o));
            });
        }
        String dynSentence = transformParam(dataSetParamDtoList, dataSetDto);
//        String dynSentence = transformParam(dataSetDto.getContextData(), dataSetDto.getDynSentence());
        //4.获取数据
        DataSourceDto dataSourceDto = new DataSourceDto();
        BeanUtils.copyProperties(dataSource, dataSourceDto);

        dataSourceDto.setDynSentence(StringUtils.isNotBlank(dataSetDto.getFilterField()) ?
                SQLParseUtil.parseJSQL(dynSentence, dataSetDto.getTableAlias(), dataSetDto.getFilterField(),dataSetDto.getOrganizations()) : dynSentence);
        dataSourceDto.setContextData(dataSetDto.getContextData());
        //获取total,判断contextData中是否传入分页参数
        if (null != dataSetDto.getContextData()
                && dataSetDto.getContextData().containsKey("pageNumber")
                && dataSetDto.getContextData().containsKey("pageSize")) {
            long total = dataSourceService.total(dataSourceDto, dataSetDto.getContextData());
            originalDataDto.setTotal(total);
        }
        List<JSONObject> data = dataSourceService.execute(dataSourceDto);
        //5.数据转换
        List<JSONObject> transform = transformOf(dataSetDto.getDataSetTransformDtoList(), data);
        originalDataDto.setData(transform);
//        }
        return originalDataDto;
    }


    @Override
    public void afterPropertiesSet(){
        Map<String, TransformStrategy> beanMap = applicationContext.getBeansOfType(TransformStrategy.class);
        //遍历该接口的所有实现，将其放入map中
        for (TransformStrategy serviceImpl : beanMap.values()) {
            queryServiceImplMap.put(serviceImpl.type(), serviceImpl);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
