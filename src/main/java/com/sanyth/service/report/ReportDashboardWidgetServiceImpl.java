package com.sanyth.service.report;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.mapper.report.ReportDashboardWidgetMapper;
import com.sanyth.model.report.ReportDashboardWidget;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Service
public class ReportDashboardWidgetServiceImpl extends ServiceImpl<ReportDashboardWidgetMapper, ReportDashboardWidget> implements IReportDashboardWidgetService {


    @Override
    public void deleteByReportIds(List<String> ids) {
        QueryWrapper<ReportDashboardWidget> wrapper = new QueryWrapper<>();
        wrapper.in("reportCode", ids);
        remove(wrapper);
    }
}
