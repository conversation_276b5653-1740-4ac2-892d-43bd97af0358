package com.sanyth.service.report;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.FileInfo;
import com.sanyth.core.common.FileOperationUtil;
import com.sanyth.core.common.ToolsUtil;
import com.sanyth.dto.*;
import com.sanyth.mapper.report.ReportDashboardMapper;
import com.sanyth.model.report.ReportDashboard;
import com.sanyth.model.report.ReportDashboardWidget;
import com.sanyth.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
@Slf4j
@Service
public class ReportDashboardServiceImpl extends ServiceImpl<ReportDashboardMapper, ReportDashboard> implements IReportDashboardService, InitializingBean, ApplicationContextAware {

    private Map<String, ChartStrategy> queryServiceImplMap = new HashMap<>();
    private ApplicationContext applicationContext;

    @Autowired
    private IDataSetService dataSetService;
    @Autowired
    private IReportDashboardWidgetService reportDashboardWidgetService;
    @Autowired
    private FileOperationUtil fileOperationUtil;

    @Override
    public ReportDashboardObjectDto getDetail(ReportDashboardDto dto) {
        String reportCode = dto.getReportCode();
        ReportDashboardObjectDto result = new ReportDashboardObjectDto();
        ReportDashboard reportDashboard = this.getOne(new QueryWrapper<ReportDashboard>().eq("REPORTCODE", reportCode));
        if (null == reportDashboard) {
            return new ReportDashboardObjectDto();
        }
        BeanUtils.copyProperties(reportDashboard, dto,"components");
        dto.setComponents(JSONObject.parseObject(reportDashboard.getComponents()));
        QueryWrapper<ReportDashboardWidget> reportDashboardWidgetEntityWrapper = new QueryWrapper<>();
        reportDashboardWidgetEntityWrapper.eq("reportCode", reportCode).orderByAsc("sort");
        List<ReportDashboardWidget> list = reportDashboardWidgetService.list(reportDashboardWidgetEntityWrapper);
        List<ReportDashboardWidgetDto> reportDashboardWidgetDtoList = new ArrayList<>();
        list.forEach(reportDashboardWidget -> {
            ReportDashboardWidgetDto reportDashboardWidgetDto = new ReportDashboardWidgetDto();
            ReportDashboardWidgetValueDto value = new ReportDashboardWidgetValueDto();
            value.setSetup(StringUtils.isNotBlank(reportDashboardWidget.getSetup()) ? JSONObject.parseObject(reportDashboardWidget.getSetup()) : new JSONObject());
            value.setData(StringUtils.isNotBlank(reportDashboardWidget.getData()) ? JSONObject.parseObject(reportDashboardWidget.getData()) : new JSONObject());
            value.setPosition(StringUtils.isNotBlank(reportDashboardWidget.getPosition()) ? JSONObject.parseObject(reportDashboardWidget.getPosition()) : new JSONObject());
            value.setCollapse(StringUtils.isNotBlank(reportDashboardWidget.getCollapse()) ? JSONObject.parseObject(reportDashboardWidget.getCollapse()) : new JSONObject());

            //实时数据的替换
//            analysisData(value);
            reportDashboardWidgetDto.setType(reportDashboardWidget.getType());
            reportDashboardWidgetDto.setValue(value);
            reportDashboardWidgetDto.setOptions(JSONObject.parseObject(reportDashboardWidget.getOptions()));
            reportDashboardWidgetDtoList.add(reportDashboardWidgetDto);
        });
        dto.setWidgets(reportDashboardWidgetDtoList);
        result.setDashboard(dto);
        result.setReportCode(reportCode);
        return result;
    }

    @Override
    public void insertDashboard(ReportDashboardObjectDto dto) {
        String reportCode = dto.getReportCode();
        //查询ReportDashboard
        ReportDashboard reportDashboard = this.getOne(new QueryWrapper<ReportDashboard>().eq("REPORTCODE", reportCode));
        if (null == reportDashboard) {
            reportDashboard = new ReportDashboard();
        }
        BeanUtils.copyProperties(dto.getDashboard(), reportDashboard, "components");
        reportDashboard.setComponents(JSONObject.toJSONString(dto.getDashboard().getComponents()));
        reportDashboard.setReportCode(reportCode);
        this.saveOrUpdate(reportDashboard);

        //删除reportDashboardWidget
        reportDashboardWidgetService.remove(new QueryWrapper<ReportDashboardWidget>().eq("REPORTCODE", reportCode));
        List<ReportDashboardWidgetDto> widgets = dto.getWidgets();

        List<ReportDashboardWidget> reportDashboardWidgetList = new ArrayList<>();
        for (int i = 0; i < widgets.size(); i++) {
            ReportDashboardWidget reportDashboardWidget = new ReportDashboardWidget();
            ReportDashboardWidgetDto reportDashboardWidgetDto = widgets.get(i);
            String type = reportDashboardWidgetDto.getType();
            ReportDashboardWidgetValueDto value = reportDashboardWidgetDto.getValue();
            reportDashboardWidget.setReportCode(reportCode);
            reportDashboardWidget.setType(type);
            reportDashboardWidget.setSetup(value.getSetup() != null ? JSONObject.toJSONString(value.getSetup()) : "");
            reportDashboardWidget.setData(value.getData() != null ? JSONObject.toJSONString(value.getData()) : "");
            reportDashboardWidget.setPosition(value.getPosition() != null ? JSONObject.toJSONString(value.getPosition()) : "");
            reportDashboardWidget.setCollapse(value.getCollapse() != null ? JSONObject.toJSONString(value.getCollapse()) : "");
            reportDashboardWidget.setOptions(reportDashboardWidgetDto.getOptions() != null ? JSONObject.toJSONString(reportDashboardWidgetDto.getOptions()) : "");
            reportDashboardWidget.setSort(i + 1);
            reportDashboardWidgetList.add(reportDashboardWidget);
        }
        reportDashboardWidgetService.saveBatch(reportDashboardWidgetList);
    }

    @Override
    public Map<String,Object> getChartData(ChartDto dto) throws Exception {
        DataSetDto setDto = new DataSetDto();
        setDto.setId(dto.getSetCode());
        setDto.setContextData(dto.getContextData());
        OriginalDataDto result = dataSetService.getData(setDto);
        List<JSONObject> data = result.getData();
        //处理时间轴
        List<JSONObject> resultData = buildTimeLine(data, dto);
        Map<String, Object> map = new HashMap<>();
        map.put("total", result.getTotal());
        map.put("data", resultData);
        return map;
    }

    /**
     * 导出大屏，zip文件
     *
     * @param request
     * @param response
     * @param reportCode
     * @return
     */
    @Override
    public ResponseEntity<byte[]> exportDashboard(HttpServletRequest request, HttpServletResponse response, String reportCode, Integer showDataSet){
        String userAgent = request.getHeader("User-Agent");
        boolean isIeBrowser = userAgent.indexOf("MSIE") > 0;
        ReportDashboardDto dto = new ReportDashboardDto();
        dto.setReportCode(reportCode);
        ReportDashboardObjectDto detail = getDetail(dto);
        List<ReportDashboardWidgetDto> widgets = detail.getDashboard().getWidgets();
        detail.setWidgets(widgets);
        detail.getDashboard().setWidgets(null);

        //1.组装临时目录
        String path = FileUtil.getPath("user.dir") + File.separator + "upload" + File.separator + "zip" + File.separator + ToolsUtil.getRandomCode(6);

        //将涉及到的图片保存下来（1.背景图，2.组件为图片的）
        String backgroundImage = detail.getDashboard().getBackgroundImage();
        zipLoadImage(backgroundImage, path);
        detail.getWidgets().stream()
                .filter(reportDashboardWidgetDto -> "widget-image".equals(reportDashboardWidgetDto.getType()))
                .forEach(reportDashboardWidgetDto -> {
                    String imageAddress = reportDashboardWidgetDto.getValue().getSetup().getString("imageAdress");
                    zipLoadImage(imageAddress, path);
                });

        //showDataSet == 0 代表不包含数据集
        if (0 == showDataSet) {
            detail.getWidgets().forEach(reportDashboardWidgetDto -> {
                ReportDashboardWidgetValueDto value = reportDashboardWidgetDto.getValue();
                JSONObject data = value.getData();
                if (null != data && data.containsKey("dataType")) {
                    reportDashboardWidgetDto.getValue().getData().put("dataType", "staticData");
                }
            });
        }

        //2.将大屏设计到的json文件保存
        String jsonPath = path + "/dashboard.json";
        FileUtil.WriteStringToFile(jsonPath,JSONObject.toJSONString(detail));

        //将path文件夹打包zip
        String zipPath = path + ".zip";
        FileUtil.compress(path, zipPath);
        File file = new File(zipPath);
        ResponseEntity.BodyBuilder builder = ResponseEntity.ok();
        builder.contentLength(file.length());
        //application/octet-stream 二进制数据流（最常见的文件下载）
        builder.contentType(MediaType.APPLICATION_OCTET_STREAM);
        if (isIeBrowser) {
            builder.header("Content-Disposition", "attachment; filename=" + reportCode + ".zip");
        } else {
            builder.header("Content-Disposition", "attacher; filename*=UTF-8''" + reportCode + ".zip");
        }
        ResponseEntity<byte[]> body = builder.body(FileUtil.readFileToByteArray(file));
        //删除zip文件
        file.delete();
        //删除path临时文件夹
        FileUtil.delete(path);
        log.info("删除临时文件：{}，{}", zipPath, path);
        return body;
    }

    @Override
    public ResponseEntity<byte[]> exportDirllData(HttpServletRequest request, HttpServletResponse response, List<JSONObject> chartData) {
        String userAgent = request.getHeader("User-Agent");
        boolean isIeBrowser = userAgent.indexOf("MSIE") > 0;
        //1.组装临时目录
        String path = FileUtil.getPath("user.dir") + File.separator + "upload" + File.separator + "drillData" + File.separator + ToolsUtil.getRandomCode(6);

        return null;
    }

    /**
     * 导入大屏zip
     *
     * @param file
     * @param reportCode
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importDashboard(MultipartFile file, String reportCode) throws Exception {
        log.info("导入开始,{}", reportCode);
        //1.组装临时目录
        String path = FileUtil.getPath("user.dir") + File.separator + "upload" + File.separator + "zip" + File.separator + ToolsUtil.getRandomCode(6);
        //2.解压
        FileUtil.decompress(file, path);
        // path/uuid/
        File parentPath = new File(path);
        //获取打包的第一层目录
        File firstFile = parentPath.listFiles()[0];

        File[] files = firstFile.listFiles();

        //定义map
        Map<String, String> fileMap = new HashMap<>();
        String content = "";

        for (int i = 0; i < files.length; i++) {
            File childFile = files[i];
            if ("dashboard.json".equals(childFile.getName())) {
                content = FileUtil.readFile(childFile);
            } else if ("image".equals(childFile.getName())) {
                File[] imageFiles = childFile.listFiles();
                //所有需要上传的图片
                for (File imageFile : imageFiles) {
                    //查看是否存在此image
//                    String fileName = imageFile.getName().split("\\.")[0];
//                    GridFsResource fsFile = fileOperationUtil.getFile(fileName);
//                    if (null == fsFile) {
                        FileInfo info = fileOperationUtil.save(imageFile);
                        log.info("存入图片: {}", info.getUrl());
                        fileMap.put(info.getId(), info.getUrl());
//                    }
                }
            }

        }

        //解析cotent
        ReportDashboardObjectDto detail = JSONObject.parseObject(content, ReportDashboardObjectDto.class);
        //将涉及到的图片路径替换（1.背景图，2.组件为图片的）
        String backgroundImage = detail.getDashboard().getBackgroundImage();
        if (StringUtils.isNotBlank(backgroundImage)) {
            detail.getDashboard().setBackgroundImage(replaceUrl(backgroundImage, fileMap));
        }
        detail.getWidgets().stream()
                .filter(reportDashboardWidgetDto -> "widget-image".equals(reportDashboardWidgetDto.getType()))
                .forEach(reportDashboardWidgetDto -> {
                    String imageAddress = reportDashboardWidgetDto.getValue().getSetup().getString("imageAdress");
                    String address = replaceUrl(imageAddress, fileMap);
                    reportDashboardWidgetDto.getValue().getSetup().put("imageAdress", address);
                    reportDashboardWidgetDto.getOptions().getJSONArray("setup").getJSONObject(4).put("value", address);
                });
        //将新的大屏编码赋值
        detail.setReportCode(reportCode);
        //解析结束，删除临时文件夹
        FileUtil.delete(path);
        log.info("解析成功，开始存入数据库...");
        insertDashboard(detail);
    }

    @Override
    public void deleteByReportIds(List<String> ids) {
        QueryWrapper<ReportDashboard> wrapper = new QueryWrapper<>();
        wrapper.in("reportCode", ids);
        remove(wrapper);
    }



    /**
     * 将大屏涉及到的图片存入指定文件夹
     * @param fileId
     * @param path
     */
    private void zipLoadImage(String fileId, String path) {
        try {
            if (fileId.startsWith("/file/view/")) {
                fileId = fileId.substring("/file/view/".length());
                GridFsResource file = fileOperationUtil.getFile(fileId);
                if (null != file) {
                    path = path + "/image/" + file.getFilename();
                    //path = /upload/zip/UUID/image
                    File file1 = new File(path);
                    FileUtils.copyInputStreamToFile(file.getInputStream(),file1);
//                    FileUtil.copyFileUsingFileChannels(file, file1);
                }
            }
        } catch (Exception e) {
            log.error("压缩图片出现错误");
        }
    }

    private String replaceUrl(String imageAddress, Map<String, String> fileMap) {
        String fileId = "";
        if (imageAddress.startsWith("/file/view/")) {
            fileId = imageAddress.substring("/file/view/".length());
        }
        String orDefault = fileMap.getOrDefault(fileId, null);
        if (StringUtils.isBlank(orDefault)) {
            return imageAddress;
        }
        return orDefault;
    }

    public List<JSONObject> buildTimeLine(List<JSONObject> data, ChartDto dto) {
        Map<String, String> chartProperties = dto.getChartProperties();
        if (null == chartProperties || chartProperties.size() < 1) {
            return data;
        }
        Map<String, Object> contextData = dto.getContextData();
        if (null == contextData || contextData.size() < 1) {
            return data;
        }
        if (contextData.containsKey("startTime") && contextData.containsKey("endTime")) {
            dto.setStartTime(contextData.get("startTime").toString());
            dto.setEndTime(contextData.get("endTime").toString());
        }
        if (StringUtils.isBlank(dto.getStartTime()) || StringUtils.isBlank(dto.getEndTime())) {
            return data;
        }
        //获取时间轴字段和解析时间颗粒度
        chartProperties.forEach((key, value) -> {
            dto.setParticles(value);
            setTimeLineFormat(dto);
            if (StringUtils.isNotBlank(dto.getDataTimeFormat())) {
                dto.setTimeLineFiled(key);
                return;
            }
        });

        if (StringUtils.isBlank(dto.getDataTimeFormat())) {
            return data;
        }

        Date beginTime = DateUtil.parse(dto.getStartTime(),"yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.parse(dto.getEndTime(),"yyyy-MM-dd HH:mm:ss");
        SimpleDateFormat showFormat = new SimpleDateFormat(dto.getTimeLineFormat());
        SimpleDateFormat dataFormat = new SimpleDateFormat(dto.getDataTimeFormat());


        Calendar calendar = Calendar.getInstance();
        calendar.setTime(beginTime);

        Calendar calendarEnd = Calendar.getInstance();
        calendarEnd.setTime(endTime);

        List<String> timeLine = new ArrayList<>();
        List<String> dataTimeline = new ArrayList<>();
        timeLine.add(showFormat.format(calendar.getTime()));
        dataTimeline.add(dataFormat.format(calendar.getTime()));

        //添加时间轴数据
        while (true) {
            calendar.add(dto.getTimeUnit(), 1);
            timeLine.add(showFormat.format(calendar.getTime()));
            dataTimeline.add(dataFormat.format(calendar.getTime()));
            if (showFormat.format(calendar.getTime()).equals(showFormat.format(calendarEnd.getTime()))) {
                break;
            }
        }

        //根据时间轴生成对应的时间线，数据不存在，补数据
        List<JSONObject> result = new ArrayList<>();
        JSONObject jsonDemo = data.get(0);
        String timeLineFiled = dto.getTimeLineFiled();
        for (String dateFormat : dataTimeline) {
            boolean flag = true;
            for (JSONObject datum : data) {
                if (datum.containsKey(timeLineFiled) && datum.getString(timeLineFiled).equals(dateFormat)) {
                    result.add(datum);
                    flag = false;
                }
            }
            if (flag) {
                //补数据
                JSONObject json = new JSONObject();
                jsonDemo.forEach((s, o) -> {
                    if (s.equals(timeLineFiled)) {
                        json.put(timeLineFiled, dateFormat);
                    } else {
                        json.put(s, 0);
                    }
                });
                result.add(json);
            }

        }
        return result;
    }

    //设置时间格式
    private void setTimeLineFormat(ChartDto dto) {
        String particles = dto.getParticles();
        if ("xAxis-hour".equals(particles)) {
            dto.setDataTimeFormat("yyyy-MM-dd HH");
            dto.setTimeLineFormat("MM-dd HH");
            dto.setTimeUnit(Calendar.HOUR);
        } else if ("xAxis-day".equals(particles)) {
            dto.setDataTimeFormat("yyyy-MM-dd");
            dto.setTimeLineFormat("yyyy-MM-dd");
            dto.setTimeUnit(Calendar.DATE);
        } else if ("xAxis-month".equals(particles)) {
            dto.setDataTimeFormat("yyyy-MM");
            dto.setTimeLineFormat("yyyy-MM");
            dto.setTimeUnit(Calendar.MONTH);
        } else if ("xAxis-year".equals(particles)) {
            dto.setDataTimeFormat("yyyy");
            dto.setTimeLineFormat("yyyy");
            dto.setTimeUnit(Calendar.YEAR);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, ChartStrategy> beanMap = applicationContext.getBeansOfType(ChartStrategy.class);
        //遍历该接口的所有实现，将其放入map中
        for (ChartStrategy serviceImpl : beanMap.values()) {
            queryServiceImplMap.put(serviceImpl.type(), serviceImpl);
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
