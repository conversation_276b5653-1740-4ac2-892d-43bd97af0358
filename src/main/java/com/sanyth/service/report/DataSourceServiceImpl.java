package com.sanyth.service.report;

import cn.hutool.log.StaticLog;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.core.common.JdbcConstants;
import com.sanyth.dto.DataSourceDto;
import com.sanyth.mapper.report.DataSourceMapper;
import com.sanyth.model.report.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class DataSourceServiceImpl extends ServiceImpl<DataSourceMapper, DataSource> implements DataSourceService {

    @Resource(name = "dataSourceRestTemplate")
    private RestTemplate restTemplate;
    @Resource
    private RedisTemplate redisTemplate;
    @Autowired
    private JdbcService jdbcService;

    @Override
    public Boolean testConnection(DataSource dataSource) throws Exception {
        DataSourceDto dto = new DataSourceDto();
        dto.setSourceConfig(dataSource.getSourceConfig());
        switch (dataSource.getSourceType()) {
            case JdbcConstants.MYSQL:
            case JdbcConstants.KUDU_IMAPLA:
            case JdbcConstants.ORACLE:
            case JdbcConstants.SQL_SERVER:
            case JdbcConstants.JDBC:
                testRelationalDb(dto);
                break;
            case JdbcConstants.HTTP:
                testHttp(dto);
                break;
            default:
                throw new Exception("未找得到数据源类型");
        }
        StaticLog.info("测试连接成功：{}", JSONObject.toJSONString(dto));
        return true;
    }

    @Override
    public long total(DataSourceDto dataSourceDto, Map<String, Object> contextData) throws Exception {
        //区分数据类型
        String sourceType = dataSourceDto.getSourceType();
        switch (sourceType) {
            case JdbcConstants.ELASTIC_SEARCH_SQL:
                return 0;
            case JdbcConstants.MYSQL:
            case JdbcConstants.ORACLE:
            case JdbcConstants.SQL_SERVER:
                return sqlTotal(dataSourceDto, contextData);
            case JdbcConstants.HTTP:

            default:
                return 0;
        }
    }

    private long sqlTotal(DataSourceDto sourceDto, Map<String, Object> contextData) throws Exception {
        StringBuilder dynSentence = new StringBuilder(sourceDto.getDynSentence());
        String sql = "select count(1) as COUNT from (" + dynSentence + ")";
        sourceDto.setDynSentence(sql);
        List<JSONObject> result = execute(sourceDto);
        log.info("当前total：{}, 添加分页参数,sql语句：{}", JSONObject.toJSONString(result), sourceDto.getDynSentence());
        long count = 0L;
        if (result.size() > 0) {
            count = result.get(0).getLongValue("COUNT");
        }
        //sql 拼接 limit 分页信息
        int pageNumber = Integer.parseInt(contextData.getOrDefault("pageNumber", "1").toString());
        int pageSize = Integer.parseInt(contextData.getOrDefault("pageSize", "5").toString());
        String pageSql = "select * from ( select rownum sytnum,a.* from (";
        String sqlLimit = " ) a where rownum <= " + getEndElement(pageNumber, pageSize, count) + ") where sytnum >=" + getBeginElement(pageNumber,pageSize);
        //处理页面排序
        if (contextData.containsKey("sortable")) {
            dynSentence.append(" order by ");
            Map<String,String> orderMap = (Map<String, String>) contextData.get("sortable");
            StringBuilder finalDynSentence = dynSentence;
            orderMap.forEach((prop,order)->{
                order = "descending".equals(order) ? "desc" : "";
                finalDynSentence.append("to_number(regexp_substr(").append(prop).append(",'[0-9]*[0-9]',1)) ").append(order).append(",");
            });
            dynSentence = finalDynSentence.deleteCharAt(finalDynSentence.length() - 1);
        }
        sourceDto.setDynSentence(pageSql.concat(String.valueOf(dynSentence)).concat(sqlLimit));
        return count;
    }

    private int getBeginElement(int pageNumber,int pageSize) {
        return (pageNumber-1) * pageSize +1;
    }
    private long getEndElement(int pageNumber,int pageSize,long maxElements) {
        return (pageNumber*pageSize >=maxElements ? maxElements : pageNumber*pageSize);
    }
    @Override
    public List<JSONObject> execute(DataSourceDto dto) throws SQLException {
        log.info("数据集最终SQL:{}", dto.getDynSentence());
        String sourceType = dto.getSourceType();
        switch (sourceType) {
//            case JdbcConstants.ELASTIC_SEARCH_SQL:
//                return executeElasticsearchSql(dto);
            case JdbcConstants.MYSQL:
            case JdbcConstants.ORACLE:
            case JdbcConstants.SQL_SERVER:
                return executeRelationalDb(dto);
            case JdbcConstants.HTTP:
                return executeHttp(dto);
            default:
                return null;
        }
    }

    @Override
    public Page<DataSource> queryPage(BaseQuery<DataSource> query) {
        Page<DataSource> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DataSource> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    private Wrapper<DataSource> buildWrapper(DataSource query) {
        QueryWrapper<DataSource> wrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(query.getSourceName())) {
            wrapper.like("SOURCENAME", query.getSourceName());
        }
        wrapper.orderByDesc("createTime");
        return wrapper;
    }

    public List<JSONObject> executeRelationalDb(DataSourceDto dto) throws SQLException {
        analysisRelationalDbConfig(dto);
        Connection pooledConnection = null;
        List<JSONObject> list = new ArrayList<>();
        try {
            pooledConnection = jdbcService.getPooledConnection(dto);

            PreparedStatement statement = pooledConnection.prepareStatement(dto.getDynSentence());
            ResultSet rs = statement.executeQuery();

            int columnCount = rs.getMetaData().getColumnCount();

            List<String> columns = new ArrayList<>();
            for (int i = 1; i <= columnCount; i++) {
                String columnName = rs.getMetaData().getColumnLabel(i);
                columns.add(columnName);
            }
            while (rs.next()) {
                JSONObject jo = new JSONObject();
                columns.forEach(t -> {
                    try {
                        Object value = rs.getObject(t);
                        jo.put(t, value);
                    } catch (SQLException throwable) {
                        log.error(throwable.getMessage(),throwable);
                    }
                });
                list.add(jo);
            }
        } catch (Exception throwable) {
            log.error(throwable.getMessage(),throwable);
            throw throwable;
        } finally {
            try {
                if (pooledConnection != null) {
                    pooledConnection.close();
                }
            } catch (SQLException throwable) {
                log.error(throwable.getMessage(),throwable);
                throw throwable;
            }
        }
        return list;
    }

    /**
     * http 执行获取数据
     *
     * @param dto
     */
    public List<JSONObject> executeHttp(DataSourceDto dto) {
        analysisHttpConfig(dto);
        HttpHeaders headers = new HttpHeaders();
        headers.setAll(JSONObject.parseObject(dto.getHeader(), Map.class));
        HttpEntity<String> entity = new HttpEntity<>(dto.getDynSentence(), headers);
        ResponseEntity<Object> exchange = null;
        try {
            exchange = restTemplate.exchange(dto.getApiUrl(), HttpMethod.valueOf(dto.getMethod()), entity, Object.class);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            throw new RuntimeException(e.getMessage(),e);
        }
        List<JSONObject> result = new ArrayList<>();
        if (!exchange.getStatusCode().isError()) {
            Object body = exchange.getBody();
            String jsonStr = JSONObject.toJSONString(body);
            if (jsonStr.trim().startsWith("{") && jsonStr.trim().endsWith("}")) {
                //JSONObject
                result.add(JSONObject.parseObject(jsonStr));
            } else if (jsonStr.trim().startsWith("[") && jsonStr.trim().endsWith("]")) {
                //List
                result = JSONArray.parseArray(jsonStr, JSONObject.class);
            } else {
                result.add(new JSONObject());
            }
        }
        return result;
    }

    public void testRelationalDb(DataSourceDto dto) throws Exception {
        analysisRelationalDbConfig(dto);
        try {
            Connection unPooledConnection = jdbcService.getUnPooledConnection(dto);
            String catalog = unPooledConnection.getCatalog();
            log.info("数据库测试连接成功：{}", catalog);
            unPooledConnection.close();
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            throw new Exception(e.getMessage());
        }
    }

    public void analysisRelationalDbConfig(DataSourceDto dto) {
        JSONObject json = JSONObject.parseObject(dto.getSourceConfig());
        String jdbcUrl = json.getString("jdbcUrl");
        String username = json.getString("username");
        String password = json.getString("password");
        String driverName = json.getString("driverName");
        dto.setJdbcUrl(jdbcUrl);
        dto.setDriverName(driverName);
        dto.setUsername(username);
        dto.setPassword(password);
    }

    public void testHttp(DataSourceDto dto) throws Exception {
        analysisHttpConfig(dto);
        String apiUrl = dto.getApiUrl();
        String method = dto.getMethod();
        String body = dto.getBody();
        HttpHeaders headers = new HttpHeaders();
        headers.setAll(JSONObject.parseObject(dto.getHeader(), Map.class));
        HttpEntity<String> entity = new HttpEntity<>(body, headers);
        ResponseEntity<Object> exchange;
        try {
            exchange = restTemplate.exchange(apiUrl, HttpMethod.valueOf(method), entity, Object.class);
            if (exchange.getStatusCode().isError()) {
                throw new Exception(exchange.getBody().toString());
            }
        } catch (RestClientException e) {
            throw new Exception(e.getMessage());
        }
    }

    //TODO HTTP
    public void analysisHttpConfig(DataSourceDto dto) {
        JSONObject json = JSONObject.parseObject(dto.getSourceConfig());
        String apiUrl = json.getString("apiUrl");
        String method = json.getString("method");
        String header = json.getString("header");
        String body = json.getString("body");
        //解决url中存在的动态参数
//        apiUrl = dataSetParamService.transform(dto.getContextData(), apiUrl);
        //请求头中动态参数
//        header = dataSetParamService.transform(dto.getContextData(), header);
        dto.setApiUrl(apiUrl);
        dto.setMethod(method);
        dto.setHeader(header);
        dto.setBody(body);
    }
}
