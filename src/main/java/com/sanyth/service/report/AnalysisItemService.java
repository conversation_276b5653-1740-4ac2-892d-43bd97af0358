package com.sanyth.service.report;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.model.report.AnalysisItem;

import java.util.List;

public interface AnalysisItemService extends IService<AnalysisItem> {
    Page<AnalysisItem> queryPage(BaseQuery<AnalysisItem> query);

    List<AnalysisItem> queryList(AnalysisItem query);
}
