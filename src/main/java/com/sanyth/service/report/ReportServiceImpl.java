package com.sanyth.service.report;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.mapper.report.ReportMapper;
import com.sanyth.model.report.Report;
import com.sanyth.model.report.ReportDashboard;
import com.sanyth.model.report.ReportDashboardWidget;
import com.sanyth.model.system.SytPermissionAccount;
import com.sanyth.util.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-02
 */
@Service
public class ReportServiceImpl extends ServiceImpl<ReportMapper, Report> implements IReportService {

    @Resource
    private IReportDashboardService reportDashboardService;
    @Resource
    private IReportDashboardWidgetService reportDashboardWidgetService;

    @Override
    public Page<Report> queryPage(BaseQuery<Report> query) {
        Page<Report> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<Report> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    @Override
    public List<Report> queryList(Report query) {
        return list(buildWrapper(query));
    }

    @Override
    public Page<Report> getList(BaseQuery<Report> query) {
        Page<Report> page = new Page<>(query.getPage(), query.getPageSize());
        QueryWrapper<Report> queryWrapper = buildWrapper(query.getQueryParam());
        SytPermissionAccount user = SecurityUtils.getUser();
        queryWrapper.and(wrapper -> wrapper.isNull("ROLEID").or().like("ROLEID", "[]").or().like("ROLEID", user.getRoles().get(0).getId()));
        queryWrapper.and(wrapper -> {
            wrapper.isNull("ORGID").or().like("ORGID", "[]");
            if (CollectionUtils.isNotEmpty(user.getOrganizationList())) {
                wrapper.or(subWrapper -> {
                    user.getOrganizationList().forEach(organization -> {
                        subWrapper.or().like("ORGID", organization.getId());
                    });
                });
            }
        });
        return page(page, queryWrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copy(String id,String createUser) {
        Report select = getById(id);
        Report report = new Report();
        BeanUtils.copyProperties(select, report, "id");
        report.setCreateTime(new Date());
        report.setCreateUser(createUser);
        save(report);
        QueryWrapper<ReportDashboard> dashboardWrapper = new QueryWrapper<>();
        dashboardWrapper.eq("reportCode", id);
        List<ReportDashboard> dashboards = reportDashboardService.list(dashboardWrapper);
        List<ReportDashboard> dashboardList = new ArrayList<>();
        dashboards.forEach(dash->{
            ReportDashboard dashboard = new ReportDashboard();
            BeanUtils.copyProperties(dash, dashboard, "id", "reportCode");
            dashboard.setReportCode(report.getId());
            dashboardList.add(dashboard);
        });
        reportDashboardService.saveBatch(dashboardList);

        QueryWrapper<ReportDashboardWidget> widgetWrapper = new QueryWrapper<>();
        widgetWrapper.eq("reportCode", id);
        List<ReportDashboardWidget> dashboardWidgets = reportDashboardWidgetService.list(widgetWrapper);
        List<ReportDashboardWidget> dashboardWidgetList = new ArrayList<>();
        dashboardWidgets.forEach(widget->{
            ReportDashboardWidget dashboardWidget = new ReportDashboardWidget();
            BeanUtils.copyProperties(widget, dashboardWidget, "id", "reportCode");
            dashboardWidget.setReportCode(report.getId());
            dashboardWidgetList.add(dashboardWidget);
        });
        reportDashboardWidgetService.saveBatch(dashboardWidgetList);

    }

    @Override
    @Transactional
    public void deleteByIds(List<String> ids) {
        removeByIds(ids);
        reportDashboardService.deleteByReportIds(ids);
        reportDashboardWidgetService.deleteByReportIds(ids);
    }

    private QueryWrapper<Report> buildWrapper(Report query) {
        QueryWrapper<Report> queryWrapper = new QueryWrapper<>();
        // Query condition...

        if (StringUtils.isNoneBlank(query.getReportName())) {
            queryWrapper.like("reportName", query.getReportName());
        }
        if (StringUtils.isNoneBlank(query.getReportRroup())) {
            queryWrapper.like("reportGroup", query.getReportRroup());
        }
        if (StringUtils.isNoneBlank(query.getReportType())) {
            queryWrapper.eq("reportType", query.getReportType());
        }
        queryWrapper.orderByDesc("createTime");
        return queryWrapper;
    }
}
