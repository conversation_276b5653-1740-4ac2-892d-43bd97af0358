package com.sanyth.service.report;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.mapper.report.DecisionItemMapper;
import com.sanyth.model.report.DecisionItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class DecisionItemServiceImpl extends ServiceImpl<DecisionItemMapper, DecisionItem> implements DecisionItemService {
    @Override
    public Page<DecisionItem> queryPage(BaseQuery<DecisionItem> query) {
        Page<DecisionItem> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<DecisionItem> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    private Wrapper<DecisionItem> buildWrapper(DecisionItem query) {
        QueryWrapper<DecisionItem> wrapper = new QueryWrapper<>();
        if (StringUtils.isNoneBlank(query.getRoleId())) {
            wrapper.like("roleId", query.getRoleId());
        }
        if (StringUtils.isNoneBlank(query.getName())) {
            wrapper.like("name", query.getName());
        }
        wrapper.orderByAsc("sort");
        return wrapper;
    }


}
