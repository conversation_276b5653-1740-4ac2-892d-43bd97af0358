package com.sanyth.service.report;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.sanyth.dto.ChartDto;
import com.sanyth.dto.ReportDashboardDto;
import com.sanyth.dto.ReportDashboardObjectDto;
import com.sanyth.model.report.ReportDashboard;
import org.springframework.http.ResponseEntity;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.FileNotFoundException;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-03
 */
public interface IReportDashboardService extends IService<ReportDashboard> {

    ReportDashboardObjectDto getDetail(ReportDashboardDto reportDashboardDto);

    void insertDashboard(ReportDashboardObjectDto dto);

    Map<String,Object> getChartData(ChartDto dto) throws Exception;

    ResponseEntity<byte[]> exportDashboard(HttpServletRequest request, HttpServletResponse response, String reportCode, Integer showDataSet);

    void importDashboard(MultipartFile file, String reportCode) throws FileNotFoundException, Exception;

    void deleteByReportIds(List<String> ids);

    ResponseEntity<byte[]> exportDirllData(HttpServletRequest request, HttpServletResponse response, List<JSONObject> chartData);
}
