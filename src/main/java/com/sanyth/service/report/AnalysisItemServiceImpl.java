package com.sanyth.service.report;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sanyth.core.common.BaseQuery;
import com.sanyth.mapper.report.AnalysisItemMapper;
import com.sanyth.model.report.AnalysisItem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class AnalysisItemServiceImpl extends ServiceImpl<AnalysisItemMapper, AnalysisItem> implements AnalysisItemService {
    @Override
    public Page<AnalysisItem> queryPage(BaseQuery<AnalysisItem> query) {
        Page<AnalysisItem> page = new Page<>(query.getPage(), query.getPageSize());
        Wrapper<AnalysisItem> wrapper = buildWrapper(query.getQueryParam());
        return page(page, wrapper);
    }

    @Override
    public List<AnalysisItem> queryList(AnalysisItem query) {
        Wrapper<AnalysisItem> wrapper = buildWrapper(query);
        return list(wrapper);
    }

    private Wrapper<AnalysisItem> buildWrapper(AnalysisItem query) {
        QueryWrapper<AnalysisItem> wrapper = new QueryWrapper<>();
        if (StringUtils.isNoneBlank(query.getRoleId())) {
            wrapper.like("role_Id", query.getRoleId());
        }
        if (StringUtils.isNoneBlank(query.getReportId())) {
            wrapper.eq("report_Id", query.getReportId());
        }
        if (StringUtils.isNoneBlank(query.getName())) {
            wrapper.like("name", query.getName());
        }
        if (StringUtils.isNoneBlank(query.getTopic())) {
            wrapper.eq("topic", query.getTopic());
        }
        if (StringUtils.isNoneBlank(query.getStatus())) {
            wrapper.eq("status", query.getStatus());
        }
        wrapper.orderByAsc("sort");
        return wrapper;
    }


}
