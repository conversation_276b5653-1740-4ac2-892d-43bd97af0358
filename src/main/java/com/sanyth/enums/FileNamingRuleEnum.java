package com.sanyth.enums;

/**
 * 文件命名规则枚举
 * 
 * <AUTHOR>
 * @since 2025-01-04
 */
public enum FileNamingRuleEnum {
    
    /**
     * 考生号_姓名_专业名称
     */
    KSH_XM_ZYMC("ksh_xm_zymc", "考生号_姓名_专业名称", "{ksh}_{xm}_{zymc}"),

    /**
     * 身份证号_姓名_专业名称
     */
    SFZH_XM_ZYMC("sfzh_xm", "身份证号_姓名_专业名称", "{sfzh}_{xm}_{zymc}"),
    
    /**
     * 考生号_姓名_学院名称
     */
    KSH_XM_XYMC("ksh_xm_xymc", "考生号_姓名_学院名称", "{ksh}_{xm}_{xymc}"),

    /**
     * 身份证号_姓名_学院名称
     */
    SFZH_XM_XYMC("ksh_xm_xymc", "身份证号_姓名_学院名称", "{sfzh}_{xm}_{xymc}"),

    /**
     * 考生号_姓名
     */
    KSH_XM("ksh_xm", "考生号_姓名", "{ksh}_{xm}"),

    /**
     * 身份证号_姓名
     */
    SFZH_XXM("xm_ksh", "身份证号_姓名", "{sfzh}_{xm}"),

    /**
     * 仅考生号
     */
    KSH_ONLY("ksh_only", "仅考生号", "{ksh}"),

    /**
     * 仅身份证号
     */
    SFZH_ONLY("sfzh_only", "仅身份证号", "{sfzh}"),
    
    /**
     * 仅姓名
     */
    XM_ONLY("xm_only", "仅姓名", "{xm}"),
    
    /**
     * 自定义规则
     */
    CUSTOM("custom", "自定义规则", "");
    
    private final String code;
    private final String name;
    private final String template;
    
    FileNamingRuleEnum(String code, String name, String template) {
        this.code = code;
        this.name = name;
        this.template = template;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getName() {
        return name;
    }
    
    public String getTemplate() {
        return template;
    }
    
    /**
     * 根据代码获取枚举
     */
    public static FileNamingRuleEnum getByCode(String code) {
        for (FileNamingRuleEnum rule : values()) {
            if (rule.getCode().equals(code)) {
                return rule;
            }
        }
        return KSH_XM_ZYMC; // 默认规则
    }
}
