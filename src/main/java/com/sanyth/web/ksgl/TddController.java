
package com.sanyth.web.ksgl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.rpamis.security.annotation.Desensitizationed;
import com.sanyth.core.common.*;
import com.sanyth.core.config.PDFExportConfig;
import com.sanyth.core.excel.CommonWriteEntity;
import com.sanyth.core.exception.BusinessException;
import com.sanyth.core.support.Condition;
import com.sanyth.core.support.Query;
import com.sanyth.dto.*;
import com.sanyth.mapper.ksgl.TddMapper;
import com.sanyth.model.ksgl.Ksjl;
import com.sanyth.model.ksgl.Tdd;
import com.sanyth.model.ksgl.Tjxx;
import com.sanyth.model.system.CustomCol;
import com.sanyth.model.system.DictField;
import com.sanyth.pojo.ImportErrorInfo;
import com.sanyth.service.ksgl.IKsjlService;
import com.sanyth.service.ksgl.ITddService;
import com.sanyth.service.ksgl.ITjxxService;
import com.sanyth.service.system.ICustomColService;
import com.sanyth.service.system.IDictFieldService;
import com.sanyth.service.system.SytSysParamService;
import com.sanyth.service.zsxt.ILqtzsRulesService;
import com.sanyth.util.*;
import com.sanyth.enums.FileNamingRuleEnum;
import com.sanyth.util.FileNamingUtil;
import com.sanyth.vo.TddVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.beans.BeanUtils;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static com.sanyth.core.common.Constants.*;

/**
 * 考生投档表 控制器
 *
 * <AUTHOR> @since 2024-02-23
 */
@RestController
@AllArgsConstructor
@RequestMapping("/ksgl/tdd")
@Api(value = "考生投档表", tags = "考生投档表接口")
public class TddController extends BaseController {

	private static final Logger log = LoggerFactory.getLogger(TddController.class);
	private final FileOperationUtil fileOperationUtil;
    private ITddService tddService;
    private TddMapper tddMapper;
    private PDFExportConfig pdfExportConfig;
    private ICustomColService customColService;
    private IKsjlService ksjlService;
    private ITjxxService tjxxService;
    private SytSysParamService sysParamService;
    private ILqtzsRulesService lqtzsRulesService;
    private DownloadTaskHelper downloadTaskHelper;
    private IDictFieldService dictFieldService;

    /**
     * 详情
     */
    @GetMapping("/detail")
    @ApiOperation(value = "详情", notes = "传入tdd")
    @Desensitizationed
    public R<Tdd> detail(Tdd tdd) {
        Tdd detail = tddService.getOne(Condition.getQueryWrapper(tdd));
        return R.data(detail);
    }

    @GetMapping("/one")
    @ApiOperation(value = "详情(未脱敏)", notes = "传入tdd")
    public R<Tdd> one(Tdd tdd) {
        Tdd detail = tddService.getOne(Condition.getQueryWrapper(tdd));
        return R.data(detail);
    }

    /**
     * 分页 考生投档表
     */
    @GetMapping("/list")
    @ApiOperation(value = "分页", notes = "传入tdd")
    @Desensitizationed
    public R<IPage<Tdd>> list(Tdd tdd, Query query) {
        String nf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : nf);
        //预科生
        String ykszt = tdd.getYkszt();
        if (StringUtils.isNotBlank(ykszt)) {
            tdd.setYkszt(null);
        }
        Map<String, Condition.QueryOperator> operatorMap = new HashMap<>();
        operatorMap.put("cc", Condition.QueryOperator.LIKE);
        operatorMap.put("xm", Condition.QueryOperator.LIKE);
        QueryWrapper<Tdd> queryWrapper = Condition.getQueryWrapper(tdd,operatorMap);
        if (StringUtils.isNotBlank(ykszt)) {
            queryWrapper.in("ykszt", ykszt.split(","));
        }
        queryWrapper.orderByDesc("nf").orderByAsc("lqtzssxh");
        IPage<Tdd> pages = tddService.page(Condition.getPage(query), queryWrapper);

        return R.data(pages);
    }

    /**
     * 全部 考生投档表
     */
    @GetMapping("/all")
    @ApiOperation(value = "全部", notes = "传入tdd")
    @Desensitizationed
    public R<List<Tdd>> list(Tdd tdd) {
        String nf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : nf);
        List<Tdd> pages = tddService.list(Condition.getQueryWrapper(tdd));
        return R.data(pages);
    }

    @GetMapping("/allnf")
    @ApiOperation(value = "全部", notes = "传入tdd")
    public R<List<TddVO>> allnf() {
        return R.data(tddMapper.findAllNf());
    }

    @GetMapping("/selectByField")
    @ApiOperation(value = "全部", notes = "传入tdd")
    public R<List<TddVO>> select(Tdd tdd,String field) {
        String nf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : nf);
        return R.data(tddMapper.selectByField(Condition.getQueryWrapper(tdd),field));
    }

    /**
     * 自定义分页 考生投档表
     */
    @GetMapping("/page")
    @ApiOperation(value = "分页", notes = "传入tdd")
    @Desensitizationed
    public R<IPage<TddVO>> page(TddVO tdd, Query query) {
        String nf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : nf);
//        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : String.valueOf(DateUtil.thisYear()));
        IPage<TddVO> pages = tddService.selectTddPage(Condition.getPage(query), tdd);
        return R.data(pages);
    }

    /**
     * 新增 考生投档表
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增", notes = "传入tdd")
    public R save(@Valid @RequestBody Tdd tdd) {
        return R.status(tddService.save(tdd));
    }

    /**
     * 修改 考生投档表
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改", notes = "传入tdd")
    public R update(@Valid @RequestBody Tdd tdd) {
        return R.status(tddService.updateById(tdd));
    }

    /**
     * 新增或修改 考生投档表
     */
    @PostMapping("/submit")
    @ApiOperation(value = "新增或修改", notes = "传入tdd")
    public R submit(@Valid @RequestBody Tdd tdd) {
        return R.status(tddService.saveOrUpdate(tdd));
    }


    /**
     * 删除 考生投档表
     */
    @PostMapping("/remove")
    @ApiOperation(value = "删除", notes = "传入ids")
    public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        return R.status(tddService.removeByIds(Func.toStrList(ids)));
    }

    /** 清空考生数据
     * @param tdd  tdd
     * @param qklx 清空类型:tdd,zsjh
     * @return {@link R}
     */
    @RequestMapping("/tddclear")
    public R tddclear(Tdd tdd,@RequestParam("qklx") String qklx) throws Exception {
        tddService.dataClear(tdd, qklx);
        return R.success("清空成功");
    }

    @GetMapping("/selecRrevisedPage")
    @ApiOperation(value = "分页", notes = "传入tdd")
    public R<IPage<Map<String, Object>>> selecRrevisedPage(Tdd tdd, Query query, String type,String xzzt) {
        String nf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : nf);
//        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : String.valueOf(DateUtil.thisYear()));
        Map<String, Condition.QueryOperator> operatorMap = new HashMap<>();
        operatorMap.put("nf", Condition.QueryOperator.EQ);
        operatorMap.put("cc", Condition.QueryOperator.LIKE);
        operatorMap.put("sfmc", Condition.QueryOperator.EQ);
        operatorMap.put("zslx", Condition.QueryOperator.EQ);
        operatorMap.put("lqzydm", Condition.QueryOperator.EQ);
        operatorMap.put("lqzy", Condition.QueryOperator.EQ);
        operatorMap.put("xymc", Condition.QueryOperator.EQ);
        operatorMap.put("lqzymc", Condition.QueryOperator.EQ);
        IPage<Map<String, Object>> page = Condition.getPage(query);
        QueryWrapper<Tdd> queryWrapper = Condition.getQueryWrapper(tdd, operatorMap);
        if ("xyzyxz".equals(type)&&StringUtils.isNotBlank(xzzt)) {
            switch (xzzt) {
                case "yxz":
                    queryWrapper.isNotNull("zymc");
                    break;
                case "wxz":
                    queryWrapper.isNull("zymc");
                    break;
                default:
                    break;
            }
        }
        List<LinkedHashMap<String, Object>> tddVOS = tddMapper.selecRrevisedPage(queryWrapper, type, page);
        List<Map<String, Object>> list = new ArrayList<>();
        tddVOS.forEach(tddVO -> {
            list.add(Snippet.transformUpperCase(tddVO));
        });
        page.setRecords(list);
        return R.data(page);
    }

    /**
     * 数据导出
     */
    @RequestMapping("/export")
    public void export(HttpServletResponse response, @RequestBody(required = false) Map<String, Object> params) {
        try {
            List<List<Object>> lists = new ArrayList<>();
            List<CellRangeAddress> cellRangeAddressList = new ArrayList<>();
            List<List<String>> titles = new ArrayList<>();      //标题
            List<String> titlesEn = new ArrayList<>();

            DictField dictField = new DictField();
            dictField.setScope("学生");
            List<DictField> dictFieldList = dictFieldService.list(Condition.getQueryWrapper(dictField).orderByAsc("sort"));
            dictFieldList.forEach(dictField1 -> {
                titlesEn.add(dictField1.getFielden());
                titles.addAll(Collections.singleton(ListUtil.toList(dictField1.getFieldzh())));
            });

            List<Tdd> list;
            String exportType = (String) params.get("exportType");

            if ("byIds".equals(exportType) && params.containsKey("ids")) {
                String ids = (String) params.get("ids");
                if (StringUtils.isNotBlank(ids)) {
                    QueryWrapper<Tdd> queryWrapper = new QueryWrapper<>();
                    queryWrapper.in("id", ids.split(","));
                    list = tddService.list(queryWrapper);
                } else {
                    list = new ArrayList<>();
                }
            } else {
                params.remove("exportType");
                params.remove("fileType");
                Tdd tdd = new Tdd();
                BeanMap beanMap = BeanMap.create(tdd);
                for (Map.Entry<String, Object> entry : params.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    if (value != null && !value.toString().trim().isEmpty()) {
                        if (beanMap.containsKey(key)) {
                            beanMap.put(key, value.toString());
                        }
                    }
                }
                String nf = sysParamService.getValue(ZSXT_NF);
                if (StringUtils.isBlank(tdd.getNf())) {
                    tdd.setNf(nf);
                }
                Map<String, Condition.QueryOperator> operatorMap = new HashMap<>();
                operatorMap.put("xm", Condition.QueryOperator.LIKE);
                operatorMap.put("ksh", Condition.QueryOperator.LIKE);
                operatorMap.put("zjhm", Condition.QueryOperator.LIKE);

                list = tddService.list(Condition.getQueryWrapper(tdd, operatorMap));
            }

            list.forEach(tdd1 -> {
                List<Object> subList = new ArrayList<>();
                titlesEn.forEach(en->{
                    String value = (String) ReflectUtil.getFieldValue(tdd1, en);
                    subList.add(value);
                });
                lists.add(subList);
            });

            String fileName = URLEncoder.encode("考生信息", "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcelUtil.simpleWrite(new CommonWriteEntity<>(response, "考生信息", titles, lists, cellRangeAddressList));
        } catch (Exception e) {
            log.error("导出失败", e);
        }
    }


    /**
     * 编辑 报到数据等
     *
     * @param param 参数
     * @param ids   ids
     * @return {@link R}
     */
    @PostMapping("/edit")
    @ApiOperation(value = "编辑", notes = "传入ids")
    public R edit(@Valid @RequestBody Map<String, Object> param, @ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
        Tdd tddxz = Func.toBean(param, Tdd.class);
        tddService.edit(ids, tddxz);
        return R.success("修正成功");
    }

    /**
     * 修正
     *
     * @return {@link R}
     */
    @PostMapping("/revised")
    @ApiOperation(value = "修正", notes = "")
    public R revisedsed(@Valid @RequestBody Map<String, Object> param) {
        List<LinkedHashMap<String, Object>> list = (List<LinkedHashMap<String, Object>>) param.get("tdd");
        LinkedHashMap query = (LinkedHashMap) param.get("query");
        list.add(query);
        LinkedHashMap obj = (LinkedHashMap) param.get("tddxz");
        Tdd tddxz = obj == null ? null : Func.toBean(obj, Tdd.class);

        tddService.revisedsed(list, tddxz);
        return R.success("修正成功");
    }

    /**
     * 一键修正
     *
     * @return {@link R}
     */
    @PostMapping("/oneKeyRevised")
    @ApiOperation(value = "一键修正", notes = "")
    public R oneKeyRevised(@Valid @RequestBody Map<String, Object> param) {
        String type = (String) param.get("type");
        String nf = (String) param.get("nf");
        List<LinkedHashMap<String, Object>> list = (List<LinkedHashMap<String, Object>>) param.get("list");
        Tdd tdd = new Tdd();
        String zsnf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(nf) ? nf : zsnf);
//        tdd.setNf(StringUtils.isNotBlank(nf) ? nf : String.valueOf(DateUtil.thisYear()));
        tddService.oneKeyRevised(list, tdd, type);
        return R.success("修正成功");
    }

    /**
     * 一键还原
     *
     * @return {@link R}
     */
    @PostMapping("/oneKeyRestore")
    @ApiOperation(value = "一键还原", notes = "")
    public R oneKeyRestore(@Valid @RequestBody Map<String, Object> param) {
        String type = (String) param.get("type");
        String nf = (String) param.get("nf");
        List<LinkedHashMap<String, Object>> list = (List<LinkedHashMap<String, Object>>) param.get("list");
        Tdd tdd = new Tdd();
        String zsnf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(nf) ? nf : zsnf);
//        tdd.setNf(StringUtils.isNotBlank(nf) ? nf : String.valueOf(DateUtil.thisYear()));
        tddService.oneKeyRestore(list, tdd, type);
        return R.success("还原成功");
    }


    /**
     * 自动匹配 例:毕业信息省市区
     *
     * @return {@link R}
     */
    @PostMapping("/autoRevised")
    @ApiOperation(value = "自动匹配", notes = "")
    public R autoRevised(Tdd tdd, String revisedType) {
        String nf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : nf);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : String.valueOf(DateUtil.thisYear()));
        tddService.autoRevised(tdd, revisedType);
        return R.success("匹配成功");
    }


    /**
     * 导入照片、体检信息等
     *
     * @param file     文件
     * @param fileType 文件类型:photo,tjxx
     * @param request  要求
     * @param response 回答
     */
    @RequestMapping("/importPhotos")
    public void importPhotos(@RequestParam("file") MultipartFile file, String fileType, HttpServletRequest request, HttpServletResponse response) {
        try {
            boolean isZipPack = true;
            String separator = File.separator;
            String filePath = request.getSession().getServletContext().getRealPath("/") + "tmp" + separator;
            File tmp = new File(filePath);
            if (!tmp.exists()) {
                boolean mkdirs = tmp.mkdirs();
            }
            if (file == null) {
                throw new BusinessException("请上传文件");
            }
            String contentType = file.getContentType();
            String filename = file.getOriginalFilename();
            //将压缩包保存在指定路径
            String packFilePath = filePath + File.separator + filename;
            if (contentType.contains("zip")) {
                //zip解压缩处理
            } else if (Constants.RAR_FILE.equals(contentType)) {
                //rar解压缩处理
                isZipPack = false;
            } else {
                throw new RuntimeException("上传的压缩包格式不正确,仅支持zip压缩文件!");
            }
            File file1 = new File(packFilePath);
            try {
                file.transferTo(file1);
            } catch (IOException e) {
                e.printStackTrace();
                throw new RuntimeException("压缩文件到:" + filePath + " 失败!");
            }
            Map<String, Object> map;
            if (isZipPack) {
                //zip压缩包
                map = UnPackeUtil.unPackZipToMongo(file1, fileType);
            } else {
                //rar压缩包
                map = UnPackeUtil.unPackRarToMongo(file1, fileType);
            }
            List<Tdd> tddList = new ArrayList<>();
            String nf = sysParamService.getValue(ZSXT_NF);
            for (String ksh : map.keySet()) {
                String fileId = (String) map.get(ksh);
                Tdd tdd = new Tdd();
                tdd.setNf(nf);
                tdd.setKsh(ksh);
                Tdd tdd1 = tddService.getOne(Condition.getQueryWrapper(tdd));
                if (tdd1 == null) {
                    continue;
                }
                switch (fileType) {
                    case "zsxt_photo":
                        tdd1.setPhoto(fileId);
                        break;
                    case "zsxt_tjxx":
                        tdd1.setTjxx(fileId);
                        break;
                    case "zsxt_bmxx":
                        tdd1.setBmxx(fileId);
                        break;
                    case "zsxt_cjyzyxx":
                        tdd1.setCjyzyxx(fileId);
                        break;
                    case "zsxt_sfz":
                        tdd1.setSfz(fileId);
                        break;
                    case "zsxt_fjb":
                        tdd1.setFjb(fileId);
                        break;
                    case "zsxt_wbdwj":
                        tdd1.setWbdwj(fileId);
                        break;
                    case "zsxt_hyzp":
                        tdd1.setHyzp(fileId);
                        break;
                    case "zsxt_xjzp":
                        tdd1.setXjzp(fileId);
                        break;
                    case "zsxt_hysfztxzp":
                        tdd1.setHysfztxzp(fileId);
                        break;
                    case "zsxt_dzqm":
                        tdd1.setDzqm(fileId);
                        break;
                    default:
                        break;
                }
                tddList.add(tdd1);
            }
            tddService.saveOrUpdateBatch(tddList);

            Resp.render(response, ErrorInfo.CODE_00000, "success");
        } catch (Exception e) {
            e.printStackTrace();
            Resp.render(response, ErrorInfo.CODE_00001, "error");
        }
    }

    /**
     * 下载照片
     *
     * @param response 回答
     * @param tdd      tdd
     * @param fileType 文件类型,zsxt_photo,zsxt_tjxx等
     * @param namingRule 命名规则代码（可选）
     * @param customTemplate 自定义命名模板（可选）
     * @throws Exception 例外
     */
    @RequestMapping("/downloadPhotos")
    public void downloadPhotos( HttpServletResponse response,Tdd tdd,@RequestParam("fileType") String fileType,
                               @RequestParam(value = "namingRule", required = false) String namingRule,
                               @RequestParam(value = "customTemplate", required = false) String customTemplate) throws Exception {
        String ids = tdd.getId();
        tdd.setId(null);
        String nf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : nf);
//        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : String.valueOf(DateUtil.thisYear()));
        QueryWrapper<Tdd> queryWrapper = Condition.getQueryWrapper(tdd);
        if (StringUtils.isNotBlank(ids)) {
            queryWrapper.in("id", ids.split(","));
        }
        List<Tdd> list = tddService.list(queryWrapper);
        Map<String, Tdd> tddMap = new HashMap<>();
        list.forEach(tdd1 -> tddMap.put(tdd1.getKsh(), tdd1));

        // 获取命名规则，如果没有指定则使用默认规则或系统配置
        String finalNamingRule = namingRule;
        if (StringUtils.isBlank(finalNamingRule)) {
            // 尝试从系统参数获取默认命名规则
            finalNamingRule = sysParamService.getValue("PHOTO_NAMING_RULE");
            if (StringUtils.isBlank(finalNamingRule)) {
                finalNamingRule = FileNamingRuleEnum.KSH_XM_ZYMC.getCode(); // 默认规则
            }
        }
        if (list.size() > 0) {
            String date = DateUtil.format(new Date(), "yyyyMMddHHmmssSSS");
            String fileName = URLEncoder.encode(date + "_" + "附件信息", "UTF-8");
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".zip");
            try (ZipOutputStream zipOutputStream = new ZipOutputStream(response.getOutputStream());){
                List<GridFsResource> fileList = new ArrayList<>();
                Set<String> fileNameSet = new HashSet<>();
                for (Tdd tdd1 : list) {
                    fileNameSet.add(fileType + "_" + tdd1.getKsh());
                }
                fileList.addAll(fileOperationUtil.getListByIdsOrFileNames(new ArrayList<>(fileNameSet)));
                if (fileList.size() > 0) {
                    for (GridFsResource gridFsResource : fileList) {
                        String fileName1 = gridFsResource.getFilename();
                        String ksh = fileName1.substring(fileName1.lastIndexOf("_") + 1);
                        if (tddMap.containsKey(ksh)) {
                            Tdd tdd1 = tddMap.get(ksh);
                            String contentType = gridFsResource.getContentType();

                            // 使用动态命名规则生成文件名
                            String dynamicFileName = FileNamingUtil.generateFileName(tdd1, finalNamingRule, customTemplate, contentType);

                            zipOutputStream.putNextEntry(new ZipEntry("附件信息/" + dynamicFileName));
                            IOUtils.copy(gridFsResource.getInputStream(), zipOutputStream);
                            zipOutputStream.closeEntry();
                        }

                    }
                }
            }
        }
    }

    /**
     * 获取文件命名规则列表
     *
     * @return 命名规则列表
     */
    @RequestMapping("/getNamingRules")
    public R<List<Map<String, Object>>> getNamingRules() {
        List<Map<String, Object>> rules = new ArrayList<>();

        for (FileNamingRuleEnum rule : FileNamingRuleEnum.values()) {
            Map<String, Object> ruleMap = new HashMap<>();
            ruleMap.put("code", rule.getCode());
            ruleMap.put("name", rule.getName());
            ruleMap.put("template", rule.getTemplate());
            rules.add(ruleMap);
        }

        return R.data(rules);
    }

    /**
     * 获取可用的占位符列表
     *
     * @return 占位符列表
     */
    @RequestMapping("/getAvailablePlaceholders")
    public R<Map<String, String>> getAvailablePlaceholders() {
        return R.data(FileNamingUtil.getAvailablePlaceholders());
    }

    /**
     * 测试文件命名规则
     *
     * @param ksh 考生号
     * @param namingRule 命名规则
     * @param customTemplate 自定义模板
     * @return 生成的文件名
     */
    @RequestMapping("/testNamingRule")
    public R<String> testNamingRule(@RequestParam("ksh") String ksh,
                                   @RequestParam(value = "namingRule", required = false) String namingRule,
                                   @RequestParam(value = "customTemplate", required = false) String customTemplate) {
        try {
            // 根据考生号查询考生信息
            QueryWrapper<Tdd> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("ksh", ksh);
            Tdd tdd = tddService.getOne(queryWrapper);

            if (tdd == null) {
                return R.fail("未找到考生号为 " + ksh + " 的考生信息");
            }

            // 使用默认规则如果没有指定
            if (StringUtils.isBlank(namingRule)) {
                namingRule = FileNamingRuleEnum.KSH_XM_ZYMC.getCode();
            }

            // 生成文件名
            String fileName = FileNamingUtil.generateFileName(tdd, namingRule, customTemplate, "jpg");

            return R.data(fileName);
        } catch (Exception e) {
            log.error("测试命名规则失败", e);
            return R.fail("测试失败：" + e.getMessage());
        }
    }

    /**
     * 下载自定义录取数据导入模板
     *
     * @param response
     */
    @RequestMapping("/downImportTemplate")
    public void downImportTemplate(HttpServletResponse response) {
        try {
            String fileName = URLEncoder.encode("自定义录取数据导入模板", "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            String customImpName = sysParamService.getValue("customImpName");
            if ("TddImportsWHZYDTO".equals(customImpName)) {
                EasyExcel.write(response.getOutputStream(), TddImportsWHZYDTO.class)
                        .excelType(ExcelTypeEnum.XLSX)
                        .sheet("sheet1")
                        .doWrite(new ArrayList());
            } else {
                EasyExcel.write(response.getOutputStream(), TddImportsDTO.class)
                        .excelType(ExcelTypeEnum.XLSX)
                        .sheet("sheet1")
                        .doWrite(new ArrayList());
            }

        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入自定义录取数据
     *
     * @param file     文件
     * @param nf       nf
     * @param sctzs    是否生成通知书
     * @param response 回答
     */
    @RequestMapping("/importZdylqsj")
    public void importZdylqsj(@RequestParam("file") MultipartFile file, String nf,String sctzs, HttpServletResponse response) {
        try {
            LinkedList<Tdd> linkedList = new LinkedList<>();
            String zsnf = sysParamService.getValue(ZSXT_NF);
            nf = StringUtils.isNotBlank(nf) ? nf : zsnf;
            List<ImportErrorInfo> errorInfo = new ArrayList<>();
            String customImpName = sysParamService.getValue("customImpName");
            if ("TddImportsWHZYDTO".equals(customImpName)) {
                List<TddImportsWHZYDTO> list = EasyExcel.read(file.getInputStream(), TddImportsWHZYDTO.class, new TddImportsWHZYDTOListener()).sheet().doReadSync();
                if (!CollectionUtils.isEmpty(list)) {
                    for (TddImportsWHZYDTO tddImportsDTO : list) {
                        boolean error = false;
                        if (StringUtils.isEmpty(tddImportsDTO.getSfmc())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getSfmc(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getKsh())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getKsh(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getXm())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getXm(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getZjhm())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getZjhm(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getLqzymc())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getLqzymc(), "不能为空"));
                            error = true;
                        }

                        if (!error) {
                            QueryWrapper<Tdd> one = new QueryWrapper<>();
                            one.eq("nf", nf);
                            one.eq("sfmc", tddImportsDTO.getSfmc());
                            one.eq("ksh", tddImportsDTO.getKsh());
                            one.eq("xm", tddImportsDTO.getXm());
                            one.eq("zjhm", tddImportsDTO.getZjhm());
                            one.eq("lqzymc", tddImportsDTO.getLqzymc());
                            List<Tdd> tddList = tddService.list(one);
                            if (tddList.size() == 0) {
                                Tdd tdd = new Tdd();
                                tdd.setNf(nf);
                                BeanUtils.copyProperties(tddImportsDTO, tdd, "id");
                                tddService.saveOrUpdate(tdd);
                                linkedList.add(tdd);
                            }
                        }
                    }
                }
            } else {
                List<TddImportsDTO> list = EasyExcel.read(file.getInputStream(), TddImportsDTO.class, new TddImportsDTOListener()).sheet().doReadSync();
                if (!CollectionUtils.isEmpty(list)) {
                    for (TddImportsDTO tddImportsDTO : list) {
                        boolean error = false;
                        if (StringUtils.isEmpty(tddImportsDTO.getCc())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getCc(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getKsh())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getKsh(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getXm())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getXm(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getPcmc())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getPcmc(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getKlmc())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getKlmc(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getZjhm())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getZjhm(), "不能为空"));
                            error = true;
                        }
                        if (StringUtils.isEmpty(tddImportsDTO.getLqzymc())) {
                            errorInfo.add(new ImportErrorInfo(tddImportsDTO.getRowIndex(), tddImportsDTO.getLqzymc(), "不能为空"));
                            error = true;
                        }

                        if (!error) {
                            QueryWrapper<Tdd> one = new QueryWrapper<>();
                            one.eq("nf", nf);
                            one.eq("cc", tddImportsDTO.getCc());
                            one.eq("ksh", tddImportsDTO.getKsh());
                            one.eq("xm", tddImportsDTO.getXm());
                            one.eq("pcmc", tddImportsDTO.getPcmc());
                            one.eq("klmc", tddImportsDTO.getKlmc());
                            one.eq("zjhm", tddImportsDTO.getZjhm());
                            one.eq("lqzymc", tddImportsDTO.getLqzymc());
                            List<Tdd> tddList = tddService.list(one);
                            if (tddList.size() == 0) {
                                Tdd tdd = new Tdd();
                                tdd.setNf(nf);
                                BeanUtils.copyProperties(tddImportsDTO, tdd, "id");
                                tddService.saveOrUpdate(tdd);
                                linkedList.add(tdd);
                            }
                        }
                    }
                }
            }

            if (!CollectionUtils.isEmpty(errorInfo)) {
                // 回写导入错误信息...
                String fileName = URLEncoder.encode("错误信息", "UTF-8");
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf8");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), ImportErrorInfo.class)
                        .excelType(ExcelTypeEnum.XLSX)
                        .sheet("sheet1")
                        .doWrite(errorInfo);
                return;
            }
            sctzs = StringUtils.isEmpty(sctzs) ? HAS_YES : sctzs;
            if (HAS_YES.equals(sctzs) && org.apache.commons.collections4.CollectionUtils.isNotEmpty(linkedList)) {
                Tdd tdd = new Tdd();
                tdd.setNf(nf);
                lqtzsRulesService.generateNotificationNumber(tdd, linkedList);
            }
            Resp.render(response, ErrorInfo.CODE_00000, ErrorInfo.CODE_MSG_00000);
        } catch (Exception e) {
            e.printStackTrace();
            Resp.render(response, ErrorInfo.CODE_00001, ErrorInfo.CODE_MSG_00001);
        }
    }

    /**
     * 下载报到数据模板
     *
     * @param response
     */
    @RequestMapping("/downBdsjTemplate")
    public void downBdsjTemplate(HttpServletResponse response) {
        try {
            String fileName = URLEncoder.encode("报到数据导入模板", "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), BdsjImportsDTO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("sheet1")
                    .doWrite(new ArrayList());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入报到数据
     *
     * @param file     文件
     * @param nf       nf
     * @param response 回答
     */
    @RequestMapping("/importBdsj")
    public void importBdsj(@RequestParam("file") MultipartFile file, String nf, HttpServletResponse response) {
        try {
            String zsnf = sysParamService.getValue(ZSXT_NF);
            nf = StringUtils.isNotBlank(nf) ? nf : zsnf;
            List<BdsjImportsDTO> list = EasyExcel.read(file.getInputStream(), BdsjImportsDTO.class, new BdsjImportsDTOListener()).sheet().doReadSync();
            List<ImportErrorInfo> errorInfo = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                for (BdsjImportsDTO bdsjImportsDTO : list) {
                    boolean error = false;
                    if (StringUtils.isEmpty(bdsjImportsDTO.getKsh())) {
                        errorInfo.add(new ImportErrorInfo(bdsjImportsDTO.getRowIndex(), bdsjImportsDTO.getKsh(), "不能为空"));
                        error = true;
                    }
                    if (StringUtils.isEmpty(bdsjImportsDTO.getBdzt())) {
                        errorInfo.add(new ImportErrorInfo(bdsjImportsDTO.getRowIndex(), bdsjImportsDTO.getBdzt(), "不能为空"));
                        error = true;
                    }
                    if (!error) {
                        QueryWrapper<Tdd> one = new QueryWrapper<>();
                        one.eq("nf", nf);
                        one.eq("ksh", bdsjImportsDTO.getKsh());
                        Tdd tdd = tddService.getOne(one);
                        tdd.setBdzt(bdsjImportsDTO.getBdzt());
                        tdd.setBdztbz(bdsjImportsDTO.getBdztbz());
                        tddService.saveOrUpdate(tdd);
                    }
                }
            }

            if (!CollectionUtils.isEmpty(errorInfo)) {
                // 回写导入错误信息...
                String fileName = URLEncoder.encode("错误信息", "UTF-8");
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf8");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), ImportErrorInfo.class)
                        .excelType(ExcelTypeEnum.XLSX)
                        .sheet("sheet1")
                        .doWrite(errorInfo);
                return;
            }
            Resp.render(response, ErrorInfo.CODE_00000, ErrorInfo.CODE_MSG_00000);
        } catch (Exception e) {
            e.printStackTrace();
            Resp.render(response, ErrorInfo.CODE_00001, ErrorInfo.CODE_MSG_00001);
        }
    }

    /**
     * 下载预科生模板
     *
     * @param response
     */
    @RequestMapping("/downYksTemplate")
    public void downYksTemplate(HttpServletResponse response) {
        try {
            String fileName = URLEncoder.encode("预科生转入模板", "UTF-8");
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            EasyExcel.write(response.getOutputStream(), YksImportsDTO.class)
                    .excelType(ExcelTypeEnum.XLSX)
                    .sheet("sheet1")
                    .doWrite(new ArrayList());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 导入预科生
     *
     * @param file     文件
     * @param nf       nf
     * @param response 回答
     */
    @RequestMapping("/importYks")
    public void importYks(@RequestParam("file") MultipartFile file, String nf, HttpServletResponse response) {
        try {
            String zsnf = sysParamService.getValue(ZSXT_NF);
            nf = StringUtils.isNotBlank(nf) ? nf : zsnf;
            List<YksImportsDTO> list = EasyExcel.read(file.getInputStream(), YksImportsDTO.class, new YksImportsDTOListener()).sheet().doReadSync();
            List<ImportErrorInfo> errorInfo = new ArrayList<>();
            if (!CollectionUtils.isEmpty(list)) {
                for (YksImportsDTO yksImportsDTO : list) {
                    boolean error = false;
                    if (StringUtils.isEmpty(yksImportsDTO.getKsh())) {
                        errorInfo.add(new ImportErrorInfo(yksImportsDTO.getRowIndex(), yksImportsDTO.getKsh(), "不能为空"));
                        error = true;
                    }
                    if (StringUtils.isEmpty(yksImportsDTO.getXm())) {
                        errorInfo.add(new ImportErrorInfo(yksImportsDTO.getRowIndex(), yksImportsDTO.getXm(), "不能为空"));
                        error = true;
                    }
                    if (StringUtils.isEmpty(yksImportsDTO.getZymc())) {
                        errorInfo.add(new ImportErrorInfo(yksImportsDTO.getRowIndex(), yksImportsDTO.getZymc(), "不能为空"));
                        error = true;
                    }
                    if (!error) {
                        QueryWrapper<Tdd> one = new QueryWrapper<>();
                        one.in("nf", String.valueOf(DateUtil.thisYear() - 1), String.valueOf(DateUtil.thisYear() - 2));
                        one.eq("ksh", yksImportsDTO.getKsh());
                        one.eq("zymc", yksImportsDTO.getZymc());
                        Tdd tdd = tddService.getOne(one);
                        if (tdd != null) {
                            Tdd newTdd = new Tdd();
                            BeanUtils.copyProperties(tdd, newTdd, "id");
                            newTdd.setNf(nf);
                            newTdd.setYkszt(YKS_ZRZT_DZR);
                            tddService.saveOrUpdate(newTdd);
                        } else {
                            errorInfo.add(new ImportErrorInfo(yksImportsDTO.getRowIndex(), yksImportsDTO.getKsh(), "不存在"));
                        }
                    }
                }
            }

            if (!CollectionUtils.isEmpty(errorInfo)) {
                // 回写导入错误信息...
                String fileName = URLEncoder.encode("错误信息", "UTF-8");
                response.setContentType("application/vnd.ms-excel");
                response.setCharacterEncoding("utf8");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
                EasyExcel.write(response.getOutputStream(), ImportErrorInfo.class)
                        .excelType(ExcelTypeEnum.XLSX)
                        .sheet("sheet1")
                        .doWrite(errorInfo);
                return;
            }
            Resp.render(response, ErrorInfo.CODE_00000, ErrorInfo.CODE_MSG_00000);
        } catch (Exception e) {
            e.printStackTrace();
            Resp.render(response, ErrorInfo.CODE_00001, ErrorInfo.CODE_MSG_00001);
        }
    }

    /**
     * 预科生转入
     *
     * @param tdd tdd
     * @return {@link R}
     */
    @PostMapping("/transferInto")
    @ApiOperation(value = "预科生转入", notes = "传入tdd")
    public R transferInto(@Valid @RequestBody Tdd tdd) {
        String nf = sysParamService.getValue(ZSXT_NF);
        UpdateWrapper<Tdd> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("nf", nf);
        updateWrapper.eq("ykszt", YKS_ZRZT_DZR);
        updateWrapper.set("ykszt", YKS_ZRZT_YZR);
        updateWrapper.set("zslx", ZSLX_YKZR);
        if (StringUtils.isNotBlank(tdd.getId())) {
            updateWrapper.eq("id", tdd.getId());
        }
        return R.status(tddService.update(updateWrapper));
    }


    /**
     * 三表下载
     *
     * @param request  三表下载请求参数
     * @param httpRequest  HTTP请求对象
     * @return 任务创建结果
     */
    @PostMapping("/sbdownload")
    @ApiOperation(value = "创建三表下载任务", notes = "创建异步下载任务")
    public R<String> sbdownload(@RequestBody ThreeTableDownloadRequest request, 
                               HttpServletRequest httpRequest) {
        try {
            CurrentUser user = currentUser(httpRequest);

            String ids = request.getId();
            String nf = sysParamService.getValue(ZSXT_NF);
            String taskNf = StringUtils.isNotBlank(request.getNf()) ? request.getNf() : nf;
            
            if (request.getXzlx() == null || request.getXzlx().length == 0) {
                return R.fail("请选择下载类型");
            }
            if (StringUtils.isBlank(request.getWjcl())) {
                return R.fail("请选择文件处理方式");
            }
            String taskName = DateUtil.format(new Date(), "yyyyMMdd") + "_学生档案_三表导出";
            
            // 创建三表导出任务
            String taskId = downloadTaskHelper.createThreeTableExportTask(
                taskName,                    // 任务名称
                ids,                        // 学生ID列表
                taskNf,                     // 年份
                request.getWjcl(),          // 文件处理方式
                request.getXzlx(),          // 下载类型数组
                request.getWjfl(),          // 文件分层
                request.getFlzd(),          // 分层字段
                user.getHumancode(),        // 用户ID
                user.getHumanname(),        // 用户名称
                720                         // 过期时间：一个月(30天*24小时)
            );
            
            log.info("创建三表下载任务成功: taskId={}, userId={}, taskName={}", taskId, user.getHumancode(), taskName);
            
            return R.data(taskId, "下载任务创建成功，请到任务列表查看进度");
            
        } catch (Exception e) {
            log.error("创建三表下载任务失败", e);
            return R.fail("创建下载任务失败: " + e.getMessage());
        }
    }

    /**
     * 三表查看
     *
     * @param tdd      tdd
     * @param fileType 文件类型:zsxt_bmxx, zsxt_tjxx, zsxt_cjyzyxx
     * @return {@link String}
     * @throws Exception 例外
     */
    @RequestMapping("/sbview")
    public Map<String, String> sbview(Tdd tdd,@RequestParam("fileType") String fileType) {
        Map<String, String> map = new HashMap<>();
        String htmlStr = "";
        Tdd one = tddService.getOne(Condition.getQueryWrapper(tdd));
        if (one != null) {
            try {
                Map<String, Object> mapData = new HashMap<>();
                BeanMap beanMap = BeanMap.create(one);
                for (Object key : beanMap.keySet()) {
                    mapData.put(key.toString(), beanMap.get(key));
                }
                try {
                    GridFsResource fsResource = fileOperationUtil.getFile(one.getPhoto());
                    byte[] bytes = StreamUtils.copyToByteArray(fsResource.getInputStream());
                    mapData.put("photo", "data:text/html;base64," + Base64.encode(bytes));
                } catch (Exception e) {
                    log.info("三表查看 {} 缺失照片", one.getKsh());
                }
                switch (fileType) {
                    case "zsxt_bmxx":
                        if (StringUtils.isNotBlank(one.getBmxx())) {
                            GridFsResource bmxxGfs = fileOperationUtil.getFile(one.getBmxx());
                            if (bmxxGfs != null) {
                                byte[] bytes = StreamUtils.copyToByteArray(bmxxGfs.getInputStream());
                                map.put("data", "data:text/html;base64," + Base64.encode(bytes));
                                map.put("type", "manual");
                            }
                        } else {
                            Ksjl ksjl = new Ksjl();
                            ksjl.setNf(one.getNf());
                            ksjl.setKsh(one.getKsh());
                            List<Ksjl> ksjlList = ksjlService.list(Condition.getQueryWrapper(ksjl));
                            mapData.put("ksjlList", ksjlList);
                            htmlStr = PDFUtils.getTemplateContent(pdfExportConfig.getBmbFtl(), mapData);
                            map.put("data", htmlStr);
                            map.put("type", "automatic");
                        }
                        break;
                    case "zsxt_tjxx":
                        if (StringUtils.isNotBlank(one.getTjxx())) {
                            GridFsResource tjxxGfs = fileOperationUtil.getFile(one.getTjxx());
                            if (tjxxGfs != null) {
                                byte[] bytes = StreamUtils.copyToByteArray(tjxxGfs.getInputStream());
                                map.put("data", "data:text/html;base64," + Base64.encode(bytes));
                                map.put("type", "manual");
                            }
                        } else {
                            Tjxx tjxx = new Tjxx();
                            tjxx.setNf(one.getNf());
                            tjxx.setKsh(one.getKsh());
                            List<Tjxx> tjxxList = tjxxService.list(Condition.getQueryWrapper(tjxx));
                            if (tjxxList.size() > 0) {
                                Tjxx tjxx1 = tjxxList.get(0);
                                BeanMap tjxxMap = BeanMap.create(tjxx1);
                                for (Object key : tjxxMap.keySet()) {
                                    mapData.put(key.toString(), tjxxMap.get(key));
                                }
                            }
                            htmlStr = PDFUtils.getTemplateContent(pdfExportConfig.getTjbFtl(), mapData);
                            map.put("data", htmlStr);
                            map.put("type", "automatic");
                        }

                        break;
                    case "zsxt_cjyzyxx":
                        if (StringUtils.isNotBlank(one.getCjyzyxx())) {
                            GridFsResource cjyzyxxGfs = fileOperationUtil.getFile(one.getCjyzyxx());
                            if (cjyzyxxGfs != null) {
                                byte[] bytes = StreamUtils.copyToByteArray(cjyzyxxGfs.getInputStream());
                                map.put("data", "data:text/html;base64," + Base64.encode(bytes));
                                map.put("type", "manual");
                            }
                        } else {
                            htmlStr = PDFUtils.getTemplateContent(pdfExportConfig.getCjzybFtl(), mapData);
                            map.put("data", htmlStr);
                            map.put("type", "automatic");
                        }
                        break;
                    default:
                        break;
                }
            } catch (Exception e) {
                log.error("查看异常", e);
            }
        }
        return map;
    }

    /**
     * 清空三表
     *
     * @param tdd  tdd
     * @param xzlx 清空文件类型:zsxt_bmxx, zsxt_tjxx, zsxt_cjyzyxx
     * @return {@link R}
     */
    @RequestMapping("/sbclear")
    public R sbclear(Tdd tdd,@RequestParam("xzlx") String[] xzlx) {
        String ids = tdd.getId();
        tdd.setId(null);
        String nf = sysParamService.getValue(ZSXT_NF);
        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : nf);
//        tdd.setNf(StringUtils.isNotBlank(tdd.getNf()) ? tdd.getNf() : String.valueOf(DateUtil.thisYear()));
        QueryWrapper<Tdd> queryWrapper = Condition.getQueryWrapper(tdd);
        if (StringUtils.isNotBlank(ids)) {
            queryWrapper.in("id", ids.split(","));
        }
        List<Tdd> list = tddService.list(queryWrapper);
        if (list.size() > 0) {
            Set<String> fileNameSet = new HashSet<>();
            for (String s : xzlx) {
                for (Tdd tdd1 : list) {
                    fileNameSet.add(s + "_" + tdd1.getKsh());
                }
            }
            fileOperationUtil.removeByIdsOrFileNames(new ArrayList<>(fileNameSet));
        }
        return R.success("清空成功");
    }
}
