
package com.sanyth.web.zsxt;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.sanyth.core.common.BaseController;
import com.sanyth.core.support.Condition;
import com.sanyth.core.support.Query;
import com.sanyth.model.zsxt.Zjlxdm;
import com.sanyth.service.zsxt.IZjlxdmService;
import com.sanyth.vo.ZjlxdmVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 证件类型 控制器
 *
 * <AUTHOR> @since 2024-04-01
 */
@RestController
@AllArgsConstructor
@RequestMapping("/code/zjlxdm")
@Api(value = "", tags = "接口")
public class ZjlxdmController extends BaseController {

	private IZjlxdmService zjlxdmService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperation(value = "详情", notes = "传入zjlxdm")
	public R<Zjlxdm> detail(Zjlxdm zjlxdm) {
		Zjlxdm detail = zjlxdmService.getOne(Condition.getQueryWrapper(zjlxdm));
		return R.data(detail);
	}

	/**
	 * 分页 
	 */
	@GetMapping("/list")
	@ApiOperation(value = "分页", notes = "传入zjlxdm")
	public R<IPage<Zjlxdm>> list(Zjlxdm zjlxdm, Query query) {
		IPage<Zjlxdm> pages = zjlxdmService.page(Condition.getPage(query), Condition.getQueryWrapper(zjlxdm));
		return R.data(pages);
	}

	/**
	 * 全部 
	 */
	@GetMapping("/all")
	@ApiOperation(value = "全部", notes = "传入zjlxdm")
	public R<List<Zjlxdm>> list(Zjlxdm zjlxdm) {
		List<Zjlxdm> pages = zjlxdmService.list(Condition.getQueryWrapper(zjlxdm));
		return R.data(pages);
	}
	/**
	 * 自定义分页 
	 */
	@GetMapping("/page")
	@ApiOperation(value = "分页", notes = "传入zjlxdm")
	public R<IPage<ZjlxdmVO>> page(ZjlxdmVO zjlxdm, Query query) {
		IPage<ZjlxdmVO> pages = zjlxdmService.selectZjlxdmPage(Condition.getPage(query), zjlxdm);
		return R.data(pages);
	}

	/**
	 * 新增 
	 */
	@PostMapping("/save")
	@ApiOperation(value = "新增", notes = "传入zjlxdm")
	public R save(@Valid @RequestBody Zjlxdm zjlxdm) {
		return R.status(zjlxdmService.save(zjlxdm));
	}

	/**
	 * 修改 
	 */
	@PostMapping("/update")
	@ApiOperation(value = "修改", notes = "传入zjlxdm")
	public R update(@Valid @RequestBody Zjlxdm zjlxdm) {
		return R.status(zjlxdmService.updateById(zjlxdm));
	}

	/**
	 * 新增或修改 
	 */
	@PostMapping("/submit")
	@ApiOperation(value = "新增或修改", notes = "传入zjlxdm")
	public R submit(@Valid @RequestBody Zjlxdm zjlxdm) {
		return R.status(zjlxdmService.saveOrUpdate(zjlxdm));
	}

	
	/**
	 * 删除 
	 */
	@PostMapping("/remove")
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		return R.status(zjlxdmService.removeByIds(Func.toLongList(ids)));
	}

	
}
