spring:
  application:
    name: zsxt
  profiles:
#    active: @active.properties@
    active: dev
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 2048MB
  #数据源连接池配置
  druid:
    initial-size: 10 # 初始化时建立物理连接的个数。初始化发生在显示调用init方法，或者第一次getConnection时
    min-idle: 5 # 最小连接池数量
    maxActive: 200 # 最大连接池数量
#    maxWait: 3000 # 获取连接时最大等待时间，单位毫秒。配置了maxWait之后，缺省启用公平锁，并发效率会有所下降，如果需要可以通过配置
    timeBetweenEvictionRunsMillis: 1000 # 关闭空闲连接的检测时间间隔.Destroy线程会检测连接的间隔时间，如果连接空闲时间大于等于minEvictableIdleTimeMillis则关闭物理连接。单位是毫秒
    minEvictableIdleTimeMillis: 300000 # 连接的最小生存时间.连接保持空闲而不被驱逐的最小时间 单位是毫秒
    testWhileIdle: true # 申请连接时检测空闲时间，根据空闲时间再检测连接是否有效.建议配置为true，不影响性能，并且保证安全性。申请连接的时候检测，如果空闲时间大于timeBetweenEvictionRun
    poolPreparedStatements: true # 开启PSCache
    maxPoolPreparedStatementPerConnectionSize: 20 #设置PSCache值
    connectionErrorRetryAttempts: 3 # 连接出错后再尝试连接三次
    breakAfterAcquireFailure: true # 数据库服务宕机自动重连机制
    timeBetweenConnectErrorMillis: 300000 # 连接出错后重试时间间隔
    removeAbandonedTimeout: 30 #超过30秒的空闲连接就可以被关闭了,单位是秒
    removeAbandoned: true #对于长时间不使用的连接强制关闭
mybatis-plus:
  mapperLocations: classpath:/mybatis/mapper/*.xml
  type-aliases-package: com.sanyth.model.*,com.sanyth.**.domain
  global-config:
    db-config:
      where-strategy: not_empty
#      update-strategy: not_empty
    id-type: 0
    field-strategy: 2
    db-column-underline: true
    refresh-mapper: true
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
#    自定义日志类控制按级别打印,但是BSL等插件将看不到真实sql语句，开发时根据具体情况切换
#    log-impl: com.sanyth.report.core.log.CustomSl4jImpl
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


# 忽略url
ignored:
  # 无需登录认证的请求
  urls:
    - /
    - /index
    - /manage/**
    - /css/**
    - /js/**
    - /img/**
    - /fonts/**
    - /cdn/**
    - /svg/**
    - /util/**
    - /static/**
    - /captcha*
    - /favicon.ico
    - /oauth/**
    - /user/**
    - /findPassword*
    - /isLogin*
    - /redirectPage*
    - /file/view/**
    - /file/download/**
    - /serviceValidate*
    - /noAuth/**
    - /magic/web/**
#    - /report/dashboard/get
#    - /report/dashboard/getData
#    - /report/manage/list/nologin
  # 无需检查权限的请求, 但需登录
  limitUrls:
    - /toClient*
    - /magic/**
    - /data/api/**

ldap:
  switch: false

syt:
  sm4:
    telmobile:
      key: 019d5858d0d0bf0d

system:
  config:
    allow-upload-ext: xls,xlsx,bmp,jpeg,jpg,png,gif,bmp,pdf,doc,docx,mp4,avi,mp3,ftl,exe,zip

rpamis:
  # rpamis-security配置
  security:
    # 是否开启安全组件，落库加密，出库脱密，如果不指定加密算法，则默认返回原值
    # 当此开关为false时，无论脱敏切面是否开启，均不生效
    enable: true
    # 加密算法类型，内置sm4，可自行扩展
    algorithm: sm4
    # 加密算法密钥，需要自己生成，满足16位即可，下面只是样例
    sm4key: qVEmJtPAuVMrVJ4Q
    # 忽略解密失败，如果解密失败则返回原值，否则抛出异常，如果不填写默认true
    ignore-decrypt-failed: true
    # 是否开启脱敏切面
    desensitization-enable: true
    # 自定义切点，比如增加RestController切点
#    custom-pointcut: @within(org.springframework.web.bind.annotation.RestController)

## pdf export config
pdfExport:
  bmbFtl: freemarker/bmxx.ftl
  cjzybFtl: freemarker/cjzyb.ftl
  tjbFtl: freemarker/tjb.ftl


magic-api:
  web: /magic/web
  editor-config: classpath:./magic-editor-config.js
  resource:
    type: database              # 配置接口存储方式，这里选择存在数据库中
    table-name: magic_api_file  # 数据库中的表名
    prefix: /magic-api          # 前缀
  auto-import-module: db,log     # 自动导入的模块
  auto-import-package: cn.hutool.core.*   # 自动导包
  sql-column-case: camel        #启用驼峰命名转换
  secret-key: sanyth123!@# # 远程推送时的秘钥，未配置则不开启推送
  push-path: /magic/web/_sanyth-api-sync #远程推送的路径，默认为/_magic-api-sync
  show-sql: true #配置打印SQL
  #  throw-exception: true         # 执行出错时，异常将抛出处理
  response-code:
    success: 200                #执行成功的code值
    invalid: 400                #参数验证未通过的code值
    exception: 500              #执行出现异常的code值
  #  response: |- #配置JSON格式，格式为magic-script中的表达式
  #    {
  #      code: code,
  #      message: message,
  #      data
  #    }
  #  crud: # CRUD相关配置
  #    logic-delete-column: deleted #逻辑删除列
  #    logic-delete-value: 2       #逻辑删除值
  page:
    size: pageSize              # 页大小的请求参数名称
    page: page                  # 页码的请求参数名称
    default-page: 1             # 未传页码时的默认首页
    default-size: 10            # 未传页大小时的默认页大小
  backup: #备份相关配置
    enable: false #是否启用
    max-history: -1 #备份保留天数，-1为永久保留
    #    datasource: magic  #指定数据源（单数据源时无需配置，多数据源时默认使用主数据源，如果存在其他数据源中需要指定。）
    table-name: magic_api_backup #使用数据库存储备份时的表名
  security: # 安全配置
    username: admin # 登录用的用户名
    password: sanyth123456 # 登录用的密码

#tomcat优化及解决http慢攻击
server:
  tomcat:
    connection-timeout: 20000
    threads:
      max: 2000