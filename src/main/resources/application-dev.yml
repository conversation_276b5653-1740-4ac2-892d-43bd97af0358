server:
  port: 9094
  servlet:
    context-path: /
  tomcat:
    basedir: tmp
spring:
  datasource:
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: ********************************************
    username: syt_new_zsxt
    password: syt_new_zsxt_2025
#    username: syt_new_zsxt_wuhan
#    password: syt_new_zsxt_wuhan
#    url: *****************************************
#    username: syt_zsxt
#    password: Sanyth2025_zsxt
#    username: syt_zsxt_test
#    password: Sanyth2025_zsxt_testƒ
#    url: *********************************************
#    username: syt_zsxt
#    password: Sanyth_0717#WHhy
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 60000
      pool-name: DatebookHikariCP
      max-lifetime: 60000
      connection-timeout: 60000
      connection-test-query: SELECT * FROM DUAL
      validation-timeout: 3000
      login-timeout: 5
  thymeleaf:
    enabled: true
    check-template-location: false
    prefix: classpath:/templates/
    cache: false
    mode: HTML5
    encoding: utf-8
    suffix: .html
    check-template: false
    template-resolver-order: 1
  freemarker:
    enabled: true
    template-loader-path: classpath:/templates/
    cache: false
    template_update_delay: 0
    view-names: freemarker/*
    content-type: text/html
    charset: UTF-8
    suffix: .ftl
  session:
    store-type: redis
  redis:
    host: *************
    port: 6380
    password: 123456
#    host: 127.0.0.1
#    port: 6379
#    password: Sanyth123456t
    database: 5
  data:
    mongodb:
      uri: ***************************************************************
#      uri: mongodb://zsxt:SytWhxy2024_zsxt#@**************:27017/zsxt
#      uri: mongodb://admin:SanythWHzs!0718#@**************:28088/admin
  resources:
    static-locations: classpath:/static
  mvc:
    static-path-pattern: /**

sso:
  clientId: qK6193J6cQF00wa4
  clientSecret: 714Z9JdY99V22MFMhqEh19y7401St5T3
  accessTokenUri: http://**************:8282/oauth/token
  userAuthorizationUri: http://**************:8282/oauth/authorize
  userInfoUri: http://**************:8282/user/me
  logoutUri: http://**************:8282/logout?service=
  serviceValidateUri: http://**************:8282/serviceValidate

log:
  level: info
  path: logs/
  maxHistory: 1

logging:
  config: classpath:logback-spring.xml
  file:
    path: /log #这里的路径会以logback.xml为主
    name: zsxt.log  #这里的文件名会以logback.xml为主
  level:
    com.sanyth.mapper.zsxt: debug
    com.sanyth.mapper.system: debug
    com.sanyth.mapper.datawarning: debug
    com.sanyth.mapper.report: debug
    com.sanyth.mapper.jkb: debug
    com.sanyth.mapper.ksgl: debug
    com.sanyth.develop.mapper: debug
    com.sanyth.platform.qywx.mapper: debug

sanyth:
  license:
    enabled: false
