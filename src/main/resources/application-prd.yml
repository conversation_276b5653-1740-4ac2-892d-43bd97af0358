server:
  port: 9094
  servlet:
    context-path: /
spring:
  datasource:
    driver-class-name: oracle.jdbc.driver.OracleDriver
    url: *****************************************
    username: syt_new_zsxt
    password: syt_new_zsxt
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 60000
      pool-name: DatebookHikariCP
      max-lifetime: 60000
      connection-timeout: 60000
      connection-test-query: SELECT * FROM DUAL
      validation-timeout: 3000
      login-timeout: 5
  thymeleaf:
    enabled: true
    check-template-location: false
    prefix: classpath:/web/templates/
    cache: false
    mode: HTML5
    encoding: utf-8
    suffix: .html
    check-template: false
    template-resolver-order: 1
  freemarker:
    enabled: true
    template-loader-path: classpath:/web/templates/
    cache: false
    view-names: freemarker/*
    content-type: text/html
    charset: UTF-8
    suffix: .ftl
  session:
    store-type: redis
  redis:
    host: **************
    port: 16379
    password: sanyth123456
    database: 7
  data:
    mongodb:
      uri: *********************************************
  resources:
    static-locations: classpath:/web/static/
  mvc:
    static-path-pattern: /**

sso:
  clientId: qK6193J6cQF00wa4
  clientSecret: 714Z9JdY99V22MFMhqEh19y7401St5T3
  accessTokenUri: http://**************:8282/oauth/token
  userAuthorizationUri: http://**************:8282/oauth/authorize
  userInfoUri: http://**************:8282/user/me
  logoutUri: http://**************:8282/logout?service=
  serviceValidateUri: http://**************:8282/serviceValidate

log:
  level: error
  path: logs/
  maxHistory: 1

