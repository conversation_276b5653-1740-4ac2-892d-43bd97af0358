<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<html>
	<head>
		<meta charset="utf-8">
		<title>认证跳转...</title>
		<link rel="stylesheet" type="text/css" href="manage/css/reset.css" />
		<link rel="stylesheet" type="text/css" href="manage/css/findPassword.css" />
		<script src="manage/js/jquery-3.4.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="manage/js/security.js"></script>
		<script type="text/javascript" src="sec_js"></script>
		<script type="text/javascript" src="manage/js/login.js"></script>
		<script th:inline="javascript">
			var token = [[${token}]];
			var url = [[${url}]];
			$.post('/tokenLogin', {token:token,url:url}, function (data) {
				if (data.code === '00000') {
					// window.location.href = window.location.origin;
					if (url != null) {
						setCookie('redirectPage', url, 1);
					}
				} else {
					alert(data.info);
				}
				window.location.href = window.location.origin;
			});
		</script>
	</head>
	<body>

	</body>
</html>
