<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<html>
	<head>
		<meta charset="utf-8">
		<title th:utext="${systemName}">找回密码</title>
		<link rel="stylesheet" type="text/css" href="manage/css/reset.css" />
		<link rel="stylesheet" type="text/css" href="manage/css/findPassword.css" />
		<script src="manage/js/jquery-3.4.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script type="text/javascript" src="/manage/js/jsrsasign-all-min.js"></script>
		<script type="text/javascript" src="/manage/js/sign.js"></script>
	</head>
	<body>
		<div class="wrap">
			<div class="entry-header">
				<img class="logo-img" th:src="${loginLogo}">
				<a href="javascript:;;" onclick="window.history.back(-1);"><span class="loginHelp">返回登录页</span></a>
				<span class="titleName" th:utext="${systemName}">数据分析平台</span>
			</div>
			<div class="entry-main">

				<div class="registerWrap">
					<p class="title"><img src="manage/img/qyzc.png"></p>
					<div class="stepWrap clearfix">
<!--						<div class="step step1 stepClick">-->
<!--							<div>Step 1</div>-->
<!--							<p>账号信息</p>-->
<!--						</div>-->
						<div class="step step2 stepClick">
							<div>Step 1</div>
							<p>找回方式</p>
						</div>
						<div class="step step3">
							<div>Step 2</div>
							<p>重置密码</p>
						</div>
						<div class="step step4">
							<div>Step 3</div>
							<p>完成</p>
						</div>
					</div>
					<div class="stepContent">
<!--						<div class="stepItem stepItem1" style="display: block;">-->
<!--							<form>-->
<!--								<div class="one clearfix">-->
<!--									<span class="oneSpan1">账号：</span>-->
<!--									<input type="text" placeholder="请输入账号">-->
<!--								</div>-->
<!--								<div class="one clearfix">-->
<!--									<span class="oneSpan1">姓名：</span>-->
<!--									<input type="text" placeholder="请输入您的姓名">-->
<!--									<span class="oneSpan2"></span>-->
<!--								</div>-->
<!--								<button class="next next1">下一步</button>-->
<!--							</form>-->
<!--						</div>-->
						<div class="stepItem stepItem2" style="display: block;position: relative">
							<div class="one clearfix" style="position: absolute;top: 50px;left: 0px;">
								<span class="oneSpan1">找回方式：</span>
								<label class="radio radioPhone"><input name="Fruit" type="radio" value="" checked/>手机短信找回</label>
								<label class="radio radioEmail"><input name="Fruit" type="radio"value="" />邮箱找回 </label>
							</div>
							<div>
								<form class="emailForm" style="display: none;">
									<div class="one clearfix">
										<span class="oneSpan1">用户账号：</span>
										<input id="humancode" type="text" placeholder="请输入学号或工号">
                                        <span class="oneSpan2"><button type=button class="yzm">获取验证码</button></span>
<!--										<span class="oneSpan2 red">请输入账号</span>-->
									</div>
									<div class="one" style="margin-bottom: 0;">

									</div>
									<div class="one clearfix">
<!--										<span class="oneSpan1">邮箱：</span>-->
<!--										<input id="email" disabled type="text" placeholder="请输入邮箱账号">-->
<!--										<span class="oneSpan2"><button type=button class="yzm">获取验证码</button></span>-->
                                        <p id="redTip" style="text-align:center;color:red;font-size:12px;display: none;">查询不到账号信息，请查证后重新输入</p>
                                        <p id="greyTip" style="text-align:center;color:#999;font-size:12px;display: none;">验证码已发送到12******************的邮箱</p>
									</div>

									<div class="buttonWrap">
<!--										<button class="next prev2">上一步</button>-->
										<button class="next next2">下一步</button>
									</div>

								</form>
								<form class="phoneFrom" >
									<div class="one clearfix">
										<span class="oneSpan1">用户账号：</span>
										<input id="humancode2" type="text" placeholder="请输入学号或工号">
                                        <span class="oneSpan2"><button type=button class="yzm">获取验证码</button></span>
									</div>
									<div class="one" style="margin-bottom: 0;">

									</div>
									<div class="one clearfix">
                                        <p id="redTip2" style="text-align:center;color:red;font-size:12px;display: none;">查询不到账号信息，请查证后重新输入</p>
                                        <p id="greyTip2" style="text-align:center;color:#999;font-size:12px;display: none;">验证码已发送到12******************的手机号中</p>
									</div>

									<div class="buttonWrap">
<!--										<button class="next prev2">上一步</button>-->
										<button class="next next2">下一步</button>
									</div>
								</form>
							</div>
						</div>
						<div class="stepItem stepItem3" style="display: none;">
							<form>
								<div class="one clearfix">
									<span class="oneSpan1">验证码：</span>
									<input id="verifyCode" type="text" placeholder="请输入您收到的验证码">
									<span class="oneSpan2 red">您输入的验证码不正确，请返回上一步重新获取</span>
								</div>
								<div class="one clearfix">
									<span class="oneSpan1">新密码：</span>
									<input id="newpassword" type="password" placeholder="请输入新密码,请使用10到16位数字加大小写字母">
									<span class="oneSpan2 red"></span>
								</div>
								<div class="one clearfix">
									<span class="oneSpan1">确认密码：</span>
									<input id="newpasswords" type="password" placeholder="请再输入一次" >
									<span class="oneSpan2 red">您输入两次密码不一致，请重新输入</span>
								</div>
								<div class="buttonWrap">
									<button class="next prev3">上一步</button>
									<button class="next next3">提交</button>
								</div>
							</form>
						</div>
						<div class="stepItem stepItem4" style="display: none;">
							<img src="manage/img/dui.png" alt="">
							<p>恭喜您找回密码成功</p>
							<button class="next next4">跳转至登录页</button>
						</div>

					</div>
				</div>



				<div class="entry-footer">
					<p th:text="${dbxx}">Copyright©2021 北京三易拓科技有限公司 版权所有</p>
				</div>
			</div>
		</div>
	</body>
</html>
<script type="text/javascript">
	var pathName = window.location.pathname.substring(1);
	var webName = pathName == '' ? '' : pathName.substring(0, pathName.indexOf('/'));
	var contextPath = window.location.protocol + '//' + window.location.host + '/' + webName;
	var verifyType = "phone"; // 验证方式
	var humancode = "";
	$(function() {
		$("#humancode2").focus();
		// 邮箱手机号切换
		$(".radioEmail").on("click", function() {
			$(".emailForm").fadeIn(1).siblings().fadeOut(0);
			verifyType = "email";
			$("#humancode").focus();
			if(humancode!==""){
				$("#humancode").val(humancode);
			}
			$("#redTip").hide();
			$("#redTip2").hide();
			$("#greyTip").hide();
			$("#greyTip2").hide();
		})
		$(".radioPhone").on("click", function() {
			$(".phoneFrom").fadeIn(1).siblings().fadeOut(0);
			verifyType = "phone";
			$("#humancode2").focus();
			if(humancode!==""){
				$("#humancode2").val(humancode);
			}
			$("#redTip").hide();
			$("#redTip2").hide();
			$("#greyTip").hide();
			$("#greyTip2").hide();
		})

		// // 60s倒计时
		$('.yzm').on('click', function() {
			var that = $(this);
			if(verifyType=="email"){
				humancode = $("#humancode").val();
			}else if(verifyType == "phone"){
				humancode = $("#humancode2").val();
			}
			$("#redTip").hide();
			$("#redTip2").hide();
			$("#greyTip").hide();
			$("#greyTip2").hide();
			if(humancode==""){
				if(verifyType=="email"){
					$("#redTip").text('请先输入学号或工号');
					$("#redTip").show();
					$("#humancode").focus();
				}else if(verifyType == "phone"){
					$("#redTip2").text('请先输入学号或工号');
					$("#redTip2").show();
					$("#humancode2").focus();
				}
				return;
			}

            var timeo = 60;
            var timeStop = setInterval(function() {
                timeo--;
                if (timeo > 0) {
                    that.text('重新发送' + timeo + 's');
                    that.attr('disabled', 'disabled').css("background","#ccc"); //禁止点击
                } else {
                    timeo = 60; //当减到0时赋值为60
                    that.text('获取验证码');
                    clearInterval(timeStop); //清除定时器
                    that.removeAttr('disabled').css("background","#1F93E0"); //移除属性，可点击
                }
            }, 1000);
			// 发送验证码
			var url = contextPath + "user/sendVerifyCode";
			var searchData = {"humancode": humancode, "verifyType":verifyType};
			$.ajax(url, {
				method: 'POST',
				dataType: 'json',
				contentType: 'application/json; charset=UTF-8',
				data: JSON.stringify(searchData),
				beforeSend: function (XMLHttpRequest) {
					XMLHttpRequest.setRequestHeader("sign", RSA_SIGN(JSON.stringify(searchData)));
				},
				success: function (res) {
					if(res.code === '00000'){
						if(verifyType=="email"){
							var tmp = res.info.email;
							var index = tmp.indexOf("@");
							var textShow = "验证码已发送至"
									+tmp.substring(0, index - 3)
									+"******"
									+tmp.substring(index, tmp.length)+"邮箱中，请注意查收";
							$("#greyTip").text(textShow);
							$("#greyTip").show();
						}else if(verifyType == "phone"){
							var tmp = res.info.phone;
							var textShow = "验证码已发送至"
									+tmp.substring(0, 3)
									+"****"
									+tmp.substring(7, 11)+"手机中，请注意查收";
							$("#greyTip2").text(textShow);
							$("#greyTip2").show();
						}
					}else{
						if(verifyType=="email"){
							$("#redTip").text(res.info);
							$("#redTip").show();
						}else if(verifyType == "phone"){
							$("#redTip2").text(res.info);
							$("#redTip2").show();
						}
					}
				}
			});
		})


		// $(".next1").on("click", function(e) {
		// 	if (e.preventDefault) {
		// 		e.preventDefault();
		// 	} else {
		// 		window.event.returnValue == false;
		// 	}
		// 	$(".step2").addClass("stepClick").siblings().removeClass("stepClick")
		// 	$(".stepItem2").fadeIn(1).siblings().fadeOut(0);
		// })
		// 邮箱验证
		$("#humancode").on("blur", function(e) {
			humancode = $("#humancode").val();
			// getEmailAndPhoneByHumancode();
		});
		// 手机验证
		$("#humancode2").on("blur", function(e) {
			humancode = $("#humancode2").val();
			// getEmailAndPhoneByHumancode();
		});
		// 下一步
		$(".next2").on("click", function(e) {
			if (e.preventDefault) {
				e.preventDefault();
			} else {
				window.event.returnValue == false;
			}
			if(humancode==""){
				if(verifyType=="email"){
					$("#redTip").text('请先输入学号或工号');
					$("#redTip").show();
					$("#humancode").focus();
				}else if(verifyType == "phone"){
					$("#redTip2").text('请先输入学号或工号');
					$("#redTip2").show();
					$("#humancode2").focus();
				}
				return;
			}
			$(".step3").addClass("stepClick").siblings().removeClass("stepClick");
			$(".stepItem3").fadeIn(1).siblings().fadeOut(0)
			$("#newpassword").on("blur", function(e) {
				var newpassword = $("#newpassword").val();
				changePass(newpassword);
			});
			$("#newpasswords").on("blur", function(e) {
				changeRepassword()
			});
		})
		// $(".prev2").on("click", function(e) {
		// 	if (e.preventDefault) {
		// 		e.preventDefault();
		// 	} else {
		// 		window.event.returnValue == false;
		// 	}
		// 	$(".step1").addClass("stepClick").siblings().removeClass("stepClick")
		// 	$(".stepItem1").fadeIn(1).siblings().fadeOut(0);
		// })
		$(".next3").on("click", function(e) {
			if (e.preventDefault) {
				e.preventDefault();
			} else {
				window.event.returnValue == false;
			}
			// 重置密码
			var verifyCode = $("#verifyCode").val();
			var newpassword = $("#newpassword").val();
			var newpasswords = $("#newpasswords").val();
			if (verifyCode === '') {
				alert('请输入验证码');
				$("#verifyCode").focus();
				return;
			}
			if (newpassword === '') {
				alert('请输入新密码');
				$("#newpassword").focus();
				return;
			}
			if (newpasswords === '') {
				alert('请输入确认密码');
				$("#newpasswords").focus();
				return;
			}
			/*if (newpasswords !== newpassword) {
				alert('两次密码不一致');
				$("#newpassword").focus();
				return;
			}*/

			if (!changeRepassword()&&!checkStrength(newpassword)&&!checkStrength(newpasswords)) {
				return false;
			}
			var url = contextPath + "user/resetPasswdByCode";
			var searchData = {
				"humancode": humancode,
				"verifyType":verifyType,
				"verifyCode":verifyCode,
				"newpassword":newpassword,
				"newpasswords":newpasswords
			};
			// console.log(searchData);
			$.ajax(url, {
				method: 'POST',
				dataType: 'json',
				contentType: 'application/json; charset=UTF-8',
				data: JSON.stringify(searchData),
				beforeSend: function (XMLHttpRequest) {
					XMLHttpRequest.setRequestHeader("sign", RSA_SIGN(JSON.stringify(searchData)));
				},
				success: function (res) {
					if(res.code === '00000'){
						// 操作成功
						$(".step4").addClass("stepClick").siblings().removeClass("stepClick");
						$(".stepItem4").fadeIn(1).siblings().fadeOut(0);
					}else{
						alert(res.info);
					}
				}
			});
		})
		$(".prev3").on("click", function(e) {
			if (e.preventDefault) {
				e.preventDefault();
			} else {
				window.event.returnValue == false;
			}
			$(".step2").addClass("stepClick").siblings().removeClass("stepClick")
			$(".stepItem2").fadeIn(1).siblings().fadeOut(0);
		})
		$(".next4").on("click", function(e) {
			if (e.preventDefault) {
				e.preventDefault();
			} else {
				window.event.returnValue == false;
			}
			window.location = "/";
		})
	})
	function getEmailAndPhoneByHumancode(){
		if(humancode!=="") {
			var url = contextPath + "user/getEmailAndPhoneByHumancode";
			var searchData = {"humancode": humancode};
			$.ajax(url, {
				method: 'POST',
				dataType: 'json',
				contentType: 'application/json; charset=UTF-8',
				data: JSON.stringify(searchData),
				beforeSend: function (XMLHttpRequest) {
					XMLHttpRequest.setRequestHeader("sign", RSA_SIGN(JSON.stringify(searchData)));
				},
				success: function (res) {
					if(res.code === '00000'){
						// 查询成功
						if(verifyType=="email"){
							$("#email").val(res.info.email);
						}else if(verifyType == "phone"){
							$("#phone").val(res.info.phone);
						}
					}else{
						alert(res.info);
						if(verifyType=="email"){
							$("#email").val("");
						}else if(verifyType == "phone"){
							$("#phone").val("");
						}
					}
				}
			});
		}
	}

	function changePass(value) {
		if (!checkStrength(value)) {
			alert("当前密码过于简单,请使用10到16位数字加大小写字母");
			$("#newpassword").val('');
			$("#newpasswords").val('');
			$("#newpassword").focus();
			return false;
		}
		return true;
	}
	function checkStrength(value){
		var reg = new RegExp(/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{10,16}$/);
		if (!reg.test(value)) {
			return false;
		}
		return true;
	}

	function changeRepassword() {
		var newpassword = $("#newpassword").val();
		var newpasswords = $("#newpasswords").val();
		if (newpasswords !== newpassword) {
			alert("两次密码输入不一致，请重新输入");
			return false;
		}
		return true;
	}
</script>
