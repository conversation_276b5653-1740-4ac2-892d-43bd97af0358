INSERT INTO SYS_RESOURCE(ID, PARENT_ID,CODE,LABEL, PATH,ICON,IS_BTN,  PARAM,  STATUS, DESCRIPTION, URL, COMPONENT, TYPE, SORT, NAME, REDIRECT, TITLE, EVENT,  ACTION, IS_OPEN)
VALUES ('$!{menuId}', 1752610026146512898, '$!{entityKey}', '$!{codeName}', '$!{entityKey}', NULL, '否', NULL,'是',NULL, NULL, 'views/$!{servicePackage}/$!{entityKey}',0, 100, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO SYS_RESOURCE(ID, PARENT_ID,CODE,LABEL, PATH,ICON,IS_BTN,  PARAM,  STATUS, DESCRIPTION, URL, COMPONENT, TYPE, SORT, NAME, REDIRECT, TITLE, EVENT,  ACTION, <PERSON>_<PERSON>PEN)
VALUES ('$!{addMenuId}', '$!{menuId}', '$!{entityKey}_add', '新增', '/$!{entityKey}/add', NULL, '是', NULL,'是',NULL, NULL, NULL,0, 1, NULL, NULL, NULL, NULL, 1, NULL);
INSERT INTO SYS_RESOURCE(ID, PARENT_ID,CODE,LABEL, PATH,ICON,IS_BTN,  PARAM,  STATUS, DESCRIPTION, URL, COMPONENT, TYPE, SORT, NAME, REDIRECT, TITLE, EVENT,  ACTION, IS_OPEN)
VALUES ('$!{editMenuId}', '$!{menuId}', '$!{entityKey}_edit', '修改', '/$!{entityKey}/edit', NULL, '是', NULL,'是',NULL, NULL, NULL,0, 2, NULL, NULL, NULL, NULL, 1, NULL);
INSERT INTO SYS_RESOURCE(ID, PARENT_ID,CODE,LABEL, PATH,ICON,IS_BTN,  PARAM,  STATUS, DESCRIPTION, URL, COMPONENT, TYPE, SORT, NAME, REDIRECT, TITLE, EVENT,  ACTION, IS_OPEN)
VALUES ('$!{removeMenuId}', '$!{menuId}', '$!{entityKey}_delete', '删除', '/$!{entityKey}/remove', NULL, '是', NULL,'是',NULL, NULL, NULL,0, 3, NULL, NULL, NULL, NULL, 1, NULL);
INSERT INTO SYS_RESOURCE(ID, PARENT_ID,CODE,LABEL, PATH,ICON,IS_BTN,  PARAM,  STATUS, DESCRIPTION, URL, COMPONENT, TYPE, SORT, NAME, REDIRECT, TITLE, EVENT,  ACTION, IS_OPEN)
VALUES ('$!{viewMenuId}', '$!{menuId}', '$!{entityKey}_view', '查看', '/$!{entityKey}/view', NULL, '是', NULL,'是',NULL, NULL, NULL,0, 4, NULL, NULL, NULL, NULL, 1, NULL);
