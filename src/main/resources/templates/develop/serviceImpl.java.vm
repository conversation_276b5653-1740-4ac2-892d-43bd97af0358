
package $!{package.ServiceImpl};

import $!{package.Entity}.$!{entity};
#set($voPackage=$package.Entity.replace("model","vo"))
import $!{voPackage}.$!{entity}VO;
import $!{package.Mapper}.$!{table.mapperName};
import $!{package.Service}.$!{table.serviceName};
import $!{superServiceImplClassPackage};
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * $!{table.comment} 服务实现类
 *
 * <AUTHOR>
 * @since $!{date}
 */
@Service
#if($!{kotlin})
open class $!{table.serviceImplName} : $!{superServiceImplClass}<$!{table.mapperName}, $!{entity}>(), $!{table.serviceName} {

}
#else
public class $!{table.serviceImplName} extends $!{superServiceImplClass}<$!{table.mapperName}, $!{entity}> implements $!{table.serviceName} {

	@Override
	public IPage<$!{entity}VO> select$!{entity}Page(IPage<$!{entity}VO> page, $!{entity}VO $!{table.entityPath}) {
		return page.setRecords(baseMapper.select$!{entity}Page(page, $!{table.entityPath}));
	}

}
#end
