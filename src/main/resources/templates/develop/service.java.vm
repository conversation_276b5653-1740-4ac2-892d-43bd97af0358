
package $!{package.Service};

import $!{package.Entity}.$!{entity};
#set($voPackage=$package.Entity.replace("model","vo"))
import $!{voPackage}.$!{entity}VO;
import $!{superServiceClassPackage};
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * $!{table.comment} 服务类
 *
 * <AUTHOR>
 * @since $!{date}
 */
#if($!{kotlin})
interface $!{table.serviceName} : $!{superServiceClass}<$!{entity}>
#else
public interface $!{table.serviceName} extends $!{superServiceClass}<$!{entity}> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param $!{table.entityPath}
	 * @return
	 */
	IPage<$!{entity}VO> select$!{entity}Page(IPage<$!{entity}VO> page, $!{entity}VO $!{table.entityPath});

}
#end
