
#set($voPackage=$package.Entity.replace("model","vo"))
package $!{voPackage};

import $!{package.Entity}.$!{entity};
#if($!{entityLombokModel})
import lombok.Data;
import lombok.EqualsAndHashCode;
#end
#if($!{swagger})
import io.swagger.annotations.ApiModel;
#end

/**
 * $!{table.comment}视图实体类
 *
 * <AUTHOR>
 * @since $!{date}
 */
#if($!{entityLombokModel})
@Data
@EqualsAndHashCode(callSuper = true)
#end
#if($!{swagger})
@ApiModel(value = "$!{entity}VO对象", description = #if ("$!{table.comment}"=="")"$!{entity}VO对象"#else"$!{table.comment}"#end)
#end
public class $!{entity}VO extends $!{entity} {
	private static final long serialVersionUID = 1L;

}
