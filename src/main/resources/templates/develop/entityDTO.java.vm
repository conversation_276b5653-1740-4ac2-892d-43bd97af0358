
#set($dtoPackage=$package.Entity.replace("model","dto"))
package $!{dtoPackage};

import com.alibaba.excel.annotation.ExcelIgnore;
import $!{package.Entity}.$!{entity};
#if($!{entityLombokModel})
import lombok.Data;
import lombok.EqualsAndHashCode;
#end

/**
 * $!{table.comment}数据传输对象实体类
 *
 * <AUTHOR>
 * @since $!{date}
 */
#if($!{entityLombokModel})
@Data
@EqualsAndHashCode(callSuper = true)
#end
public class $!{entity}DTO extends $!{entity} {
	private static final long serialVersionUID = 1L;
	@ExcelIgnore
	private Integer rowIndex;
}
