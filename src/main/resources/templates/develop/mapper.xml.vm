<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="$!{package.Mapper}.$!{table.mapperName}">
#set($voPackage=$package.Entity.replace("model","vo"))
#if($!{enableCache})
    <!-- 开启二级缓存 -->
    <cache type="org.mybatis.caches.ehcache.LoggingEhcache"/>

#end
#if($!{baseResultMap})
    <!-- 通用查询映射结果 -->
    <resultMap id="$!{table.entityPath}ResultMap" type="$!{voPackage}.$!{entity}VO">
#foreach($field in $!{table.fields})
#if($!{field.keyFlag})##生成主键排在第一位
        <id column="$!{field.name}" property="$!{field.propertyName}"/>
#end
#end
#foreach($field in $!{table.commonFields})##生成公共字段
        <result column="$!{field.name}" property="$!{field.propertyName}"/>
#end
#foreach($field in $!{table.fields})
#if(!$!{field.keyFlag} && $!{field.name} != $!{tenantColumn})##生成普通字段
        <result column="$!{field.name}" property="$!{field.propertyName}"/>
#end
#end
    </resultMap>

#end

    <select id="select$!{entity}Page" resultMap="$!{table.entityPath}ResultMap">
        select * from $!{table.name} where is_deleted = 0
    </select>

</mapper>
