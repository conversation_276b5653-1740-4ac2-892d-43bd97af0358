
package $!{package.Mapper};

import $!{package.Entity}.$!{entity};
#set($voPackage=$package.Entity.replace("model","vo"))
import $!{voPackage}.$!{entity}VO;
import $!{superMapperClassPackage};
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * $!{table.comment} Mapper 接口
 *
 * <AUTHOR>
 * @since $!{date}
 */
#if($!{kotlin})
interface $!{table.mapperName} : $!{superMapperClass}<$!{entity}>
#else
@Mapper
public interface $!{table.mapperName} extends $!{superMapperClass}<$!{entity}> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param $!{table.entityPath}
	 * @return
	 */
	List<$!{entity}VO> select$!{entity}Page(IPage page, $!{entity}VO $!{table.entityPath});

}
#end
