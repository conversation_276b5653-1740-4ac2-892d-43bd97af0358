<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>carousel</title>

    <style>
        @page {
            size: 290mm 185mm;
            margin: 0;
            padding: 0;
        }
        @media print{
            /*设置要打印的区域*/
            .content{
                display:block;
                width:100%;
                height:auto;
                overflow:hidden;
            }
        }
        body {
            width: 290mm; /* 横向时的宽度 */
            height: 185mm; /* 横向时的高度 */
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        .content{
            height: 185mm;
            width: 290mm;
            position: relative;
            padding: 0;
            margin: 0;
        }
        .box{
            /*height: 200px;*/
            width: 500px;
            /*border: 1px solid #3a8ee6;*/
            position: absolute;
            top: 300px;
            left: 130px;
            /*transition: transform 0.3s ease;*/
        }
        .bianhao{
            text-align: right;
            font-size: 12px;
        }
        .zhengwen1{
            margin-top: 25px;
            display: flex;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            font-family: 'Comic Sans MS', 'Brush Script MT', cursive;
        }
        .zhengwen2{
            /*width: 400px;*/
            margin: auto;
            font-size: 16px;
            font-weight: bold;
            font-family: 'Comic Sans MS', 'Brush Script MT', cursive;
            text-indent: 2em;
            margin-top: 15px;
            line-height: 25px;
        }
        .zhengwen3{
            /*width: 400px;*/
            margin: auto;
            font-size: 16px;
            font-weight: bold;
            font-family: 'Comic Sans MS', 'Brush Script MT', cursive;
            text-indent: 2em;
            line-height: 25px;

        }
        .yuanxiao{
            position: absolute;
            font-size: 16px;
            font-weight: bold;
            color: #5F5F5F;
            position: absolute;
            bottom: 120px;
            left: 130px;
        }
        .riqi{
            position: absolute;
            font-size: 16px;
            font-weight: bold;
            font-family: 'Comic Sans MS', 'Brush Script MT', cursive;
            position: absolute;
            bottom: 70px;
            left: 130px;
        }
        .qianming{
            position: absolute;
            font-size: 16px;
            font-weight: bold;
            font-family: 'Comic Sans MS', 'Brush Script MT', cursive;
            position: absolute;
            bottom: 130px;
            left: 400px;
        }
        .tiaoxingma{
            width: 160px;
            height: 40px;
            position: absolute;
            bottom: 60px;
            left: 400px;
        }

    </style>
</head>
<body>

<div class="content">
    <div class="box">
        <div class="bianhao">
            NO.<#if lqtzsh ??>${lqtzsh}</#if>
    </div>
    <div class="zhengwen1">
      <span style="margin-right: 25px;">
        <#if xm ??>${xm}</#if>
        </span>
        <span style="margin-right: 25px;">
        同学
      </span>
        <span>
        （高考考生号 <#if ksh ??>${ksh}</#if>）
        </span>
    </div>
    <div class="zhengwen2">
        经山东省教育招生考试院批准，你通过<#if kslxmc ??>${kslxmc}</#if>被录取到我院<#if xymc ??>${xymc}</#if>，<#if lqzymc ??>${lqzymc}</#if>专业学习，为 <#if
加载更多