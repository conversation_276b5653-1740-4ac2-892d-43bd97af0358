<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.mapper.system.ResourceLinkMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.sanyth.model.system.ResourceLink">
        <id column="id" property="id"/>
        <result column="pattern" property="pattern"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
    </resultMap>

    <select id="queryList" resultMap="baseResultMap">
            SELECT T.*
      FROM SYS_RESOURCE_LINK T
     WHERE EXISTS (SELECT 1
              FROM SYS_ROLE_RESOURCE_LINK
             WHERE T.ID = RESOURCE_ID
               AND ROLE_ID = #{roleId, jdbcType=VARCHAR})
	</select>
</mapper>
