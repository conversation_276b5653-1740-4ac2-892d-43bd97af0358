<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.mapper.system.SytSysOrganizationMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="baseResultMap" type="com.sanyth.model.system.SytSysOrganization">
		<id column="ID" property="id" />
		<result column="CODE" property="code" />
		<result column="ORGNAME" property="orgname" />
		<result column="VALID" property="valid" />
		<result column="DISPLAYORDER" property="displayorder" />
		<result column="ORGDESCRIPTION" property="orgdescription" />
		<result column="CATEGORY_ID" property="categoryId" />
		<result column="PARENT" property="parent" />
		<result column="PARENTS" property="parents" />
		<result column="ORGCODE" property="orgcode" />
	</resultMap>

	<sql id="base_columns">
		o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE
	</sql>

	<select id="getOrganizationByUser" resultMap="baseResultMap">
		select <include refid="base_columns"/>
		from SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o
		where u.organization_id = o.id
		and u.user_id = #{userId, jdbcType=VARCHAR}
	</select>
</mapper>
