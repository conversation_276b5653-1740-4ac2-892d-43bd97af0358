<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.mapper.system.ResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.sanyth.model.system.Resource">
        <result column="ID" property="id"/>
        <result column="LABEL" property="label"/>
        <result column="IS_BTN" property="isBtn"/>
        <result column="ICON" property="icon"/>
        <result column="PARAM" property="param"/>
        <result column="PATH" property="path"/>
        <result column="PARENT_ID" property="parentId"/>
        <result column="STATUS" property="status"/>
        <result column="DESCRIPTION" property="description"/>
        <result column="URL" property="url"/>
        <result column="COMPONENT" property="component"/>
        <result column="TYPE" property="type"/>
        <result column="SORT" property="sort"/>
        <result column="EVENT" property="event"/>
        <result column="CODE" property="code"/>
        <result column="ACTION" property="action"/>
        <result column="IS_OPEN" property="isOpen"/>
    </resultMap>

    <sql id="Base_Column_List">
		r.ID,r.code,r.action,r.is_open,r.LABEL ,r.IS_BTN ,r.ICON ,r.PARAM ,r.PATH ,r.PARENT_ID ,r.STATUS ,r.DESCRIPTION ,r.URL ,r.COMPONENT ,r.type ,r.sort,r.event
	</sql>

    <select id="getAllChildren" parameterType="map" resultMap="baseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SYS_RESOURCE r start with r.id = #{id, jdbcType=VARCHAR}
        connect by prior r.id = r.parent_id
    </select>

    <select id="getListByRole" parameterType="map" resultMap="baseResultMap">
        select
        <include refid="Base_Column_List"/>
        from SYS_RESOURCE r, SYS_ROLE_RESOURCE srr
        where r.id = srr.RESOURCE_ID
        and srr.ROLE_ID = #{roleId, jdbcType=VARCHAR}
        and (r.TYPE = '0' or r.TYPE is null)
        and r.STATUS = '是'
        and r.IS_BTN = '否'
        order by r.sort
    </select>
    <select id="getFrontListByRole" resultType="com.sanyth.model.system.Resource">
        select
        <include refid="Base_Column_List"/>
        from SYS_RESOURCE r, SYS_ROLE_RESOURCE srr
        where r.id = srr.RESOURCE_ID
        and srr.ROLE_ID = #{roleId, jdbcType=VARCHAR}
        and r.TYPE = 1
        and r.STATUS = '是'
        and r.IS_BTN = '否'
        order by r.sort
    </select>

    <select id="getButtonsListByRole" resultMap="baseResultMap">
        select t.* from (
        select
        <include refid="Base_Column_List"/>
        from SYS_RESOURCE r
        where r.id in (
        select
         r.parent_id
        from SYS_RESOURCE r, SYS_ROLE_RESOURCE srr
        where r.id = srr.RESOURCE_ID
        and srr.ROLE_ID = #{roleId, jdbcType=VARCHAR}
        and (r.TYPE = '0' or r.TYPE is null)
        and r.STATUS = '是'
        and r.IS_BTN = '是'
        )

        union all

        select
        <include refid="Base_Column_List"/>
        from SYS_RESOURCE r, SYS_ROLE_RESOURCE srr
        where r.id = srr.RESOURCE_ID
        and srr.ROLE_ID = #{roleId, jdbcType=VARCHAR}
        and (r.TYPE = '0' or r.TYPE is null)
        and r.STATUS = '是'
        and r.IS_BTN = '是'
        ) t
    </select>
</mapper>
