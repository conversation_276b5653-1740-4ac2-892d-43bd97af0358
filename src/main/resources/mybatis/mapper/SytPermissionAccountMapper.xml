<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.mapper.system.SytPermissionAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.sanyth.model.system.SytPermissionAccount">
        <result column="ID" property="id"/>
        <result column="HUMANCODE" property="humancode"/>
        <result column="HUMANSMSCODE" property="humansmscode"/>
        <result column="HUMANNAME" property="humanname"/>
        <result column="HUMANDESCRIPTION" property="humandescription"/>
        <result column="CREATEDATE" property="createdate"/>
        <result column="VALIDFROMDATE" property="validfromdate"/>
        <result column="VALIDTODATE" property="validtodate"/>
        <result column="VALIDFLAG" property="validflag"/>
        <result column="HUMANPASSWORD" property="humanpassword"/>
        <result column="SEX" property="sex"/>
        <result column="BIRTHDAY" property="birthday"/>
        <result column="TELOFFICE" property="teloffice"/>
        <result column="TELHOME" property="telhome"/>
        <result column="TELMOBILE1" property="telmobile1"/>
        <result column="TELMOBILE2" property="telmobile2"/>
        <result column="EMAIL" property="email"/>
        <result column="ADDRESS" property="address"/>
        <result column="POSTALCODE" property="postalcode"/>
        <result column="AGE" property="age"/>
        <result column="ORGID" property="orgid"/>
        <result column="SIGNATURE" property="signature"/>
        <result column="ENCRYPTYPE" property="encryptype"/>
        <result column="IDCODE" property="idcode"/>
        <result column="IDTYPE" property="idtype"/>
        <result column="LOGINTIME" property="logintime"/>
        <result column="LOGININFO" property="logininfo"/>
        <result column="DUTYID" property="dutyid"/>
        <result column="HUMANNUMBER" property="humannumber"/>
        <result column="DISPLAYORDER" property="displayorder"/>
        <result column="ORGANIZATIONNAMES" property="organizationnames"/>
        <result column="ACTIVEFLAG" property="activeflag"/>
        <result column="EMPLOYEETYPE" property="employeeType"/>
        <result column="DORMITORYINFO" property="dormitoryInfo"/>
        <result column="MZMC" property="mzmc"/>
    </resultMap>

    <sql id="base_columns">
		t.ID,
		t.HUMANCODE,
		t.HUMANSMSCODE,
		t.HUMANNAME,
		t.HUMANDESCRIPTION,
		t.CREATEDATE,
		t.VALIDFROMDATE,
		t.VALIDTODATE,
		t.VALIDFLAG,
		t.SEX,
		t.BIRTHDAY,
		t.TELOFFICE,
		t.TELHOME,
		t.TELMOBILE1,
		t.TELMOBILE2,
		t.EMAIL,
		t.ADDRESS,
		t.POSTALCODE,
		t.AGE,
		t.ORGID,
		t.SIGNATURE,
		t.ENCRYPTYPE,
		t.IDCODE,
		t.IDTYPE,
		t.LOGINTIME,
		t.LOGININFO,
		t.DUTYID,
		t.HUMANNUMBER,
		t.DISPLAYORDER,
		t.ORGANIZATIONNAMES,
		t.ACTIVEFLAG,
        t.EMPLOYEETYPE,
        t.DORMITORYINFO,
		t.MZMC
	</sql>

    <select id="queryList" resultMap="baseResultMap">
        select
        <include refid="base_columns"/>
        from SYT_PERMISSION_ACCOUNT t
        <where>
            <if test="map.roleId!=null and map.roleId!=''">
                and exists(select 1 from SYT_PERMISSION_ACCOUNT_ROLE ar
                where ar.account_id=t.id
                and ar.role_id=#{map.roleId})
            </if>
            <if test="map.organizationnames!=null and map.organizationnames!=''">
                and exists(select 1 from SYT_SYS_ORGANIZATION_USER ou
                where ou.user_id=t.id
                    and ou.organization_id = #{map.organizationnames})
                    <!--<foreach collection="map.organizationnames" item="org_name" index='index' separator="," open="(" close=")">
                        <if test = 'index%1000 == 999'>'') or id in ( </if>
                        #{org_name}
                    </foreach>-->
            </if>
            <if test="map.humancode!=null and map.humancode!=''">
                <!--and t.humancode=#{humancode,jdbcType=VARCHAR}-->
                and t.humancode like concat(concat('%', #{map.humancode}), '%')
            </if>
            <if test="map.humanname!=null and map.humanname!=''">
                and t.humanname like concat(concat('%', #{map.humanname}), '%')
            </if>
            <if test="map.dormitoryInfo!=null and map.dormitoryInfo!=''">
                and t.DORMITORYINFO like concat(concat('%', #{map.dormitoryInfo}), '%')
            </if>
            <if test="map.sex!=null and map.sex!=''">
                and t.sex=#{map.sex,jdbcType=VARCHAR}
            </if>
            <if test="map.telmobile1!=null and map.telmobile1!=''">
                and t.TELMOBILE1=#{map.telmobile1,jdbcType=VARCHAR}
            </if>
            <if test="map.employeeType!=null and map.employeeType!=''">
                and t.EMPLOYEETYPE=#{map.employeeType,jdbcType=VARCHAR}
            </if>
            <if test="map.mzmc !=null and map.mzmc.size()>0">
                and t.mzmc in
                <foreach collection="map.mzmc" item="mz" index='index' separator="," open="(" close=")">
                    <if test = 'index%1000 == 999'>'') or id in ( </if>
                    #{mz}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
