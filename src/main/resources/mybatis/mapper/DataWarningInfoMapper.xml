<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.mapper.datawarning.DataWarningInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.sanyth.model.datawarning.DataWarningInfo">
        <result column="ID" property="id" />
        <result column="HUMANCODE" property="humanCode" />
        <result column="HUMANNAME" property="humanName" />
        <result column="ORGANIZATIONNAMES" property="organizationNames" />
        <result column="WARNTYPE" property="warnType" />
        <result column="WARNNAME" property="warnName" />
        <result column="WARNREASON" property="warnReason" />
        <result column="WARNTIME" property="warnTime" />
        <result column="WARNLEVEL" property="warnLevel" />
<!--        <result column="STATUS" property="status" />-->
        <result column="TASKRECORDID" property="taskRecordId" />
        <result column="RULEID" property="ruleId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="base_columns">
        ID, HUMANCODE, HUMANNAME, ORGANIZATIONNAMES, WARNTYPE, WARNNAME, WARNREASON, WARNTIME, WARNLEVEL, TASKRECORDID, RULEID
    </sql>

    <!--查询推送推送人员名单-->
    <select id="getPushList" resultType="java.util.Map">
        select a.HUMANCODE,a.ID USER_ID,ar.ROLE_ID,r.ROLENAME from SYT_PERMISSION_ACCOUNT a, SYT_PERMISSION_ACCOUNT_ROLE ar,SYT_PERMISSION_ROLE r
        <where>
            and a.id=ar.ACCOUNT_ID and r.id=ar.ROLE_ID
            <if test="roles!=null and roles.size>0">
                and ar.ROLE_ID in
                <foreach collection="roles" item="role_id" separator="," open="(" close=")">
                    #{role_id}
                </foreach>
            </if>

        </where>
        group by a.HUMANCODE,a.id,ar.ROLE_ID,r.ROLENAME
    </select>

    <!--查询指定账号关联的人员-->
    <select id="getRelationData" resultType="java.lang.String" parameterType="java.util.Map" >
        select a.HUMANCODE from SYT_PERMISSION_ACCOUNT a, SYT_SYS_ORGANIZATION_USER u
        <where>
            and a.ID=u.USER_ID
                <if test="humancode!=null and humancode!=''">
                    and a.HUMANCODE=#{humancode}
                </if>
                <if test="organization_id!=null and organization_id.size()>0">
                    and u.organization_id in
                    <foreach collection="organization_id" item="org_id" index='index' separator="," open="(" close=")">
                        <if test = 'index%1000 == 999'>'') or id in ( </if>
                        #{org_id}
                    </foreach>
                </if>

        </where>
        group by a.HUMANCODE
    </select>

    <!--预警数据统计-->
    <select id="getStatisticsData" resultType="com.sanyth.dto.ExportWarnStatisticsDto" parameterType="com.sanyth.dto.DataWarningInfoDto">
        SELECT WARNTYPE,
        count(DISTINCT HUMANCODE) rs,
        count(HUMANCODE) rc
        from DATA_WARNING_INFO t
        <where>
            and exists(select 1 from SYT_PERMISSION_ACCOUNT pa
            where t.humancode=pa.humancode
            and pa.validflag = 0
            <if test="employeeType!=null and employeeType!=''">
                and pa.employeeType=#{employeeType}
            </if>
            )
            <if test="taskRecordId!=null and taskRecordId!=''">
                and taskRecordId=#{taskRecordId}
            </if>
            <if test="ruleId!=null and ruleId!=''">
                and ruleId=#{ruleId}
            </if>
            <if test="warnLevel!=null and warnLevel!=''">
                and warnLevel=#{warnLevel}
            </if>
            <if test="warnType!=null and warnType!=''">
                and WARNTYPE=#{warnType}
            </if>
            <if test="startTime!=null and startTime!=''">
                <if test="startTime.length()==7">
                    and TO_CHAR(warnTime,'YYYY-MM') &gt;=#{startTime}
                </if>
                <if test="startTime.length()==10">
                    and TO_CHAR(warnTime,'YYYY-MM-DD') &gt;=#{startTime}
                </if>
            </if>
            <if test="endTime!=null and endTime!=''">
                <if test="endTime.length()==7">
                    and TO_CHAR(warnTime,'YYYY-MM') &lt;=#{endTime}
                </if>
                <if test="endTime.length()==10">
                    and TO_CHAR(warnTime,'YYYY-MM-DD') &lt;=#{endTime}
                </if>
            </if>

        </where>
        group by WARNTYPE
    </select>

    <!--获取预警人数-->
    <select id="getRsWarnData" resultType="java.util.Map" parameterType="com.sanyth.dto.DataWarningInfoDto">
        SELECT count( DISTINCT HUMANCODE ) total
        from DATA_WARNING_INFO t
        <where>
            and exists(select 1 from SYT_PERMISSION_ACCOUNT pa
            where t.humancode=pa.humancode
            and pa.validflag = 0
            <if test="employeeType!=null and employeeType!=''">
                and pa.employeeType=#{employeeType}
            </if>
            )
            <if test="taskRecordId!=null and taskRecordId!=''">
                and taskRecordId=#{taskRecordId}
            </if>
            <if test="ruleId!=null and ruleId!=''">
                and ruleId=#{ruleId}
            </if>
            <if test="warnLevel!=null and warnLevel!=''">
                and warnLevel=#{warnLevel}
            </if>
            <if test="warnType!=null and warnType!=''">
                and WARNTYPE=#{warnType}
            </if>
            <if test="startTime!=null and startTime!=''">
                <if test="startTime.length()==7">
                    and TO_CHAR(warnTime,'YYYY-MM') &gt;=#{startTime}
                </if>
                <if test="startTime.length()==10">
                    and TO_CHAR(warnTime,'YYYY-MM-DD') &gt;=#{startTime}
                </if>
            </if>
            <if test="endTime!=null and endTime!=''">
                <if test="endTime.length()==7">
                    and TO_CHAR(warnTime,'YYYY-MM') &lt;=#{endTime}
                </if>
                <if test="endTime.length()==10">
                    and TO_CHAR(warnTime,'YYYY-MM-DD') &lt;=#{endTime}
                </if>
            </if>

        </where>
    </select>

    <!--获取预警人次-->
    <select id="getRcWarnData" resultType="java.util.Map" parameterType="com.sanyth.dto.DataWarningInfoDto">
        SELECT count( HUMANCODE ) total
        from DATA_WARNING_INFO t
        <where>
            and exists(select 1 from SYT_PERMISSION_ACCOUNT pa
            where t.humancode=pa.humancode
            and pa.validflag = 0
            <if test="employeeType!=null and employeeType!=''">
                and pa.employeeType=#{employeeType}
            </if>
            )
            <if test="taskRecordId!=null and taskRecordId!=''">
                and taskRecordId=#{taskRecordId}
            </if>
            <if test="ruleId!=null and ruleId!=''">
                and ruleId=#{ruleId}
            </if>
            <if test="warnLevel!=null and warnLevel!=''">
                and warnLevel=#{warnLevel}
            </if>
            <if test="warnType!=null and warnType!=''">
                and WARNTYPE=#{warnType}
            </if>
            <if test="startTime!=null and startTime!=''">
                <if test="startTime.length()==7">
                    and TO_CHAR(warnTime,'YYYY-MM') &gt;=#{startTime}
                </if>
                <if test="startTime.length()==10">
                    and TO_CHAR(warnTime,'YYYY-MM-DD') &gt;=#{startTime}
                </if>
            </if>
            <if test="endTime!=null and endTime!=''">
                <if test="endTime.length()==7">
                    and TO_CHAR(warnTime,'YYYY-MM') &lt;=#{endTime}
                </if>
                <if test="endTime.length()==10">
                    and TO_CHAR(warnTime,'YYYY-MM-DD') &lt;=#{endTime}
                </if>
            </if>

        </where>
    </select>

</mapper>
