<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sanyth.mapper.system.IndexDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="baseResultMap" type="com.sanyth.dto.StatisticsDto">
        <result column="NAME" property="name"/>
        <result column="VALUE" property="value"/>
    </resultMap>

    <sql id="Base_Column_List">
		name ,value
	</sql>
    <select id="queryAccountNumber" resultType="java.util.Map">
        select count(*) zhzs from SYT_PERMISSION_ACCOUNT
    </select>
    <select id="queryNumberByField" resultType="java.util.Map"  statementType="STATEMENT">
        select ${field} name,count(*) value from SYT_PERMISSION_ACCOUNT group by ${field}
    </select>
    <select id="queryVisitNumber" resultType="java.util.Map">
        select count(distinct HUMAN_CODE) fwrs from sys_login_logs
    </select>
    <select id="queryAppNumber" resultType="java.util.Map">
        select count(*) yys from oauth_client_details
    </select>
    <select id="queryOrgNumber" resultType="java.util.Map">
        select count(*) orgnum from SYT_SYS_ORGANIZATION
    </select>
    <select id="queryVisitNumberByDay" resultType="java.util.Map">
        select to_char(LOGIN_TIME,'dd') day,count(distinct HUMAN_CODE) num from sys_login_logs
        where LOGIN_TIME between trunc(sysdate,'mm') and sysdate group by to_char(LOGIN_TIME,'dd')
        order by to_char(LOGIN_TIME,'dd')
    </select>


</mapper>
