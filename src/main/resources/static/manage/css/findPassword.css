@charset "utf-8";

body {
	padding: 0;
	background: #fff;
}

.entry-header {
	height: 95px;
	/*min-height: 80px;*/
	width: 100%;
	/*border-top: 5px solid #1670a6;*/
	box-sizing: border-box;
}

.entry-header p {
	line-height: 80px;
}

.logo-img {
	height: 66px;
	/*width: 482px;*/
	float: left;
	margin-left: 20px;
	margin-top: 10px;
}

.entry-header span {
	color: #666666;
	font-size: 22px;
	margin-left: 20px;
	line-height: 100px;
	padding-left: 20px;
	display: inline-block;
	height: 40px;
	line-height: 40px;
	margin-top: 30px;
}

.entry-header {
	color: #1e649f;
	font-size: 20px;
}

.entry-main {
	width: 100%;
	position: relative;
	overflow: hidden;
	height: calc(100vh - 95px);
	background: #F0F2F5;
	/*margin-top: 35px;*/
}

.titleName {
	border-left: 1px solid #B7B8B8;
}

.entry-footer {
	height: 50px;
	width: 100%;
	padding: 0px 20px;
	text-align: center;
	line-height: 52px;
	color: #f8f8f8;
	font-size: 12px;
	position: absolute;
	left: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.2);
}

.entry-footer p {
	margin-bottom: 0;
}

.registerWrap {
	width: 1200px;
	margin: 0 auto;
	background: #fff;
	margin-top: 70px;
	border-radius: 10px;
	padding: 0 45px;
	padding-top: 0.1px;
}

.title {
	width: 100%;
	height: 50px;
	margin-top: -25px;
	text-align: center;
}

.step {
	width: 370px;
	height: 70px;
	margin-top: 10px;
	float: left;
	color: #999;
	text-align: center;
	background: url(../img/mr1.png) no-repeat left top;
}

.step div {
	height: 55px;
	line-height: 55px;
	font-size: 18px;
}

.step p {
	height: 15px;
	line-height: 15px;
	font-size: 14px;
}

.stepClick {
	background: url(../img/xz1.png) no-repeat left top;
	color: #1074D6;
}

.stepClick p {
	color: #1074D6;
}

.stepContent {
	width: 100%;
	margin-top: 30px;
	padding: 0 30px;
}

.one,
.two {
	width: 100%;
	height: 40px;
	line-height: 40px;
	margin-bottom: 30px;
}

.oneSpan1 {
	width: 350px;
	height: 40px;
	float: left;
	text-align: right;
	padding-right: 10px;
	font-size: 16px;
	color: #333;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.one input {
	float: left;
	width: 350px;
	height: 40px;
	border: 1px solid #ccc;
	border-radius: 3px;
	padding-left: 5px;
}

.one .radio {
	width: 120px;
	display: inline-block;
}

.one .radio input {
	width: auto;
	border: 1px solid #ccc;
	border-radius: 3px;
	padding-left: 5px;
	vertical-align: middle;
	margin-right: 5px;
}

.oneSpan2 {
	float: right;
	width: 350px;
	height: 40px;
	padding-left: 10px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.red {
	color: red;
	font-size: 12px;
	display: none;
}

.yzm {
	width: 80px;
	height: 40px;
	background: #1F93E0;
	color: #fff;
	margin-left: 10px;
	border: none;
	border-radius: 3px;
	margin-bottom: 30px;
	font-size: 12px;
	text-align: center;
	cursor: pointer;
}

.next {
	width: 100px;
	height: 35px;
	background: #1F93E0;
	color: #fff;
	margin-left: calc(50% - 50px);
	border: none;
	border-radius: 3px;
	margin-bottom: 30px;
}

.twoSpan1 {
	width: 180px;
	height: 30px;
	float: left;
	text-align: right;
	padding-right: 10px;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.two input,
.two select {
	float: left;
	width: 300px;
	height: 30px;
	border: 1px solid #ccc;
	border-radius: 3px;
	padding-left: 5px;
}

.two1 {
	height: 100px;
}

.two1 textarea {
	float: left;
	width: 780px;
	height: 100px;
	border: 1px solid #ccc;
	border-radius: 3px;
	resize: none;
}

.yzm {
	float: left;
	width: 80px;
	height: 40px;
	margin-left: 20px;
}

.yzm img {
	width: 100%;
	height: 100%;
	background: #ccc;
	margin-top: -1px;
}

.stepItem4 {
	text-align: center;
	font-size: 20px;
	padding-bottom: 50px;
	line-height: 50px;
	color: #68D279;
}

.stepItem4 img {
	margin-top: 0px;
	width: 100px;
}

.stepItem4 button {
	margin-left: 0;
	font-size: 14px;
}

.buttonWrap {
	width: 100%;
	text-align: center;
}

.buttonWrap button {
	margin-left: 0;
}


.entry-header .loginHelp{
	float: right;
	margin-right: 3%;
	color: #1890ff;
	font-size: 14px;
	cursor: pointer;
}