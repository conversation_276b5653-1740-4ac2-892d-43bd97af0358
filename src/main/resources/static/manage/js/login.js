var contextPath;
var _IS_VALID_CAPTCHA = false; // 是否需要输入验证码
$(function () {

    var pathName = window.location.pathname.substring(1);
    var webName = pathName == '' ? '' : pathName.substring(0, pathName.indexOf('/'));
    contextPath = window.location.protocol + '//' + window.location.host + '/' + webName;
    if (contextPath.charAt(contextPath.length - 1) == "/") {
        contextPath = contextPath.substring(0, contextPath.length - 1);
    }

    $("#userlogin").click(function () {
        if ($("#user").val() === '') {
            alert('请输入用户名');
            $("#user").focus();
            return;
        }
        if ($("#pass").val() === '') {
            alert('请输入密码');
            $("#pass").focus();
            return;
        }
        if (_IS_VALID_CAPTCHA && $("#captcha").val() === '') {
            alert('请输入验证码');
            $("#captcha").focus();
            return;
        }
        var url = contextPath + "/login";
        var data = {
            "username": encrypt($("#user").val()),
            "password": encrypt($("#pass").val()),
            "vercode": encrypt($("#captcha").val())
        };
        //获取表单输入:用户名,密码,是否保存密码

        // var loading = document.getElementById("loading");
        // loading.style.display = "block";
        $.ajax(url, {
            method: 'POST',
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            data: data,
            beforeSend: function (XMLHttpRequest) {
                XMLHttpRequest.setRequestHeader("sign", RSA_SIGN(JSON.stringify(data)));
            },
            success: function (res) {
                // loading.style.display = "none";
                if (res.code === '00000') {
                    var redirectUrl = res.info.redirectUrl;
                    var regex = "^((https|http)?://)";
                    var re = new RegExp(regex);
                    if (re.test(redirectUrl)) {
                        window.location.href = redirectUrl;
                    } else {
                        redirectUrl = contextPath + redirectUrl;
                        window.location.href = redirectUrl;
                    }
                    var username = $("#user").val();
                    var password = $("#pass").val();
                    var isRmbPwd = $("#remember").prop('checked');
                    //判断用户名,密码是否为空(全空格也算空)
                    if ( username.length != 0 && password.length != 0 ){
                        //若复选框勾选,则添加Cookie,记录密码
                        if ( isRmbPwd == true ){
                            setCookie ( "username", username, 7 ) ;
                            setCookie ( "password", password, 7 ) ;
                        }else{
                            delCookie ("username") ;
                            delCookie ("password") ;
                        }
                        // return true ;
                    }
                    // else{
                    //     alert('请输入必填字段!!!')
                    //     return false ;
                    // }
                } else {
                    alert(res.info);
                    if (res.code === '00003') {
                        window.location.reload();
                    } else {
                        // changeCaptcha();
                        isValidCaptcha();
                    }
                }
            }
        });
    });

    $(document).keydown(function (event) {
        if (event.keyCode === 13) {
            $("#userlogin").click();
        }
    });
    //判断是否需要显示验证码
    isValidCaptcha();

    // 发送手机验证码 60s倒计时
    $('.yzmPhone').on('click', function(e) {
        if (e.preventDefault) {
            e.preventDefault();
        } else {
            window.event.returnValue == false;
        }
        $("#phone-yzm").val("");
        var that = $(this);
        var phone = $("#phone").val();
        if (phone === '') {
            // alert('请输入手机号');
            $("#redTip").text("手机号不能为空");
            $("#redTip").show();
            // $("#phone").focus();
            return;
        }
        // 校验手机号
        if(checkMobile(phone)){
            // alert("请输入有效手机号");
            $("#redTip").text("手机号格式不正确");
            $("#redTip").show();
            return;
        }
        var timeo = 60;
        var timeStop = setInterval(function() {
            timeo--;
            if (timeo > 0) {
                that.text('重新发送' + timeo + 's');
                that.attr('disabled', 'disabled').css("background","#ccc"); //禁止点击
            } else {
                timeo = 60; //当减到0时赋值为60
                that.text('获取验证码');
                clearInterval(timeStop); //清除定时器
                that.removeAttr('disabled').css("background","#fff"); //移除属性，可点击
            }
        }, 1000);
        // 发送验证码
        var url = contextPath + "/user/sendPhoneLoginVerifyCode";
        var searchData = {"phone": phone};
        $.ajax(url, {
            method: 'POST',
            dataType: 'json',
            contentType: 'application/json; charset=UTF-8',
            data: JSON.stringify(searchData),
            beforeSend: function (XMLHttpRequest) {
                XMLHttpRequest.setRequestHeader("sign", RSA_SIGN(JSON.stringify(searchData)));
            },
            success: function (res) {
                if(res.code === '00000'){
                    var tmp = phone;
                    var textShow = "验证码已发送至" + desensitizePhone(tmp) + "手机中，请注意查收";
                    $("#greyTip").text(textShow);
                    $("#greyTip").show();
                }else{
                    $("#redTip").text(res.info);
                    $("#redTip").show();
                }
            }
        });
    })

    // 手机号登录
    $("#phone-userlogin").click(function () {
        var phone = $("#phone").val();
        if (phone === '') {
            alert('请输入手机号');
            $("#phone").focus();
            return;
        }
        // 校验手机号
        if(checkMobile(phone)){
            // alert("请输入有效手机号");
            $("#redTip").text("手机号格式不正确");
            $("#redTip").show();
            return;
        }
        var vercode = $("#phone-yzm").val();
        if (vercode === '') {
            alert('请输入验证码');
            $("#phone-yzm").focus();
            return;
        }
        var url = contextPath + "/phoneLogin";
        var data = {
            "mobile": encrypt(phone),
            "vercode": encrypt(vercode)
        };
        $.ajax(url, {
            method: 'POST',
            contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
            data: data,
            success: function (res) {
                // loading.style.display = "none";
                if (res.code === '00000') {
                    var redirectUrl = res.info.redirectUrl;
                    var regex = "^((https|http)?://)";
                    var re = new RegExp(regex);
                    if (re.test(redirectUrl)) {
                        window.location.href = redirectUrl;
                    } else {
                        redirectUrl = contextPath + redirectUrl;
                        window.location.href = redirectUrl;
                    }
                } else {
                    alert(res.info);
                    if (res.code === '00003') {
                        window.location.reload();
                    }
                }
            }
        });
    });
});

function changeCaptcha() {
    document.getElementById("captchaImg").src = contextPath + '/captcha?' + Math.random();
}

function setCookie ( name, value, expdays )
{
    var expdate = new Date();
    //设置Cookie过期日期
    expdate.setDate(expdate.getDate() + expdays) ;
    //添加Cookie
    document.cookie = name + "=" + escape(value) + ";expires=" + expdate.toUTCString();
}
function getCookie ( name )
{
    //获取name在Cookie中起止位置
    var start = document.cookie.indexOf(name+"=") ;

    if ( start != -1 )
    {
        start = start + name.length + 1 ;
        //获取value的终止位置
        var end = document.cookie.indexOf(";", start) ;
        if ( end == -1 )
            end = document.cookie.length ;
        //截获cookie的value值,并返回
        return unescape(document.cookie.substring(start,end)) ;
    }
    return "" ;
}
function delCookie ( name )
{
    setCookie ( name, "", -1 ) ;
}

//判断是否需要显示验证码
function isValidCaptcha(){
    var url = contextPath + '/captchaIsvalid';
    var val = $("#user").val();
    // if(!!val){
        var username =  encrypt(val);
        $.post(url, {"username": username},function(valid){
            if (valid) {
                _IS_VALID_CAPTCHA = true;
                $('.loginYzm').show();
                changeCaptcha();
            } else {
                $('.loginYzm').hide();
            }
        });
    // }
}
// 校验手机号
function checkMobile(v){
    var re = new RegExp("^1[3|4|5|6|7|8|9][0-9]{9}$");
    if (!re.test(v)) return true;
    return false;
}
// 手机号脱敏
function desensitizePhone(phone) {
    return phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2");
}


