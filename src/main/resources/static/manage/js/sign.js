
var privateKey = '-----BEGIN PRIVATE KEY-----\n' +
    'MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCzsE+/xB2tvffp\n' +
    'ti9jTC/tLHyMRe9fB6GcdDpDe1OvFBE2OHMj/kyqTXMO9Yl6tUF1eK0eevElUREL\n' +
    'd45PZ9h1+fVEdIJ4v+ApJ8rYd2o2h3vr/FCpMhkQYqlrXmOG4iyyRX/viD5Slnsj\n' +
    'trjAj+UWfIMis1rHQK+zN4Mtp0cRDToOn01PIYJSjqrp0N8X0bhpUV61lcKwfKgd\n' +
    '12TVavkCAIr9HRJWp9dikdGgQhUCrsKulBGM4qKtfEwv6R/6ByPunGevZ8PmFa7b\n' +
    'C7VJFFiSrlo/7BM3Gtv+azXmJtpI+/Za7atX234zGDWwci/akFnFizxThQ5L6CKl\n' +
    'IqUznby3AgMBAAECggEBALNHBtpSpjXRoobIoZEV039z8jPWFtSefYzmnbb0VfMJ\n' +
    'N378ZUyVDRKhQVm8etU2AMBejYMqsGxF7JafSxpIhrx2jlIlWIN3FQSa0hSVWCBd\n' +
    'nrM9Q5D8Deu+NECOpQhPor6e/t0S9vNZdjSUutJM/wknY4L8pWbFUEej7DphelM0\n' +
    'S6eq89NK6epk0kiUPH5AWpXJfjH/Jien8NbHW3+HGhPYIviRwW3JobkZEk4+5yQO\n' +
    'cc2InAl9rILI2v/mgCqjmMB+pRjKgoa/x5KpX2iS9K/9fBCQDKx21T1reSXaeF3F\n' +
    'Xsxd6IyFVPPrwsraymL4Ut066dIFsLQCY4vmcj5/gBECgYEA7w3etTS6IhswYm51\n' +
    'sK+l20p2/7vLx/uRS+6+SkgFIvPu1ynSQNVbC/bxeGGDsShSJq40gGCVRYBAd3S4\n' +
    'm9y+HTHCaQJJMi9zmU02GsSmkiqXPIrUBxA3xReGfWP+ePOCFHia8P8q8HURbJrT\n' +
    'zXWjT4P/ws/SN+RMl04u9sD53B8CgYEAwG0iEVe5U7THihEg1V/dLn0SIXJ84q8h\n' +
    '5W5Cq3fbswy/uEsv39MXVswEqDllEXp1NHUhHEAmNGIIMeSoJq3DWouJ5XcCeApu\n' +
    'jT0NpBAJquEfY3emK6KypQBY0oP30NkjxzMOAm83yPpAH75xD8YT4AZpR4+wTjSi\n' +
    'yP+M0W8PDGkCgYBWiFPEjjiOXf8uskNh2MMV1SesPKNwWYznyp0RMG7Djz82nYnh\n' +
    'NBoIA7S22b2c5DaQj+CrU8rU5K7xTswAh3A+CyQPdOdgBjM3G+7o06RkJoymOq6z\n' +
    'e9hkPLbPLuIPkD3XAS087XDyp73N7WGb7uhVwTdUonu7BaX/4yo24fXQCwKBgGsU\n' +
    'D7Ig48M9xRACigfSqa018gHkyLQICs+2NKlgMESJoqI2TLS7rbAiJbFLBv/b6h6/\n' +
    '0yX6WIgryS2idSPY6+2V2q1zNU+BVpWH1PBn8ElDrwjVywVbKxgAgmza/Osd+ffe\n' +
    'ZDxckWloQIipnBbP/b1bM/ibb9uFFNTOyBi/OVuJAoGBAOoCA2QnEpqnVNSfQbxt\n' +
    'MKrwenYhXFogUYoWI9HXmSjRbEM0KcLdOgU0rCpc+zKmUMQ8DRl2Ri3e1xSNTqVp\n' +
    'eloF0MyPinfXoR8jMzFwJKVdUfFin+uZPvnC3weKjgPI2k9FNoQl4rk45iLxNiSK\n' +
    'hPclt+0NeDcX1trH0HBYy2LI\n' +
    '-----END PRIVATE KEY-----'

// 签名算法
var ALGORITHM = 'SHA256withRSA'

// 私钥签名
function RSA_SIGN(src){
    var signature = new KJUR.crypto.Signature({'alg': ALGORITHM})
    // 来解析密钥
    var priKey = KEYUTIL.getKey(privateKey)
    signature.init(priKey)
    // 传入待签明文
    signature.updateString(src)
    var a = signature.sign()
    // 转换成base64，返回
    return hex2b64(a)
}
// 公钥验签
function RSA_VERIFY_SIGN(publicKey, src, data){
    var signature = new KJUR.crypto.Signature({'alg': ALGORITHM, 'prvkeypem': publicKey})
    signature.updateString(src)
    return signature.verify(b64tohex(data))
}
