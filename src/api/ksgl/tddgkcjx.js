import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/ksgl/tddgkcjx/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/ksgl/tddgkcjx/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/ksgl/tddgkcjx/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/ksgl/tddgkcjx/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/ksgl/tddgkcjx/submit',
    method: 'post',
    data: row
  })
}

