import request from '@/router/axios';
import {baseUrl} from "@/config/env";
import FileSaver from 'file-saver';

export const getList = (current, size, params) => {
  return request({
    url: '/ksgl/enrollplan/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/ksgl/enrollplan/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/ksgl/enrollplan/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/ksgl/enrollplan/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/ksgl/enrollplan/submit',
    method: 'post',
    data: row
  })
}

export async function exportDataAsync(data) {
  try {
    // 发送POST请求获取字节流数据，传递查询参数
    const response = await request.post(baseUrl + '/ksgl/enrollplan/export', data, {
      responseType: 'arraybuffer',
    });
    // 获取文件名
    const disposition = response.headers['content-disposition'];
    const encodedFileName = disposition.split(';')[1].trim().split('=')[1];
    const fileName = decodeURIComponent(encodedFileName); // 解码文件名
    // 创建Blob对象
    const blob = new Blob([response.data], { type: 'application/octet-stream' });
    // 使用FileSaver.js保存文件，传入获取到的文件名
    FileSaver.saveAs(blob, fileName);
  } catch (error) {
    console.error('Error exporting data:', error);
  }
}


