import request from '@/router/axios';
import {baseUrl} from "@/config/env";
import FileSaver from 'file-saver';

export const selectByField = (data) => {
  return request({
    url: '/ksgl/tdd/selectByField',
    method: 'post',
    data: data,
  })
}
export const getAll = (params) => {
  return request({
    url: '/ksgl/tdd/all',
    method: 'get',
    params: {
      ...params,
    }
  })
}

export const getList = (current, size, params) => {
  return request({
    url: '/ksgl/tdd/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const selecRrevisedPage = (current, size, params) => {
  return request({
    url: '/ksgl/tdd/selecRrevisedPage',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/ksgl/tdd/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/ksgl/tdd/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/ksgl/tdd/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/ksgl/tdd/submit',
    method: 'post',
    data: row
  })
}

export const getone = (id) => {
  return request({
    url: '/ksgl/tdd/one',
    method: 'get',
    params: {
      id
    }
  })
}

export const transferInto = (row) => {
  return request({
    url: '/ksgl/tdd/transferInto',
    method: 'post',
    data: row
  })
}

export const geturldata = (params,url) => {
  return request({
    url: url,
    method: 'get',
    params: {
      ...params,
    }
  })
}

export const revised = (data) => {
  return request({
    url: '/ksgl/tdd/revised',
    method: 'post',
    data: data,
  })
}

export const oneKeyRevised = (data) => {
  return request({
    url: '/ksgl/tdd/oneKeyRevised',
    method: 'post',
    data: data,
  })
}

export const oneKeyRestore = (data) => {
  return request({
    url: '/ksgl/tdd/oneKeyRestore',
    method: 'post',
    data: data,
  })
}


export const autoRevised = (data,revisedType) => {
  return request({
    url: '/ksgl/tdd/autoRevised',
    method: 'post',
    data: data,
    params: {
      revisedType: revisedType
    }
  })
}


export const edit = (row,ids) => {
  return request({
    url: '/ksgl/tdd/edit',
    method: 'post',
    data: row,
    params: {
      ids,
    }
  })
}

export const print = (data) => {
  return request({
    url: '/ksgl/lqtzs/print',
    method: 'post',
    data: {...data},
    // params: {
    //   ids,
    // }
  })
}

export const allAndField = (data) => {
  return request({
    url: '/dict/dictgroup/allAndField',
    method: 'get',
    params: {
      data
    }
  })
}

export const preview = (data) => {
  return request({
    url: '/ksgl/lqtzs/preview',
    method: 'get',
    params: {
      ...data
    }
  })
}

export const sbclear = (data) => {
  return request({
    url: '/ksgl/tdd/sbclear',
    method: 'get',
    params: {
      ...data
    }
  })
}

export const tddclear = (data) => {
  return request({
    url: '/ksgl/tdd/tddclear',
    method: 'get',
    params: {
      ...data
    }
  })
}

export const sbview = (data) => {
  return request({
    url: '/ksgl/tdd/sbview',
    method: 'get',
    params: {
      ...data
    }
  })
}



export async function exportData(data,param) {
  try {
    // 合并参数
    const requestData = {
      ...data,
      ...param,
    };

    // 发送请求获取字节流数据
    const response = await request.post(baseUrl + '/ksgl/tdd/export', requestData, {
      responseType: 'arraybuffer'
    });

    // 获取文件名
    const disposition = response.headers['content-disposition'];
    const encodedFileName = disposition.split(';')[1].trim().split('=')[1];
    const fileName = decodeURIComponent(encodedFileName); // 解码文件名
    // 创建Blob对象
    const blob = new Blob([response.data], { type: 'application/octet-stream' });
    // 使用FileSaver.js保存文件，传入获取到的文件名
    FileSaver.saveAs(blob, fileName);
  } catch (error) {
    console.error('Error exporting data:', error);
    throw error; // 重新抛出错误，让调用方能够捕获
  }
}

// 创建三表下载任务
export function createSbDownloadTask(data, param) {
  return request({
    url: '/ksgl/tdd/sbdownload',
    method: 'post',
    data: {
      ...data,
      ...param,
    }
  })
}

//清空三表
export async function clearSbData(data,param) {
  return request({
    url: '/ksgl/tdd/sbclear',
    method: 'post',
    data: data,
    params:{
      ...param,
    }
  })
}

export async function exportPhotos(data,param) {
  try {
    // 发送请求获取字节流数据
    const response = await request.get(baseUrl + '/ksgl/tdd/downloadPhotos', {
      responseType: 'arraybuffer',
      params:{
        ...data,
        ...param,
      }
    });
    // 获取文件名
    const disposition = response.headers['content-disposition'];
    const encodedFileName = disposition.split(';')[1].trim().split('=')[1];
    const fileName = decodeURIComponent(encodedFileName); // 解码文件名
    // 创建Blob对象
    const blob = new Blob([response.data], { type: 'application/octet-stream' });
    // 使用FileSaver.js保存文件，传入获取到的文件名
    FileSaver.saveAs(blob, fileName);
  } catch (error) {
    console.error('Error exporting data:', error);
  }
}

