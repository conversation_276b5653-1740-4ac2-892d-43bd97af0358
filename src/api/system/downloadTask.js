import request from '@/router/axios';

const baseUrl = '/system/downloadTask';

// 获取用户任务列表
export const getUserTasks = () => {
  return request({
    url: baseUrl + '/userTasks',
    method: 'get'
  });
};

// 获取任务统计信息
export const getTaskStatistics = () => {
  return request({
    url: baseUrl + '/statistics',
    method: 'get'
  });
};

// 分页查询任务列表
export const getTaskList = (current, size, params) => {
  return request({
    url: baseUrl + '/list',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  });
};

// 获取任务详情
export const getTaskDetail = (id) => {
  return request({
    url: baseUrl + '/detail',
    method: 'get',
    params: { id }
  });
};

// 重试任务
export const retryTask = (taskId) => {
  return request({
    url: baseUrl + '/retry/' + taskId,
    method: 'post'
  });
};

// 取消任务
export const cancelTask = (taskId) => {
  return request({
    url: baseUrl + '/cancel/' + taskId,
    method: 'post'
  });
};

// 删除任务
export const removeTask = (taskId) => {
  return request({
    url: baseUrl + '/remove/' + taskId,
    method: 'post'
  });
};

// 下载文件
export const downloadTaskFile = (taskId) => {
  return request({
    url: baseUrl + '/download/' + taskId,
    method: 'get',
    responseType: 'blob'
  }).then(response => {
    // 由于axios拦截器返回完整的响应对象，这里需要返回response.data
    console.log('下载响应:', response);
    return response.data;
  });
};

// 创建下载任务
export const createTask = (data) => {
  return request({
    url: baseUrl + '/create',
    method: 'post',
    data
  });
}; 