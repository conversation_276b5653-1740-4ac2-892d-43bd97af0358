import request from '@/router/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/code/codenjb/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/code/codenjb/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/code/codenjb/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/code/codenjb/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/code/codenjb/submit',
    method: 'post',
    data: row
  })
}

