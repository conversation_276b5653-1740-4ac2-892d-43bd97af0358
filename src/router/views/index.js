import Layout from '@/page/index/'

export default [
    {
        path: '/wel',
        component: Layout,
        redirect: '/wel/index',
        children: [{
            path: 'index',
            name: '首页',
            meta: {
                i18n: 'dashboard',
                // isAuth: false
            },
            component: () =>
                // import( /* webpackChunkName: "views" */ '@/views/report/manage/index')
                import( /* webpackChunkName: "views" */ '@/views/wel/index')
        },
            {
                path: '/dataimport/index',
                name: '数据导入',
                component: () =>
                    import('@/page/zsxtreception/dataimport'),
                meta: {
                    isTab: true,
                    isAuth: false,
                    isoperate: false,
                }
            },
            {
                path: '/zsxt-index',
                name: '招生首页',
                component: () =>
                    import('@/page/zsxtreception/index'),
                meta: {
                    isTab: false,
                    isAuth: false,
                    isoperate: true,
                }
            },
            {
                path: '/zsxt-analysis',
                name: '招生数据分析',
                component: () =>
                    import('@/page/zsxtreception/analysis'),
                meta: {
                    isTab: false,
                    isAuth: false,
                    isoperate: true,
                }
            },
            {
                path: '/zsxt-selfservice',
                name: '自助分析',
                component: () =>
                    import('@/page/zsxtreception/selfservice'),
                meta: {
                    isTab: false,
                    isAuth: false,
                    isoperate: true,
                }
            },
            {
                path: '/zsxt-shengyuanzhiliang',
                name: '生源质量报告',
                component: () =>
                    import('@/page/zsxtreception/shengyuanzhiliang'),
                meta: {
                    isTab: false,
                    isAuth: false,
                    isoperate: true,
                }
            },
            {
                path: '/zsxt-jiaoyvdiaocha',
                name: '教育调查报告',
                component: () =>
                    import('@/page/zsxtreception/jiaoyvdiaocha'),
                meta: {
                    isTab: false,
                    isAuth: false,
                    isoperate: true,
                }
            },
            {
                path: '/zsxt-manage',
                name: '数据管理',
                component: () =>
                    import('@/views/wel/index'),
                meta: {
                    isTab: true,
                    isAuth: false,
                    isoperate: false,
                }
            },
            {
                path: '/system/download-task',
                name: '下载任务管理',
                component: () =>
                    import('@/views/system/downloadTask'),
                meta: {
                    isTab: true,
                    isAuth: false,
                    isoperate: false,
                }
            },
            // {
            //     path: 'dashboard',
            //     name: '控制台',
            //     meta: {
            //         i18n: 'dashboard',
            //         menu: false,
            //     },
            //     component: () =>
            //         import( /* webpackChunkName: "views" */ '@/views/wel/dashboard')
            // }
        ]
    },
    // {
    //     path: '/form-detail',
    //     component: Layout,
    //     children: [{
    //         path: 'index',
    //         name: '详情页',
    //         meta: {
    //             i18n: 'detail'
    //         },
    //         component: () =>
    //             import( /* webpackChunkName: "views" */ '@/views/util/form-detail')
    //     }]
    // },
    // {
    //     path: '/info',
    //     component: Layout,
    //     redirect: '/info/index',
    //     children: [{
    //         path: 'index',
    //         name: '个人信息',
    //         meta: {
    //             i18n: 'info'
    //         },
    //         component: () =>
    //             import( /* webpackChunkName: "views" */ '@/views/user/info')
    //     }, {
    //         path: 'setting',
    //         name: '个人设置',
    //         meta: {
    //             i18n: 'setting'
    //         },
    //         component: () =>
    //             import( /* webpackChunkName: "views" */ '@/views/user/setting')
    //     }, {
    //         path: 'password',
    //         name: '修改密码',
    //         meta: {
    //             i18n: 'password'
    //         },
    //         component: () =>
    //             import( /* webpackChunkName: "views" */ '@/views/user/password')
    //     }]
    // }
]
