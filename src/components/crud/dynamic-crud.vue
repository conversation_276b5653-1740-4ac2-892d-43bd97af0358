<template>
  <div class="carousel-wrapper">
    <div class="box-card-hd" >
      <i class="iconstyle el-icon-back" :class="navigationList.length > 1 ? '' : 'iconcolor'" @click="toNavigate(navigationList[navigationList.length - 2])"></i>
      <span>{{title}}</span>
    </div>
    <div class="box-card-bd" :style="{'padding': ShowSusheDetail ? '20px 3% 40px 3%' : '20px 8% 40px 8%'}">
      <p v-if="!ShowSusheDetail" class="data-position">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item v-for="(item,index) in navigationList" :key="index" @click.native="toNavigate(item)" class="point">
            {{item.index==0?item.contextData[item.drillParamList[0]]?item.contextData[item.drillParamList[0]]:item.drillParamList[0]:item.contextData[item.drillParamList[0]]}}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </p>
      <p v-if="ShowSusheDetail" class="data-position" style="position: absolute;left: 3%">
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-breadcrumb-item  class="point">
            {{crudParam.contextData.sslmc}}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </p>
      <div v-if="ShowSusheDetail" style="position: absolute;left: 40%">
        <div style="height: 10px;width: 15px;margin-top:15px;background-color: #00a5ec;display: inline-block;margin-left: 15px;margin-right: 5px;border-radius: 2px"></div><span style="font-size: 14px">已住满</span>
        <div style="height: 10px;width: 15px;margin-top:15px;background-color: #54BFA0;display: inline-block;margin-left: 15px;margin-right: 5px;border-radius: 2px"></div><span style="font-size: 14px">未住满</span>
        <div style="height: 10px;width: 15px;margin-top:15px;background-color: #E06655;display: inline-block;margin-left: 15px;margin-right: 5px;border-radius: 2px"></div><span style="font-size: 14px">空房间</span>
      </div>
<!--      <p v-if="ShowSusheDetail" class="data-position" style="position: absolute;right: 18%">-->
<!--        <el-breadcrumb separator-class="el-icon-arrow-right">-->
<!--          <el-breadcrumb-item  class="point">-->
<!--            公寓概况-->
<!--          </el-breadcrumb-item>-->
<!--        </el-breadcrumb>-->
<!--      </p>-->
      <div class="table-wrapper" v-if="ShowCrud">
        <div class="d-box" v-if="textShow">
          <!-- <span><i class="iconfont iconwenhao"></i></span> -->
          <p class="p-detail"><i class="iconfont iconwenhao"></i>
            {{this.loadParam.drillDesc}}
          </p>

				</div>
        <avue-crud v-if="data.length>0" :ref="'crud'+loadParam.drillSetCode" :table-loading="tableLoading"
                   :option="option" :data="data" :page.sync="page" @size-change="sizeChange" height="200"
                   @current-change="currentChange" @cell-click="handleRowClick" :cell-class-name="addClass"
                   @cell-mouse-enter="cellPoint" @sort-change="sortChange" @search-change="searchChange">
          <template slot="menuLeft">
            <el-button type="success"
                       size="mini"
                       icon="el-icon-download"
                       @click="handleExport">导出
            </el-button>
          </template>
        </avue-crud>

        <!-- 无数据时显示提示 -->
        <NoData
          v-else-if="!tableLoading && data.length === 0"
          text="暂无数据"
          tip="当前查询条件下没有找到相关数据"
          height="200px"
        />
      </div>
      <userDetail v-if="ShowUserDetail" :crud-param= "userParam"/>
      <susheDetail v-if="ShowSusheDetail" :crud-param="susheParam" :paramlist= "data"></susheDetail>
    </div>
  </div>


</template>

<script>
import {GenExportDrillData, getDirllData} from '@/api/bigscreen'
import userDetail from '@/views/report/bigscreen/viewer/user-detail.vue'
import susheDetail from '@/components/sushe/index'
import NoData from '@/components/common/NoData.vue'

export default {
  props: ['crudParam', 'queryParam', 'reportId'],
  components: {
    userDetail,
    susheDetail,
    NoData
  },
  data() {
    return {
      page: {
        tableLoading: false,
        currentPage: 1,
        total: 0,
        pageSize: 10,
        "background": true
      },
      data: [],
      loadParam:{},
      susheParam: {},
      initParam: JSON.parse(this.$route.query.param),
      navigationList:[],
      title:``,
      ShowCrud: true,
      ShowUserDetail: false,
      ShowSusheDetail: false,
      userParam:{},
      textShow: false,
    };
  },
  updated() {
    this.title = this.loadParam.drillSetName;
    this.firstTitle = `${this.loadParam.drillSetName}-${this.loadParam.contextData[this.loadParam.drillParamList[0]]}-数据下钻`;
    document.title = this.firstTitle;
  },
  watch: {
    crudParam(val) {
      console.log(`crudParam111`,val)
      this.loadParam = val;
      if(this.loadParam.drillDesc){
        this.textShow = true
      }else{
        this.textShow = false
      }
    },
    loadParam(val){
      if(val.drillParamList.length === 0){
        val.drillParamList[0] = "first"
        val.contextData.first = val.drillSetName
      }
      if (this.navigationList.length === 0) {
        if (val.drillParamType === '表头') {
          this.crudParam.contextData = {};
        }
        this.navigationList.push(this.crudParam);
      } else {
        let b = this.navigationList.every(function (n) {
          return val.setCode !== n.setCode;
        });
        if (b) {
          this.navigationList.push(val);
        }
      }
      if(val.drillSetTyp === '宿舍页面'){
        this.ShowSusheDetail = true;
        this.ShowCrud = false;
        this.page.pageSize = 99999999;
        this.onLoad(this.pageParam((val)))
      }else {
        this.onLoad(this.pageParam((val)))
      }

    }
  },
  mounted() {
  },
  computed: {
    option() {
      return {
        size: "medium",
        index: false,
        // align: "center",
        border: true,
        // searchBtn: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        filterBtn: false,
        refreshBtn: false,
        columnBtn: true,
        viewBtn: true,
        searchShow: false,
        menu: false,
		    stripe:true,
        dialogClickModal: false,
        column: []
      }
    }
  },
  methods: {
    onLoad(param) {
      this.tableLoading = true
      this.data = [];
      this.option.column = [];
      let columns = [];
      if (param.queryParam.drillFieldConfig) {
        let fieldConfig = JSON.parse(param.queryParam.drillFieldConfig);
        for (let i = 0; i < fieldConfig.length; i++) {
          columns.push({
            label: fieldConfig[i].field,
            prop: fieldConfig[i].field,
            minWidth: 200,
            sortable:fieldConfig[i].sortable === '是',
            search:fieldConfig[i].search === '是',
            hide:fieldConfig[i].hide === '是',
          })
        }
      }
      getDirllData(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        if (data.records && data.records.length > 0) {
          if (columns.length === 0) {
            let keys = Object.keys(data.records[0]);
            for (let i = 0; i < keys.length; i++) {
              columns.push({
                label: keys[i],
                prop: keys[i],
                minWidth: 200,
              })
            }
          }
          this.option.column = columns;
          this.userParam = this.loadParam
        }
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.susheParam = this.loadParam
        this.data = data.records;
        this.$forceUpdate()
        console.log(`this.data`,this.data)
      })
      // if (this.$refs['crud' + this.loadParam.drillSetCode]) {
      //   this.$nextTick(() => {
      //     this.$refs['crud' + this.loadParam.drillSetCode].refreshTable();
      //   })
      // }
    },
    searchChange(param, done) {
      console.log(`searchChange`,this.initParam);
      // this.loadParam.contextData = {...this.loadParam.contextData,...param};
      this.loadParam.contextData = {...this.initParam.contextData,...param};
      this.page.currentPage = 1;
      var pageParam = this.pageParam(this.loadParam)
      this.searchData(pageParam);
      done();
    },
    searchData(param) {
      this.tableLoading = true
      getDirllData(param).then(res => {
        this.tableLoading = false
        const data = res.data.info;
        this.page.currentPage = data.current;
        this.page.total = data.total;
        this.page.pageSize = data.size;
        this.data = data.records;
      })
    },
    sortChange(val){
      let sortable = this.loadParam.contextData.sortable;
      if (sortable == null) {
        sortable = {}
      }
      sortable[val.prop] = val.order;
      this.loadParam.contextData.sortable = sortable;
      this.searchData(this.pageParam(this.loadParam))
    },
    //给可以点击的列 添加样式
    addClass({row, column, rowIndex, columnIndex}) {
      let drillSetList = this.queryParam.drillSetList;
      let length = this.loadParam.index + 1;
      if (drillSetList.length > length) {
        let drillSet = drillSetList[length];
        // let key = drillSet.drillProperties[Object.keys(drillSet.drillProperties)[0]];
        let drillParamListElement = Object.keys(drillSet.drillProperties);
        let key = [];
        drillParamListElement.forEach(el=>{
          key = [...key, ...drillSet.drillProperties[el]];
        })
        if(!Array.isArray(key)){
          if (column.label === key) {
            return 'cell-point'
          }
        }else {
          if (key.join(",").indexOf(column.label) != -1) {
            return 'cell-point'
          }
        }

      }
    },
    //鼠标移入效果
    cellPoint(row, column, cell, event) {
      let drillSetList = this.queryParam.drillSetList;
      let length = this.loadParam.index + 1;
      if (drillSetList.length > length) {
        let drillSet = drillSetList[length];
        // let key = drillSet.drillProperties[drillSet.drillParamList[0]];
        let drillParamListElement = Object.keys(drillSet.drillProperties);
        let key = [];
        drillParamListElement.forEach(el=>{
          key = [...key, ...drillSet.drillProperties[el]];
        })
        if(!Array.isArray(key)){
          if (column.label === key) {
            cell.style.cursor = "pointer"
          }
        }else {
          if (key.join(",").indexOf(column.label) != -1) {
            return 'pointer'
          }
        }

      }
    },
    //	单击行运行的方法
    handleRowClick(row, column) {
      console.log(`row`,row)
      console.log(`column`,column)

      let drillSetList = this.queryParam.drillSetList;
      let length = this.loadParam.index + 1;
      console.log(`this.queryParam`,this.queryParam)
      console.log(`this.loadParam`,this.loadParam)
      console.log(`this.crudParam`,this.crudParam)

      if (drillSetList.length > length) {
        let drillSet = drillSetList[length];
        let fieldConfigStr = drillSet.fieldConfig;
        drillSet.drillReportid = this.crudParam.drillReportid
        console.log(`drillSet`,drillSet)
        if (drillSet.drillParamList.length > 0) {
          // let drillParamListElement = drillSet.drillParamList[0];
          let drillParamListElement = Object.keys(drillSet.drillProperties);
          let key = [];
          drillParamListElement.forEach(el=>{
            key = [...key, ...drillSet.drillProperties[el]];
          })
          console.log(`key`,key)
          // this.queryParam.contextData = {};  //清空下钻传参
          if(!Array.isArray(key)){
            if (column.label === key) {
              if (drillSet.drillParamType == '表头') {
                this.queryParam.contextData[drillParamListElement] = column.label;
              } else {
                this.queryParam.contextData[drillParamListElement] = row[key];
              }
              Object.keys(row).forEach(k=>{
                this.queryParam.contextData[k] = row[k];
              })
              drillSet.contextData = this.queryParam.contextData;
              drillSet.setCode = drillSet.drillSetCode;
              drillSet.reportid = this.reportId;
              if (drillSet.drillSetTyp === '数据表格') {
                this.restPageSize();
                this.loadParam = drillSet;
                console.log(`this.loadParam2`,this.loadParam)

                if (this.loadParam.drillDesc) {
                  this.textShow = true
                } else {
                  this.textShow = false
                }
              } else {
                this.loadParam = drillSetList[length - 1];
                this.userParam = drillSet;
                if (drillSet.drillSetTyp.indexOf(`大屏`) !== -1) {
                  let pathId = `/bigscreen/viewer?reportId=` + `${drillSet.reportid}`
                  let {href} = this.$router.resolve({
                    path: pathId,
                    query: {
                      param: JSON.stringify(drillSet)
                    }
                  })
                  window.open(href, '_blank')
                } else if (drillSet.drillSetTyp === '详情页面') {
                  this.loadParam = drillSet;
                  this.ShowCrud = false
                  this.ShowUserDetail = true
                } else if (drillSet.drillSetTyp === '宿舍页面') {
                  this.loadParam = drillSet;
                  this.ShowCrud = false
                  this.ShowUserDetail = false
                  this.ShowSusheDetail = true
                }
              }
            }
          }else {
            if (key.join(",").indexOf(column.label) != -1) {
              let paramKey = '';
              Object.keys(drillSet.drillProperties).forEach(elKey=>{
                if (drillSet.drillProperties[elKey].join(",").indexOf(column.label) != -1) {
                  paramKey = elKey;
                }
              })

              if (drillSet.drillParamType == '表头') {
                this.queryParam.contextData[paramKey] = column.label;
                Object.keys(row).forEach(k=>{
                  this.queryParam.contextData[k] = row[k];
                })
              } else {
                this.queryParam.contextData[paramKey] = row[key];
              }
              drillSet.contextData = this.queryParam.contextData;
              drillSet.setCode = drillSet.drillSetCode;
              drillSet.reportid = this.reportId;
              if (drillSet.drillSetTyp === '数据表格') {
                this.restPageSize();
                this.loadParam = drillSet;
                console.log(`this.loadParam3`,this.loadParam)

                if (this.loadParam.drillDesc) {
                  this.textShow = true
                } else {
                  this.textShow = false
                }
              } else {
                this.loadParam = drillSetList[length - 1];
                this.userParam = drillSet;
                if (drillSet.drillSetTyp.indexOf(`大屏`) !== -1) {
                  let pathId = `/bigscreen/viewer?reportId=` + `${drillSet.reportid}`
                  let {href} = this.$router.resolve({
                    path: pathId,
                    query: {
                      param: JSON.stringify(drillSet)
                    }
                  })
                  window.open(href, '_blank')
                } else if (drillSet.drillSetTyp === '详情页面') {
                  this.loadParam = drillSet;
                  this.ShowCrud = false
                  this.ShowUserDetail = true
                  this.ShowSusheDetail = false
                }else if (drillSet.drillSetTyp === '宿舍页面') {
                  this.loadParam = drillSet;
                  this.ShowCrud = false
                  this.ShowUserDetail = false
                  this.ShowSusheDetail = true
                }
              }
            }
          }

        }
      }
    },
    toNavigate(item){
      // 点击导航，把学生详情隐藏，下钻下钻展示
        if( item && item.index !== this.navigationList.length-1){
          this.ShowCrud = true
          this.ShowUserDetail = false
          this.restPageSize();
          if (item.index == 0) {
            item.contextData = this.initParam.contextData;
          }
          this.loadParam = item;
          if(this.loadParam.drillDesc){
            this.textShow = true
          }else {
            this.textShow = false
          }

          this.navigationList = this.navigationList.slice(0, item.index);
        }
    },
    sizeChange(val) {
      this.page.currentPage = 1;
      this.page.pageSize = val;
      this.searchData(this.pageParam(this.loadParam))
    },
    currentChange(val) {
      this.page.currentPage = val;
      this.searchData(this.pageParam(this.loadParam))
    },
    restPageSize(){
      this.page.currentPage = 1;
      this.page.pageSize = 10;
    },
    pageParam(queryParam) {
      return {
        page: this.page.currentPage,
        pageSize: this.page.pageSize,
        queryParam: queryParam
      }
    },
    handleExport() {
      var pageParam = this.pageParam(this.loadParam)
      GenExportDrillData(pageParam).then((res) => {
        if (res.data.code == "00000") {
          window.open(`/report/dashboard/exportDirllData`);
        }
      });

    },
  }
};
</script>

<style scoped lang="scss">
// .d-box span {
// 	float: left;
// 	display: inline-block;
// 	height:14px;
// 	width:22px;
// }
// .d-box>span>i{
//   line-height: 14px;
// 	color: #999;
// 	font-size: 14px;
// }
.iconcolor{
  color: #8a979e;
  cursor: default !important;
}
.iconstyle{
  cursor: pointer;
  margin-right: 10px;
  margin-top: 2px;
}
.p-detail {
		//float: left;
    text-align: left;
		font-size: 14px;
		color: #999;
		margin-top: 0;
	}
  .d-box>p>i{
    font-size: 16px;
    margin-right: 5px;
  }
.el-icon-arrow-right {
		color: #00bfeb
	}

::v-deep .point {
		color: #00bfeb
	}

	.point:hover {
		cursor: pointer;
	}

	.carousel-wrapper {
		width: 100%;
		height: 100%;
		background-color: #eee;
		background-repeat: no-repeat;
		background-size: cover;
	}

	.box-card-hd {
		padding: 0 0 0 24px;
		height: 60px;
		background-color: #224BAA;
		box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
		text-align: left;
		color: #fff;
		font-size: 24px;
    line-height: 56px;
		font-weight: bold;
	}

	.box-card-bd {
    position: relative;
		margin: auto;
		padding: 20px 8% 40px 8%;
		max-height: calc(100vh - 60px);
		overflow-y: auto;
	}

	.table-wrapper {
		padding: 25px 20px 20px 20px;
		padding-bottom: 10px;
		background-color: #fff;
		border-radius: 10px;
		overflow: hidden;
	}

	//v::deep .avue-crud__menu {
	//	display: none !important;
	//}

::v-deep .avue-crud__pagination {
		padding: 15px 0 10px 20px;
	}

::v-deep .el-table .cell {
		white-space: nowrap;
	}

	.table-wrapper ::v-deep .el-table,
	.el-table__expanded-cell {
		background-color: transparent;
		font-size: 16px;
	}

::v-deep .avue-crud .el-table tr {
		background-color: transparent !important;
		color: #333333c7
	}

::v-deep .avue-crud .el-table th {
		background-color: #141d3723;
		color: #000000c7;
		padding: 0.75em 20px !important;
		border-bottom: 0px solid #EBEEF52B;
	}

::v-deep .avue-crud .el-table--medium td,
::v-deep .avue-form .el-table--medium td {
		padding: 0.7em 20px !important;
	}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
		background-color: #191c260d;
		border-bottom: 0;
		border-top: 0;
	}

::v-deep .el-table td {
		border-bottom: 0px solid #EBEEF52B;
	}

::v-deep .el-table tbody tr:hover>td {
		background-color: #82bfff43 !important
	}

	.data-position {
		padding: 10px 10px 20px 0px;
		margin: 0px;
	}

	.data-position .el-breadcrumb {
		font-size: 16px;
		padding-left: 15px;
		border-left: 5px solid #255bc6;
	}

::v-deep .el-breadcrumb__inner {
		color: #255bc6;
	}

	//v::deep .el-table_2_column_3 span {
	// 	color: #255bc6;
	// 	text-decoration: underline;
	// 	cursor: pointer;
	// }

::v-deep .cell-point{
    color: #255bc6;
		text-decoration: underline;
		cursor: pointer;
  }
</style>

