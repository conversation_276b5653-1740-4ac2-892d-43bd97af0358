<template>
  <div class="demo-container">
    <h2>NoData 组件演示</h2>
    
    <div class="demo-section">
      <h3>1. 基本用法</h3>
      <NoData />
    </div>
    
    <div class="demo-section">
      <h3>2. 自定义文本和提示</h3>
      <NoData 
        text="暂无统计数据" 
        tip="请调整查询条件后重新查询"
      />
    </div>
    
    <div class="demo-section">
      <h3>3. 自定义图标</h3>
      <NoData 
        text="暂无图表数据" 
        tip="请选择时间范围后查询"
        icon-class="el-icon-pie-chart"
      />
    </div>
    
    <div class="demo-section">
      <h3>4. 自定义高度</h3>
      <NoData 
        text="暂无数据" 
        height="200px"
      />
    </div>
  </div>
</template>

<script>
import NoData from './NoData.vue'

export default {
  name: 'NoDataDemo',
  components: {
    NoData
  }
}
</script>

<style scoped>
.demo-container {
  padding: 20px;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section h3 {
  margin-bottom: 10px;
  color: #333;
}
</style>
