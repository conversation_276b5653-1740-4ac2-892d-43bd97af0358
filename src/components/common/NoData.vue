<template>
  <div class="no-data-container">
    <div class="no-data-content">
      <i :class="iconClass" class="no-data-icon"></i>
      <p class="no-data-text">{{ text }}</p>
      <p v-if="tip" class="no-data-tip">{{ tip }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'NoData',
  props: {
    // 显示的文本
    text: {
      type: String,
      default: '暂无数据'
    },
    // 提示文本
    tip: {
      type: String,
      default: ''
    },
    // 图标类名
    iconClass: {
      type: String,
      default: 'el-icon-document'
    },
    // 容器高度
    height: {
      type: String,
      default: '400px'
    }
  }
}
</script>

<style scoped>
.no-data-container {
  width: 100%;
  height: v-bind(height);
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
}

.no-data-content {
  text-align: center;
  color: #909399;
}

.no-data-icon {
  font-size: 64px;
  color: #C0C4CC;
  margin-bottom: 16px;
}

.no-data-text {
  font-size: 16px;
  color: #606266;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.no-data-tip {
  font-size: 14px;
  color: #909399;
  margin: 0;
}
</style>
