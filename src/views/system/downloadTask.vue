<template>
  <basic-container class="download-task-container">
    <!-- 任务统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 5px;">
      <el-col :span="6">
        <el-card class="stats-card pending">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ taskStats.pending || 0 }}</div>
              <div class="stats-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card processing">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-loading"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ taskStats.processing || 0 }}</div>
              <div class="stats-label">处理中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card completed">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-check"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ taskStats.completed || 0 }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card failed">
          <div class="stats-content">
            <div class="stats-icon">
              <i class="el-icon-close"></i>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ taskStats.failed || 0 }}</div>
              <div class="stats-label">失败</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务列表 -->
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      :page="page"
      v-model="form"
      ref="crud"
      class="download-task-table"
      @search-change="searchChange"
      @search-reset="searchReset"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refresh">

      <template slot="menuLeft">
        <el-button
          type="danger"
          size="mini"
          icon="el-icon-delete"
          @click="clearCompletedTasks"
          :disabled="taskStats.completed === 0">
          清理已完成任务
        </el-button>
        <el-button
          type="warning"
          size="mini"
          icon="el-icon-refresh"
          @click="retryAllFailedTasks"
          :disabled="taskStats.failed === 0">
          重试所有失败任务
        </el-button>
      </template>

      <template slot="taskType" slot-scope="scope">
        <el-tag :type="getTaskTypeColor(scope.row.taskType)">
          {{ getTaskTypeText(scope.row.taskType) }}
        </el-tag>
      </template>

      <template slot="taskStatus" slot-scope="scope">
        <el-tag :type="getTaskStatusType(scope.row.taskStatus)">
          {{ getTaskStatusText(scope.row.taskStatus) }}
        </el-tag>
      </template>

      <template slot="progress" slot-scope="scope">
        <div class="progress-container">
          <el-progress
            :percentage="scope.row.progress || 0"
            :status="getProgressStatus(scope.row.taskStatus)"
            :stroke-width="10">
          </el-progress>
          <span class="progress-text">{{ scope.row.progress || 0 }}%</span>
        </div>
      </template>

      <template slot="fileSize" slot-scope="scope">
        <span>{{ formatFileSize(scope.row.fileSize) }}</span>
      </template>

      <template slot="expireTime" slot-scope="scope">
        <span :class="{'expire-warning': isExpiringSoon(scope.row.expireTime)}">
          {{ scope.row.expireTime }}
          <el-tag v-if="isExpiringSoon(scope.row.expireTime)" type="warning" size="mini">
            即将过期
          </el-tag>
        </span>
      </template>

      <template slot="menu" slot-scope="scope">
        <el-button
          v-if="scope.row.taskStatus === '2'"
          type="text"
          size="mini"
          icon="el-icon-download"
          @click="downloadFile(scope.row)">
          下载
        </el-button>
        <el-button
          v-if="scope.row.taskStatus === '3'"
          type="text"
          size="mini"
          icon="el-icon-refresh"
          @click="retryTask(scope.row.id)">
          重试
        </el-button>
        <el-button
          v-if="scope.row.taskStatus === '0' || scope.row.taskStatus === '1'"
          type="text"
          size="mini"
          icon="el-icon-close"
          @click="cancelTask(scope.row.id)">
          取消
        </el-button>
        <el-button
          type="text"
          size="mini"
          icon="el-icon-view"
          @click="viewTaskDetail(scope.row)">
          详情
        </el-button>
        <el-button
          v-if="scope.row.taskStatus === '2' || scope.row.taskStatus === '3' || scope.row.taskStatus === '4'"
          type="text"
          size="mini"
          icon="el-icon-delete"
          @click="removeTask(scope.row.id)">
          删除
        </el-button>
      </template>
    </avue-crud>

    <!-- 任务详情对话框 -->
    <el-dialog
      title="任务详情"
      :visible.sync="detailDialogVisible"
      :modal="false"
      width="60%">
      <div v-if="currentTask">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务名称">
            {{ currentTask.taskName }}
          </el-descriptions-item>
          <el-descriptions-item label="任务类型">
            <el-tag :type="getTaskTypeColor(currentTask.taskType)">
              {{ getTaskTypeText(currentTask.taskType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="任务状态">
            <el-tag :type="getTaskStatusType(currentTask.taskStatus)">
              {{ getTaskStatusText(currentTask.taskStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="执行进度">
            <el-progress
              :percentage="currentTask.progress || 0"
              :status="getProgressStatus(currentTask.taskStatus)">
            </el-progress>
          </el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">
            {{ currentTask.taskDesc }}
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ currentTask.createTime }}
          </el-descriptions-item>
          <el-descriptions-item label="完成时间">
            {{ currentTask.finishTime || '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="过期时间">
            <span :class="{'expire-warning': isExpiringSoon(currentTask.expireTime)}">
              {{ currentTask.expireTime }}
            </span>
          </el-descriptions-item>
          <el-descriptions-item label="创建用户">
            {{ currentTask.createUserName }}
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
        <el-button
          v-if="currentTask && currentTask.taskStatus === '2'"
          type="primary"
          icon="el-icon-download"
          @click="downloadFile(currentTask)">
          下载文件
        </el-button>
      </span>
    </el-dialog>

  </basic-container>
</template>

<script>
import {
  cancelTask,
  downloadTaskFile,
  getTaskList,
  getTaskStatistics,
  removeTask,
  retryTask
} from "@/api/system/downloadTask";
import {mapGetters} from "vuex";
import FileSaver from 'file-saver';

export default {
  name: "DownloadTask",
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 20,
        currentPage: 1,
        total: 0
      },
      data: [],
      taskStats: {},
      detailDialogVisible: false,
      currentTask: null,
      refreshTimer: null,
      option: {
        height: 'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        refreshBtn: true,
        columnBtn: true,
        menuWidth: 250,
        column: [
          {
            label: "任务名称",
            prop: "taskName",
            search: true,
            width: 200
          },
          {
            label: "任务类型",
            prop: "taskType",
            slot: true,
            search: true,
            type: "select",
            dicData: [
              { label: "三表导出", value: "three_table_export" },
              { label: "Excel导出", value: "excel_export" },
              { label: "文件下载", value: "file_download" }
            ],
            width: 120
          },
          {
            label: "状态",
            prop: "taskStatus",
            slot: true,
            search: true,
            type: "select",
            dicData: [
              { label: "待处理", value: "0" },
              { label: "处理中", value: "1" },
              { label: "已完成", value: "2" },
              { label: "失败", value: "3" },
              { label: "已过期", value: "4" }
            ],
            width: 100
          },
          {
            label: "进度",
            prop: "progress",
            slot: true,
            width: 180
          },
          /*{
            label: "任务描述",
            prop: "taskDesc",
            width: 200,
            overHidden: true
          },*/
          {
            label: "文件名",
            prop: "fileName",
            width: 200,
            overHidden: true
          },
          {
            label: "文件大小",
            prop: "fileSize",
            slot: true,
            width: 100
          },
          {
            label: "创建时间",
            prop: "createTime",
            search: true,
            type: "daterange",
            width: 160
          },
          {
            label: "完成时间",
            prop: "finishTime",
            width: 160
          },
          {
            label: "过期时间",
            prop: "expireTime",
            slot: true,
            width: 160
          },
          {
            label: "创建用户",
            prop: "createUserName",
            search: true,
            width: 120
          }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["permission"])
  },
  created() {
    this.onLoad(this.page);
    this.loadTaskStats();
    this.startAutoRefresh();
  },
  mounted() {
    this.$nextTick(() => {
      this.calcTableHeight();
    });
    window.addEventListener('resize', this.calcTableHeight);
  },
  beforeDestroy() {
    this.stopAutoRefresh();
    window.removeEventListener('resize', this.calcTableHeight);
  },
  methods: {
    onLoad(page, params = {}) {
      this.loading = true;
      getTaskList(page.currentPage, page.pageSize, Object.assign(params, this.query))
        .then(res => {
          // 解析axios响应对象，访问res.data获取业务数据
          const responseData = res.data || res;

          if (responseData.success) {
            this.data = responseData.data.records;
            this.page.total = responseData.data.total;
          } else {
            this.$message.error(responseData.msg || '获取任务列表失败');
          }
          this.loading = false;
        })
        .catch(error => {
          console.error('获取任务列表异常:', error);
          this.$message.error('获取任务列表失败: ' + (error.message || error));
          this.loading = false;
        });
    },

    loadTaskStats() {
      getTaskStatistics().then(res => {
        // 解析axios响应对象，访问res.data获取业务数据
        const responseData = res.data || res;

        if (responseData.success) {
          this.taskStats = responseData.data;
        } else {
          this.$message.error(responseData.msg || '获取任务统计失败');
        }
      }).catch(error => {
        console.error('获取任务统计异常:', error);
        this.$message.error('获取任务统计失败: ' + (error.message || error));
      });
    },

    refresh() {
      this.onLoad(this.page);
      this.loadTaskStats();
      // 通知顶部图标更新任务数量
      this.$EventBus.$emit('refreshDownloadTaskCount');
    },

    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },

    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },

    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.onLoad(this.page);
    },

    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page);
    },

    // 获取任务类型颜色
    getTaskTypeColor(taskType) {
      const typeMap = {
        'three_table_export': 'primary',
        'excel_export': 'success',
        'file_download': 'info'
      };
      return typeMap[taskType] || 'info';
    },

    // 获取任务类型文本
    getTaskTypeText(taskType) {
      const typeMap = {
        'three_table_export': '三表导出',
        'excel_export': 'Excel导出',
        'file_download': '文件下载'
      };
      return typeMap[taskType] || taskType;
    },

    // 获取任务状态类型
    getTaskStatusType(status) {
      const statusMap = {
        '0': 'info',
        '1': 'warning',
        '2': 'success',
        '3': 'danger',
        '4': 'info'
      };
      return statusMap[status] || 'info';
    },

    // 获取任务状态文本
    getTaskStatusText(status) {
      const statusMap = {
        '0': '待处理',
        '1': '处理中',
        '2': '已完成',
        '3': '失败',
        '4': '已过期'
      };
      return statusMap[status] || '未知';
    },

    // 获取进度条状态
    getProgressStatus(taskStatus) {
      if (taskStatus === '3') return 'exception';
      if (taskStatus === '2') return 'success';
      return null;
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes) return '-';
      const kb = bytes / 1024;
      if (kb < 1024) return kb.toFixed(2) + ' KB';
      const mb = kb / 1024;
      if (mb < 1024) return mb.toFixed(2) + ' MB';
      const gb = mb / 1024;
      return gb.toFixed(2) + ' GB';
    },

    // 检查是否即将过期
    isExpiringSoon(expireTime) {
      if (!expireTime) return false;
      const expireDate = new Date(expireTime);
      const now = new Date();
      const diffHours = (expireDate - now) / (1000 * 60 * 60);
      return diffHours > 0 && diffHours <= 24;
    },

    // 下载文件
    downloadFile(task) {
      downloadTaskFile(task.id).then(response => {
        const blob = new Blob([response], { type: 'application/octet-stream' });
        FileSaver.saveAs(blob, task.fileName);
        this.$message.success('文件下载开始');
      }).catch(error => {
        this.$message.error('下载失败: ' + (error.message || error));
      });
    },

    // 重试任务
    retryTask(taskId) {
      this.$confirm('确定要重试此任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        retryTask(taskId).then(res => {
          if (res.data.success) {
            this.$message.success('任务重试成功');
            this.refresh();
          } else {
            this.$message.error(res.msg || '重试失败');
          }
        });
      });
    },

    // 取消任务
    cancelTask(taskId) {
      this.$confirm('确定要取消此任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        cancelTask(taskId).then(res => {
          if (res.data.success) {
            this.$message.success('任务取消成功');
            this.refresh();
          } else {
            this.$message.error(res.msg || '取消失败');
          }
        });
      });
    },

    // 删除任务
    removeTask(taskId) {
      this.$confirm('确定要删除此任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        removeTask(taskId).then(res => {
          if (res.data.success) {
            this.$message.success('任务删除成功');
            this.refresh();
          } else {
            this.$message.error(res.msg || '删除失败');
          }
        });
      });
    },

    // 查看任务详情
    viewTaskDetail(task) {
      this.currentTask = task;
      this.detailDialogVisible = true;
    },

    // 清理已完成任务
    clearCompletedTasks() {
      this.$confirm('确定要清理所有已完成的任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const completedTasks = this.data.filter(task => task.taskStatus === '2');
        const promises = completedTasks.map(task => removeTask(task.id));

        Promise.all(promises).then(() => {
          this.$message.success('已完成任务清理成功');
          this.refresh();
        }).catch(error => {
          this.$message.error('清理失败: ' + error.message);
        });
      });
    },

    // 重试所有失败任务
    retryAllFailedTasks() {
      this.$confirm('确定要重试所有失败的任务吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const failedTasks = this.data.filter(task => task.taskStatus === '3');
        const promises = failedTasks.map(task => retryTask(task.id));

        Promise.all(promises).then(() => {
          this.$message.success('失败任务重试成功');
          this.refresh();
        }).catch(error => {
          this.$message.error('重试失败: ' + error.message);
        });
      });
    },

    // 开始自动刷新
    startAutoRefresh() {
      this.refreshTimer = setInterval(() => {
        if (this.taskStats.pending > 0 || this.taskStats.processing > 0) {
          this.refresh();
        }
      }, 10000);
    },

    // 停止自动刷新
    stopAutoRefresh() {
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    }
  }
};
</script>

<style scoped>
/* 统计卡片样式 */
.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 1px 30px;
}

.stats-icon {
  font-size: 24px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 20px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 2px;
}

.stats-label {
  font-size: 13px;
  color: #666;
  font-weight: 500;
}

/* 不同状态的颜色 */
.stats-card.pending .stats-icon {
  background: #e6f7ff;
  color: #1890ff;
}

.stats-card.pending .stats-number {
  color: #1890ff;
}

.stats-card.processing .stats-icon {
  background: #fff7e6;
  color: #fa8c16;
}

.stats-card.processing .stats-number {
  color: #fa8c16;
}

.stats-card.completed .stats-icon {
  background: #f6ffed;
  color: #52c41a;
}

.stats-card.completed .stats-number {
  color: #52c41a;
}

.stats-card.failed .stats-icon {
  background: #fff2f0;
  color: #ff4d4f;
}

.stats-card.failed .stats-number {
  color: #ff4d4f;
}

/* 进度条容器 */
.progress-container {
  display: flex;
  align-items: center;
}

.progress-container .el-progress {
  flex: 1;
  margin-right: 10px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  width: 40px;
  text-align: right;
}

/* 过期警告 */
.expire-warning {
  color: #fa8c16;
}
</style>
