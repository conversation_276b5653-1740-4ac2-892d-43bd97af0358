<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @refresh-change="refresh"
        >
            <template slot-scope="{row}" slot="menu">
                <el-button type="text"
                           icon="el-icon-edit"
                           size="small"
                           @click="handleEdit(row)"
                >编 辑
                </el-button>
                <el-button type="text"
                           icon="el-icon-edit"
                           size="small"
                           v-if="isPrintTzsEnabled"
                           @click="handlePreview(row)"
                >预 览
                </el-button>
                <el-button type="text"
                           icon="el-icon-edit"
                           size="small"
                           v-if="isPrintTzsEnabled"
                           @click="handlePrint(row)"
                >打 印
                </el-button>
            </template>
            <template slot="menuLeft">
                <el-button type="primary"
                           size="mini"
                           icon="el-icon-edit"
                           @click="handleCustomColumn">自定义列
                </el-button>
                <el-button type="success"
                           size="mini"
                           icon="el-icon-download"
                           @click="handleExport">导出
                </el-button>
                <el-button
                    size="mini"
                    @click="toxzzp">下载照片
                </el-button>
                <!--        <el-button type="success"
                                   size="mini"
                                   icon="el-icon-upload2"
                                   @click="handleImport">导入照片
                        </el-button>-->
                <el-button type="danger"
                           size="small"
                           plain
                           v-if="permission.tdd_delete"
                           @click="handleDelete">批量删除
                </el-button>
                <el-button
                    size="mini"
                    v-if="permission.gen_notification_number"
                    @click="handleGenerateNotNum">生成通知书号
                </el-button>
                <el-button type="danger"
                           size="small"
                           plain
                           v-if="permission.clear_notification_number"
                           @click="handleClearNotNum">清空通知书号
                </el-button>
                <el-button type="success"
                           size="mini"
                           icon="el-icon-download"
                           v-if="isPrintTzsEnabled"
                           @click="handleDownPrintClient">下载打印客户端
                </el-button>
                <el-button
                    :disabled="loading"
                    size="mini"
                    v-if="isPrintTzsEnabled"
                    @click="handlePrint">打印通知书
                </el-button>
                <el-button type="danger"
                           size="small"
                           plain
                           @click="handlePausePrint"
                            v-if="isShowPrintBtn">{{printBtnText}}
                </el-button>
<!--                <el-button
                    :disabled="loading"
                    size="mini"
                    @click="handlePrintStatus">获取打印状态
                </el-button>-->
                <div>
                    <transition name="fade">
                        <el-progress v-if="isShowPrintProgress" :percentage="totalProgress" :stroke-width="10"></el-progress>
                    </transition>
                </div>
            </template>
            <template v-for="z in slotname" :slot="z" slot-scope="scope">
                <div class="slotcontent" v-for="(e,a) in scope.row[z]" :key="a">
                    <div class="slotbody" v-for="(item,index) in e" :key="index">
                        <div style="border-bottom: 1px solid #EBEEF5;background-color: #fafafa;padding: 5px">
                            <p style="width: 120px">{{ item.options.label }}</p>
                        </div>
                        <div style="padding: 5px">
                            <p style="width: 120px">{{ item.values }}</p>
                        </div>
                    </div>
                </div>
            </template>
        </avue-crud>
        <el-dialog title="下载照片"
                   append-to-body
                   :visible.sync="xzzpshow"
                   width="1200px"
                   :close-on-click-modal="false">
            <el-form ref="form" label-width="120px">
<!--                <el-form-item label="下载范围">
                    <div v-if="selectionList.length > 0" style="color: #409EFF; font-size: 14px;">
                        <i class="el-icon-check"></i> 将下载已勾选的 {{ selectionList.length }} 个考生的照片
                    </div>
                    <div v-else style="color: #67C23A; font-size: 14px;">
                        <i class="el-icon-search"></i> 将按当前查询条件下载，预计 {{ page.total || 0 }} 个考生
                        <el-tooltip content="基于当前页面的搜索条件和筛选结果" placement="top">
                            <i class="el-icon-info" style="margin-left: 5px; cursor: help;"></i>
                        </el-tooltip>
                    </div>
                </el-form-item>-->
                <el-form-item label="下载类型">
                    <el-checkbox-group v-model="xzzptype">
                        <el-checkbox label="录取照片" name="zsxt_lqzp"></el-checkbox>
                        <el-checkbox label="核验照片" name="zsxt_hyzp"></el-checkbox>
                        <el-checkbox label="学籍照片" name="zsxt_xjzp"></el-checkbox>
                        <el-checkbox label="核验身份证照片" name="zsxt_hysfzzp"></el-checkbox>
                        <el-checkbox label="核验身份证头像照片" name="zsxt_hysfztxzp"></el-checkbox>
                        <el-checkbox label="电子签名照片" name="zsxt_dzqmzp"></el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="文件命名规则">
                    <el-select v-model="selectedNamingRule" placeholder="请选择命名规则" style="width: 300px;" @change="onNamingRuleChange">
                        <el-option
                            v-for="rule in namingRules"
                            :key="rule.code"
                            :label="rule.name"
                            :value="rule.code">
                        </el-option>
                    </el-select>
                    <el-button type="text" @click="showPlaceholderHelp = true" style="margin-left: 10px;">
                        <i class="el-icon-question"></i> 占位符说明
                    </el-button>
                </el-form-item>
                <el-form-item label="自定义模板" v-if="selectedNamingRule === 'custom'">
                    <el-input v-model="customTemplate" placeholder="例如: {ksh}_{xm}_{zymc}" style="width: 400px;"></el-input>
                    <div style="font-size: 12px; color: #999; margin-top: 5px;">
                        使用 {占位符} 格式，如 {ksh} 表示考生号，{xm} 表示姓名
                    </div>
                </el-form-item>
                <el-form-item label="预览示例" v-if="selectedNamingRule">
                    <div style="color: #409EFF; font-size: 14px;">
                        {{ namingPreview }}
                    </div>
                </el-form-item>
            </el-form>
            <div class="dialog-footer" style="margin-top: 20px;text-align: right;">
                <el-button type="primary" @click="xzzpshow = false;">取 消</el-button>
                <el-button type="primary" @click="submitxzzp">确 定</el-button>
            </div>
        </el-dialog>

        <!-- 占位符帮助对话框 -->
        <el-dialog title="占位符说明"
                   :visible.sync="showPlaceholderHelp"
                   width="600px"
                   append-to-body>
            <el-table :data="placeholderList" border>
                <el-table-column prop="placeholder" label="占位符" width="150">
                    <template slot-scope="scope">
                        <code>{{ '{' + scope.row.placeholder + '}' }}</code>
                    </template>
                </el-table-column>
                <el-table-column prop="description" label="说明"></el-table-column>
            </el-table>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showPlaceholderHelp = false">关 闭</el-button>
            </div>
        </el-dialog>

        <el-dialog title="自定义列" :visible.sync="customColumnOpen" width="40%" append-to-body
                   :destroy-on-close="true">
            <CustomColumnTransfer @close="closeCustomColumnTransfer" :selected-column-param="selectedColumnParam"
                                  :key="timer">
            </CustomColumnTransfer>
        </el-dialog>
        <el-dialog title="照片导入"
                   append-to-body
                   :visible.sync="excelBox"
                   width="600px"
                   :close-on-click-modal="false">
            <avue-form :option="excelOption"
                       v-model="excelForm"
                       :upload-after="uploadAfter">
            </avue-form>
        </el-dialog>
        <el-dialog title="基本信息"
                   append-to-body
                   :visible.sync="jbxxShow"
                   width="1200px"
                   :close-on-click-modal="false">
            <el-tabs v-model="activeName" v-if="jbxxShow" @tab-click="handleClick">
                <el-tab-pane label="个人信息" v-if="jbxxShow" name="first">
                    <template v-if="jbxxShow">
                        <el-descriptions class="descriptionsbox" :colon="(item.sfdt == '是') ? false : true"
                                         v-for="item in fieldlist" :key="item.id" :title="item.name">
                            <template v-if="item.sfdt == '是'">
                                <el-descriptions-item>
                                    <crudtable :dictFieldList="item.dictFieldList" :url="item.dturl"
                                               :param="jixxrow"></crudtable>
                                </el-descriptions-item>
                            </template>
                            <template v-else>
                                <el-descriptions-item v-for="field in item.dictFieldList" :key="field.id"
                                                      :label="field.fieldzh">
                                    <span v-if="!bjidlist.includes(field.id)">{{ jixxrow[field.fielden] }}</span>
                                    <fieldtype v-if="bjidlist.includes(field.id)" :fieldType="field.fieldType"
                                               :fieldValue="jixxrow[field.fielden]" :field="field"
                                               @fieldbcxx="fieldbcxx"></fieldtype>
                                    <el-button v-if="!bjidlist.includes(field.id) && field.sfbj == '是'" type="text"
                                               @click="bjxx(field)">编辑
                                    </el-button>
                                </el-descriptions-item>
                            </template>
                        </el-descriptions>
                    </template>
                </el-tab-pane>
                <el-tab-pane label="照片" name="second">
                    <div style="display: flex;">
                        <div class="zhaopianbox">
                            <p>
                                录取照片
                            </p>
                            <div>
                                <el-image style="width: 90px;" :src="`${baseurl}/file/view/zsxt_photo_${tmform.ksh}`">
                                    <div slot="error" class="image-slot">
                                        <img style="width: 90px;" src="../../assets/imgs/zanwuzhp.png">
                                    </div>
                                </el-image>
                            </div>
                            <span style="cursor: pointer;color: #00a5ec;"
                                  @click="opendialog('daoruzhaopian','导入照片','zsxt_photo')">替换</span>
                        </div>
                        <div class="zhaopianbox">
                            <p>
                                核验照片
                            </p>
                            <div>
                                <el-image style="width: 90px;" :src="`${baseurl}/file/view/zsxt_photo_${tmform.ksh}`">
                                    <div slot="error" class="image-slot">
                                        <img style="width: 90px;" src="../../assets/imgs/zanwuzhp.png">
                                    </div>
                                </el-image>
                            </div>
                        </div>
                        <div class="zhaopianbox">
                            <p>
                                学籍照片
                            </p>
                            <div>
                                <el-image style="width: 90px;" :src="`${baseurl}/file/view/zsxt_xjxx_${tmform.ksh}`">
                                    <div slot="error" class="image-slot">
                                        <img style="width: 90px;" src="../../assets/imgs/zanwuzhp.png">
                                    </div>
                                </el-image>
                            </div>
                            <span style="cursor: pointer;color: #00a5ec;"
                                  @click="opendialog('daoruzhaopian','导入照片','zsxt_xjzp')">替换</span>
                        </div>
                        <div class="zhaopianbox">
                            <p>
                                核验身份证照片
                            </p>
                            <div>
                                <el-image style="width: 90px;" :src="`${baseurl}/file/view/zsxt_sfz_${tmform.ksh}`">
                                    <div slot="error" class="image-slot">
                                        <img style="width: 90px;" src="../../assets/imgs/zanwuzhp.png">
                                    </div>
                                </el-image>
                            </div>
                        </div>
                        <div class="zhaopianbox">
                            <p>
                                核验身份证头像照片
                            </p>
                            <div>
                                <el-image style="width: 90px;"
                                          :src="`${baseurl}/file/view/zsxt_hysfztx_${tmform.ksh}`">
                                    <div slot="error" class="image-slot">
                                        <img style="width: 90px;" src="../../assets/imgs/zanwuzhp.png">
                                    </div>
                                </el-image>
                            </div>
                        </div>
                        <div class="zhaopianbox">
                            <p>
                                电子签名
                            </p>
                            <div>
                                <el-image style="width: 90px;" :src="`${baseurl}/file/view/zsxt_dzqm_${tmform.ksh}`">
                                    <div slot="error" class="image-slot">
                                        <img style="width: 90px;" src="../../assets/imgs/zanwuzhp.png">
                                    </div>
                                </el-image>
                            </div>
                        </div>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </el-dialog>
        <el-dialog
            :title="dialogtitle"
            :visible.sync="dialogVisiblesb"
            :direction="direction"
            append-to-body
            :before-close="handleClosesb">
            <div style="padding: 0 20px;">
                <daoruzhaopian @onclose="onclose" v-if="opendialogtype.daoruzhaopian" :type="zptype"></daoruzhaopian>
            </div>
        </el-dialog>
        <el-dialog
            title="通知书预览"
            :visible.sync="tzsShow"
            width="67%"
            append-to-body>
            <div v-html="this.tzsdata" style="padding: 0;">
            </div>
        </el-dialog>
        <el-dialog
            title="打印客户端下载"
            :visible.sync="printClientShow"
            width="60%"
            append-to-body>
            <el-row :gutter="12">
                <el-col v-for="(item,index) in printClientList" :span="8" @click.native="printClientDownload(item)">
                    <el-card shadow="hover" >
                        {{item.name}}
                    </el-card>
                </el-col>
            </el-row>
        </el-dialog>

    </basic-container>
</template>

<script>
import {
  add,
  allAndField,
  exportData,
  exportPhotos,
  getDetail,
  getList,
  getone,
  preview,
  remove,
  update,
  getNamingRules,
  getAvailablePlaceholders
} from "@/api/ksgl/tdd";
import {mapGetters} from "vuex";
import {getAllCustomColData} from "@/api/customColumn";
import {deepClone} from "@/util/util";
import CustomColumnTransfer from "@/components/CustomColumnTransfer.vue";
import fieldtype from "@/components/fieldtype";
import daoruzhaopian from "@/page/zsxtreception/improtmodul/daoruzhaopian";
import crudtable from "@/components/crudtable";
import {clearNotificationNumber, generateNotificationNumber, getAll, getPrintData,} from "@/api/zsxt/lqtzsrules";
import {GetListDict} from "@/api/sysDict";
import {GetSysParamNoLogin} from "@/api/sysParam";

export default {
    components: {
        CustomColumnTransfer,
        fieldtype,
        daoruzhaopian,
        crudtable
    },
    data() {
        return {
            form: {},
            defaultImage: '../../assets/imgs/zanwuzhp.png',
            input: '',
            query: {},
            param: {},
            tmform: {},
            zptype: '',
            dialogtitle: '',
            dialogtype: '',
            tableshow: false,
            xzzpshow: false,
            tzsShow: false,
            tzsdata: null,
            xzzptype: [],
            selectedNamingRule: 'ksh_xm_zymc', // 默认命名规则
            customTemplate: '', // 自定义模板
            namingRules: [], // 命名规则列表
            placeholderList: [], // 占位符列表
            showPlaceholderHelp: false, // 显示占位符帮助
            dialogVisiblesb: false,
            opendialogtype: {
                daoruzhaopian: false,
                daorusanbiao: false,
                photoimprot: false,
            },
            direction: 'rtl',
            baseurl: this.baseUrl,
            activeName: 'first',
            loading: true,
            jbxxShow: false,
            jixxrow: {},
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            fieldlist: null,
            bjidlist: [],
            timer: '',
            customColumnOpen: false,
            selectedColumnParam: {
                type: "学生",
            },
            tableColumnInit: [
                {
                    label: '录取年份',
                    prop: 'nf',
                    search: true,
                    type: 'select',
                    dicUrl: '/ksgl/tdd/allnf',
                    dicMethod: 'get',
                    props: {
                        label: 'nf',
                        value: 'nf'
                    }
                },
                {
                    label: '层次',
                    prop: 'cc',
                    search: true,
                    type: 'select',
                    props: {
                      label: 'name',
                      value: 'name'
                    },
                    dicUrl: '/code/codeccb/all?isSearch=是',
                },
                {
                    label: '招生类型',
                    prop: 'zslx',
                    search: true,
                    type: 'select',
                    dicUrl: '/code/codezslx/all',
                    dicMethod: 'get',
                    props: {
                        label: 'zslx',
                        value: 'zslx'
                    }
                },
                {
                  label: "省份",
                  prop: "sfmc",
                  search:true,
                  type: 'select',
                  props: {
                    label: 'sfmc',
                    value: 'sfmc'
                  },
                  dicUrl: '/ksgl/tdd/selectByField?field=sfmc',
                },
                {
                  label: "批次",
                  prop: "pcmc",
                  search:true,
                  type: 'select',
                  props: {
                    label: 'pcmc',
                    value: 'pcmc'
                  },
                  dicUrl: '/ksgl/tdd/selectByField?field=pcmc',
                },
                {
                  label: "科类",
                  prop: "klmc",
                  search:true,
                  type: 'select',
                  props: {
                    label: 'klmc',
                    value: 'klmc'
                  },
                  dicUrl: '/ksgl/tdd/selectByField?field=klmc',
                },
                {
                  label: "学院",
                  prop: "xymc",
                  search:true,
                  type: 'select',
                  dicUrl: '/ksgl/tdd/selectByField?field=xymc',
                  dicMethod: 'get',
                  props: {
                    label: 'xymc',
                    value: 'xymc'
                  },
                },
                {
                  label: "专业",
                  prop: "zymc",
                  search:true,
                  type: 'select',
                  props: {
                    label: 'zymc',
                    value: 'zymc'
                  },
                  dicUrl: '/ksgl/tdd/selectByField?field=zymc',
                },
                {
                    label: '考生号',
                    prop: 'ksh',
                    search: true,
                },
                {
                    label: '姓名',
                    prop: 'xm',
                    search: true,
                },
            ],
            tableColumns: [],
            slotname: [],

            selectionList: [],
            data: [],
            excelBox: false,
            excelForm: {},
            excelOption: {
                submitBtn: false,
                emptyBtn: false,
                column: [
                    {
                        label: "上传照片",
                        prop: "file",
                        type: "upload",
                        drag: true,
                        loadText: "上传中，请稍等",
                        span: 24,
                        accept: 'application/zip,image/png, image/jpeg,',
                        propsHttp: {
                            res: "info",
                        },
                        tip: "请上传 .zip 标准格式文件",
                        action: "/ksgl/tdd/importPhotos",
                    },

                ],
            },
            batchSize: 5,
            isPaused: false,
            isShowPrintProgress: false,
            totalPrints:0,
            currentRequest:0,
            successfulCount:0,
            failedCount:0,
            socket: null,
            printClientShow: false,
            printClientList: [],
            printBtnText: '暂停打印',
            isShowPrintBtn: false,
            isPrintTzsEnabled: false, // 是否启用打印录取通知书功能
        };
    },
    created() {
        if (this.$route.query) {
            if (this.$route.query.queryjson) {
                this.query = JSON.parse(this.$route.query.queryjson);
            }
        }
        this.tableColumns = deepClone(this.tableColumnInit);
        this.getCustomColumnData();
        this.onLoad(this.page);
        this.getallAndField();
        this.checkPrintTzsParam();
    },
    mounted() {
        // this.connectWebSocket();
    },
    beforeDestroy() {
        if (this.socket) {
            this.socket.close();
        }
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
                addBtn: this.vaildData(this.permission.tdd_add, false),
                viewBtn: this.vaildData(this.permission.tdd_view, false),
                delBtn: this.vaildData(this.permission.tdd_delete, false),
                editBtn: this.vaildData(this.permission.tdd_edit, false),
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        },
        namingPreview() {
            if (!this.selectedNamingRule) return '';

            const selectedRule = this.namingRules.find(rule => rule.code === this.selectedNamingRule);
            if (!selectedRule) return '';

            let template = selectedRule.template;
            if (this.selectedNamingRule === 'custom' && this.customTemplate) {
                template = this.customTemplate;
            }

            // 使用示例数据生成预览
            const exampleData = {
                '{ksh}': '2024001001',
                '{xm}': '张三',
                '{zymc}': '计算机科学与技术',
                '{xymc}': '计算机学院',
                '{bj}': '计科2024-01',
                '{sfzh}': '110101199001011234',
                '{xb}': '男',
                '{mz}': '汉族',
                '{klmc}': '理工类',
                '{pcmc}': '本科一批',
                '{nf}': '2024',
                '{xz}': '四年',
                '{lqfs}': '统招'
            };

            let preview = template;
            Object.keys(exampleData).forEach(key => {
                preview = preview.replace(new RegExp(key.replace(/[{}]/g, '\\$&'), 'g'), exampleData[key]);
            });

            return preview + '.jpg';
        },
        option() {
            return {
                height: 'auto',
                calcHeight: 210,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: true,
                column: this.tableColumns,
            }
        },
        totalProgress() {
            if (this.totalPrints === 0) {
                return 0;
            }
            return Math.round((this.currentRequest / this.totalPrints) * 100);
        },
    },
    methods: {
        // 检查是否启用打印录取通知书功能
        checkPrintTzsParam() {
            GetSysParamNoLogin({idOrNameOrType: "isPrinttzs"}).then(res => {
                if (res.data && res.data.info && res.data.info.value) {
                    // 如果参数值为"是"，则启用打印功能
                    this.isPrintTzsEnabled = res.data.info.value === '是';
                } else {
                    // 默认不启用
                    this.isPrintTzsEnabled = false;
                }
            }).catch(error => {
                console.error('获取打印参数失败:', error);
                // 出错时默认不启用
                this.isPrintTzsEnabled = false;
            });
        },
        onclose() {
            this.dialogVisiblesb = false;
            this.opendialogtype[this.dialogtype] = false;
        },
        handleClosesb(done) {
            this.dialogVisible = false;
            this.opendialogtype[this.dialogtype] = false;
            done();
        },
        handleClosetzs(done) {
            this.tzsShow = false;
            done();
        },
        opendialog(type, title, zptype) {
            this.zptype = zptype;
            this.dialogVisiblesb = true;
            this.opendialogtype[type] = true;
            this.dialogtitle = title;
            this.dialogtype = type;
        },
        handleClick(tab, event) {
            console.log(tab, event);
        },
        fieldbcxx(val) {
            console.log(val)
            const index = this.bjidlist.indexOf(val.field.id);
            if (index !== -1) {
                this.bjidlist.splice(index, 1);
            }
            let data = {
                id: this.jixxrow.id,
            }
            data[val.field.fielden] = val.fieldValue;
            add(data).then(() => {
                this.jixxrow[val.field.fielden] = val.fieldValue;
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                window.console.log(error);
            });
        },
        bjxx(field) {
            this.bjidlist.push(field.id);
        },
        rowSave(row, done, loading) {
            add(row).then(() => {
                done();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                window.console.log(error);
                loading();
            });
        },
        rowUpdate(row, index, done, loading) {
            update(row).then(() => {
                done();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                window.console.log(error);
                loading();
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handlePreview(row) {
            this.loading = true;
            preview({id:row.id}).then(res=>{
                if(res.data){
                    this.tzsdata = res.data;
                    this.tzsShow = true;
                    this.loading = false;

                }
            })
        },
        handleEdit(row) {
            getone(row.id).then((res) => {
                console.log(`getone`,res)
                this.tmform = res.data.data;
                this.jixxrow = row;
                this.jbxxShow = true;
            });

            // this.getallAndField();

        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    // this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            // this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
            this.onLoad(this.page);
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
            this.onLoad(this.page);
        },
        getallAndField() {
            allAndField().then(res => {
                const data = res.data.data;
                this.fieldlist = data;
                console.log(data)
            });
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });

        },
        submitxzzp() {
            // 自动判断下载方式：有勾选就按勾选，没有勾选就按查询条件
            let context = {};
            let downloadCount = 0;
            let modeText = '';

            if (this.selectionList.length > 0) {
                // 按勾选考生下载
                context.id = this.ids; // 使用已有的ids计算属性
                downloadCount = this.selectionList.length;
                modeText = '勾选的考生';
            } else {
                // 按查询条件下载
                context = this.query;
                downloadCount = this.page.total || 0;
                modeText = '查询条件匹配的考生';
            }

            const params = {
                fileType: 'zsxt_photo',
                namingRule: this.selectedNamingRule
            };

            // 如果是自定义规则，添加自定义模板
            if (this.selectedNamingRule === 'custom' && this.customTemplate) {
                params.customTemplate = this.customTemplate;
            }

            // 构建确认消息
            let confirmMessage = `确定要下载 ${downloadCount} 个${modeText}的照片吗？`;
            if (downloadCount === 0) {
                confirmMessage = `当前${modeText}数量为 0，确定要继续下载吗？`;
            }

            this.$confirm(confirmMessage, '确认下载', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'info'
            }).then(() => {
                exportPhotos(context, params).then((res) => {
                    this.$message.success(`照片下载任务已提交，共 ${downloadCount} 个考生`);
                }).catch((error) => {
                    this.$message.error('下载失败：' + (error.message || error));
                });
                this.xzzpshow = false;
            }).catch(() => {
                // 用户取消下载
            });
        },
        toxzzp() {
            this.xzzpshow = true;
            // 加载命名规则和占位符数据
            this.loadNamingRules();
            this.loadPlaceholders();
        },
        handleCustomColumn() {
            this.customColumnOpen = true;
        },
        // 加载命名规则
        loadNamingRules() {
            if (this.namingRules.length === 0) {
                getNamingRules().then(res => {
                    if (res.data.success) {
                        this.namingRules = res.data.data;
                    }
                }).catch(error => {
                    console.error('加载命名规则失败:', error);
                });
            }
        },
        // 加载占位符列表
        loadPlaceholders() {
            if (this.placeholderList.length === 0) {
                getAvailablePlaceholders().then(res => {
                    if (res.data.success) {
                        const placeholders = res.data.data;
                        this.placeholderList = Object.keys(placeholders).map(key => ({
                            placeholder: key,
                            description: placeholders[key]
                        }));
                    }
                }).catch(error => {
                    console.error('加载占位符失败:', error);
                });
            }
        },
        // 命名规则变化时的处理
        onNamingRuleChange(value) {
            if (value !== 'custom') {
                this.customTemplate = '';
            }
        },

        closeCustomColumnTransfer(data) {
            this.customColumnOpen = data.customColumnOpen;
            this.getCustomColumnData();
            this.onLoad(this.page);
        },
        getCustomColumnData() {
            getAllCustomColData({type: "学生"}).then(res => {
                let tableColumnsArr = deepClone(this.tableColumnInit);
                const existingProps = tableColumnsArr.map(col => col.prop);

                if (res.data.code === 200) {
                    const data = res.data.data;
                    data.forEach(obj => {
                      if (existingProps.includes(obj.fielden)) {
                        // console.log(`跳过已定义的字段: ${obj.fieldzh} (${obj.fielden})`);
                        return;
                      }
                      let dicUrl = obj.fieldData;
                      let props = obj.fieldAttribute!==''?JSON.parse(obj.fieldAttribute).props:{};
                      if (dicUrl && !dicUrl.startsWith('/')) {
                        dicUrl = '/' + dicUrl;
                      }

                      let column = {
                        label: obj.fieldzh,
                        prop: obj.fielden,
                        type: obj.fieldType,
                        search: obj.sfss === '是',
                        change: ({value, column}) => {
                          this.param[obj.fielden] = value;
                        }
                      };
                      // 处理dicUrl配置
                      if (dicUrl && dicUrl.trim() !== '') {
                        // 确保字段类型支持dicUrl
                        if (!column.type || column.type === '文本') {
                          column.type = 'select';
                          console.log(`动态字段 ${obj.fieldzh} 类型从 '${obj.fieldType}' 改为 'select'`);
                        }

                        column.dicUrl = dicUrl;
                        column.dicMethod = 'get';

                        if (!props || Object.keys(props).length === 0) {
                          column.props = {
                            label: 'label',
                            value: 'value'
                          };
                        } else {
                          column.props = props;
                        }

                        console.log(`动态字段 ${obj.fieldzh} 配置了dicUrl: ${dicUrl}, 类型: ${column.type}`);
                      }

                      tableColumnsArr.push(column);

                    })
                }

                // console.log("最终的表格列配置:", tableColumnsArr);

                // 统计字段信息
                const totalFields = tableColumnsArr.length;
                const dicUrlFields = tableColumnsArr.filter(col => col.dicUrl);
                const predefinedFields = this.tableColumnInit.filter(col => col.dicUrl);

                // console.log(`字段统计:
                //   - 总字段数: ${totalFields}
                //   - 预定义字段数: ${this.tableColumnInit.length}
                //   - 配置dicUrl的字段数: ${dicUrlFields.length}
                //   - 预定义dicUrl字段数: ${predefinedFields.length}`);

                // console.log("配置了dicUrl的字段:", dicUrlFields.map(f => `${f.label}(${f.prop}): ${f.dicUrl}`));

                this.tableColumns = tableColumnsArr;
                this.$nextTick(() => {
                  this.$forceUpdate();
                });
            })
        },
        refresh() {
            this.onLoad(this.page);
        },
        // 导入
        handleImport() {
            this.excelBox = true;
        },

        uploadAfter(res, done, loading) {
            if (res === "success") {
                this.excelBox = false;
                this.refresh();
                done();
            } else if (res === undefined) {
                this.$message.error("上传内容格式有误！");
                loading();
            } else {
                this.$message.warning("请上传 .zip 标准格式文件");
                loading();
            }
        },
        handleExport() {
            if (this.selectionList.length > 0) {
                const exportParams = {
                    ids: this.ids,
                    exportType: 'byIds'
                };
                exportData(exportParams, {fileType: 'zsxt_photo'}).then((res) => {
                    this.$message({
                        type: "success",
                        message: `已成功导出 ${this.selectionList.length} 条选中记录！`
                    });
                }).catch((error) => {
                    console.error('导出失败:', error);
                    this.$message({
                        type: "error",
                        message: "导出失败，请重试！"
                    });
                });
            } else {
                const exportParams = {
                    ...this.query,
                    exportType: 'byQuery'
                };
                exportData(exportParams, {fileType: 'zsxt_photo'}).then((res) => {
                    this.$message({
                        type: "success",
                        message: "已成功按查询条件导出数据！"
                    });
                }).catch((error) => {
                    console.error('导出失败:', error);
                    this.$message({
                        type: "error",
                        message: "导出失败，请重试！"
                    });
                });
            }
        },
        handleGenerateNotNum() {
            this.$confirm("确定要生成通知书号?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(()=>{
                this.loading = true;
                const context = this.param;
                generateNotificationNumber(context).then((res) => {
                    if (res.data.code === 200) {
                        this.refresh();
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                    } else {
                        this.$message({
                            type: "error",
                            message: "操作失败!"
                        });
                    }
                    this.loading = false;
                })
            });

        },
        handleClearNotNum() {
            this.$confirm("确定要清空通知书号?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(()=>{
                this.loading = true;
                const context = this.param;
                clearNotificationNumber(context).then((res) => {
                    if (res.data.code === 200) {
                        this.refresh();
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                    } else {
                        this.$message({
                            type: "error",
                            message: "操作失败!"
                        });
                    }
                    this.loading = false;
                })
            }).catch(()=>{

            })


        },
        connectWebSocket() {
            return new Promise((resolve, reject) => {
                if (!this.socket || this.socket.readyState !== WebSocket.OPEN) {
                    this.socket = new WebSocket('ws://localhost:7152/ws');
                    this.socket.onopen = () => {
                        console.log('WebSocket connection established');
                        resolve();
                    };
                    this.socket.onmessage = (event) => {
                        const data = JSON.parse(event.data);
                        console.log('Message from server:', data);
                    };
                    this.socket.onclose = (event) => {
                        console.log('WebSocket connection closed');
                    };
                    this.socket.onerror = (error) => {
                        console.error('WebSocket error:', error);
                        reject(error);
                    };
                } else {
                    resolve();
                }
            });
        },
        handleDownPrintClient(){
            GetListDict({code:'PRINT_CLIENT'}).then(res => {
                this.printClientList = res.data;
            })
            this.printClientShow = true;

        },
        printClientDownload(item){
            window.open('/file/download/'+(item.attachment).replace('/file/view/',''));
        },
        handlePrint(row){
            this.$confirm("确定要打印通知书?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(async () => {
                try {
                    try {
                        await this.connectWebSocket();
                    } catch (error) {
                        console.error(error);
                        this.$message({
                            type: "error",
                            message: "打印服务连接异常",
                        });
                        return
                    }
                    let context = this.param;
                    if (row&&row.id) {
                        context = {};
                        context.id = row.id;
                    }else if (this.selectionList.length > 0) {
                        context = {};
                        context.id = this.ids;
                    }
                    const res = await getAll(context);
                    if (res.data.code === "00000") {
                        this.$message({
                            type: "success",
                            message: `发起打印请求`,
                        });
                        this.isShowPrintProgress = true;
                        this.isShowPrintBtn = true;

                        let kshSet = res.data.info;
                        this.totalPrints = kshSet.length;
                        // 初始化统计变量
                        this.currentRequest = 0;
                        this.successfulCount = 0;
                        this.failedCount = 0;
                        // console.log("待打印的kshSet", kshSet)

                        // 统一处理打印数据
                        try {
                            const printRes = await getPrintData({ ksh: kshSet.join(',') });
                            if (printRes.data.code === "00000") {
                                let printData = printRes.data.info;
                                // console.log("待打印的数据:",JSON.stringify(printData))
                                this.socket.send(JSON.stringify(printData));
                                this.successfulCount = kshSet.length;
                                this.$message({
                                    type: "success",
                                    message: `打印请求完成。成功：${this.successfulCount}`,
                                });
                            } else {
                                this.failedCount = kshSet.length;
                                this.$message({
                                    type: "error",
                                    message: "打印请求发生异常",
                                });
                            }
                        } catch (error) {
                            this.failedCount = kshSet.length;
                            console.error(error);
                            this.$message({
                                type: "error",
                                message: "打印出现异常",
                            });
                        } finally {
                            this.currentRequest = kshSet.length;
                            this.hidePrintProgress();
                            this.refresh();
                        }

                        //逐个打印
                        /*for (let i = 0; i < kshSet.length; i++) {
                            // 检查是否暂停
                            await this.checkPause();

                            let batch = [kshSet[i]];
                            try {
                                const printRes = await getPrintData({ksh: batch.join(',')});
                                if (printRes.data.code === "00000") {
                                    let printData = printRes.data.info;
                                    this.socket.send(JSON.stringify(printData));
                                    this.successfulCount += batch.length;
                                } else {
                                    this.failedCount += batch.length;
                                    this.$message({
                                        type: "error",
                                        message: "打印请求发生异常",
                                    });
                                    this.hidePrintProgress();
                                }
                            } catch (error) {
                                this.failedCount += batch.length;
                                console.error(error);
                            }
                            this.currentRequest += batch.length;
                        }*/

                        //分批打印
                        /*for (let i = 0; i < kshSet.length; i += this.batchSize) {
                            // 检查是否暂停
                            await this.checkPause();

                            let batch = kshSet.slice(i, i + this.batchSize);
                            try {

                                const printRes = await getPrintData({ksh: batch.join(',')});
                                if (printRes.data.code === "00000") {
                                    let printData = printRes.data.info;
                                    this.socket.send(JSON.stringify(printData));
                                    this.successfulCount += batch.length;
                                } else {
                                    this.failedCount += batch.length;
                                    this.$message({
                                        type: "error",
                                        message: "打印请求发生异常",
                                    });
                                    this.hidePrintProgress();
                                }
                            } catch (error) {
                                this.failedCount += batch.length;
                                console.error(error);
                            }
                            this.currentRequest += batch.length;
                        }*/
                        if (this.currentRequest >= this.totalPrints) {
                            this.$message({
                                type: "success",
                                message: `打印请求完成。成功：${this.successfulCount}，失败：${this.failedCount}`,
                            });
                            this.hidePrintProgress();
                            this.refresh();
                        }
                    } else {
                        this.$message({
                            type: "error",
                            message: "获取打印数据失败",
                        });
                    }
                } catch (error) {
                    console.error(error);
                    this.$message({
                        type: "error",
                        message: "打印出现异常",
                    });
                }
            }).catch(()=>{

            })
        },
        async hidePrintProgress() {
            await new Promise(resolve => setTimeout(resolve, 5000));
            this.isShowPrintProgress = false;
            this.isShowPrintBtn = false;
        },
        async checkPause() {
            while (this.isPaused) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        },
        handlePausePrint(){
            this.isPaused = !this.isPaused;
            this.$message({
                type: "info",
                message: this.isPaused ? "打印已暂停" : "打印已继续",
            });
            this.printBtnText = this.isPaused ? "继续打印" : "暂停打印";
            // this.hidePrintProgress();
        },
        updateStatistics(data) {
            if (data.successful_count) {
                this.successfulCount += data.successful_count;
            }
            if (data.failed_count) {
                this.failedCount += data.failed_count;
            }
        },
        handlePrintStatus(){
            console.log(this.totalPrints,this.currentRequest,this.successfulCount,this.failedCount)
        },
    }
};
</script>

<style scoped>
.zhaopianbox {
    width: 130px;
    text-align: center;
    margin-right: 20px;
    margin-bottom: 20px;
}

.descriptionsbox {

}

::v-deep .el-dialog__body {
    padding: 10px 20px;
}
/* 定义过渡效果 */
.fade-enter-active, .fade-leave-active {
    transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active for below version 2.1.8 */ {
    opacity: 0;
}
</style>
