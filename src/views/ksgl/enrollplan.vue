<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @refresh-change="refresh"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.enrollplan_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   size="mini"
                   icon="el-icon-upload2"
                   @click="handleImport">导入
        </el-button>
        <el-button type="success"
                   size="mini"
                   icon="el-icon-download"
                   @click="handleExport">导出
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="数据导入"
               v-loading="dialogloading"
               append-to-body
               :visible.sync="excelBox"
               width="600px"
               :close-on-click-modal="false">
<!--      <avue-form :option="excelOption"-->
<!--                 v-model="excelForm"-->
<!--                 :upload-after="uploadAfter">-->
<!--        <template slot="menuForm">-->
<!--          <el-button type="primary" @click="handleTemplate()">-->
<!--            点击下载模板<i class="el-icon-download el-icon&#45;&#45;right"></i>-->
<!--          </el-button>-->
<!--        </template>-->
<!--      </avue-form>-->
      <el-tabs v-model="activeName">
        <el-tab-pane label="计划编制" name="first">
          <avue-form v-model="jhform" :option="jhoption">
            <template v-slot:file>
              <el-upload
                  class="upload-demo"
                  ref="jhbzupload"
                  accept=".xls/.xlsx"
                  action="/ksgl/enrollplan/import"
                  :data="jhform"
                  :on-change="handlechange"
                  :on-remove="handleRemove"
                  :on-success="handlesuccess"
                  :on-error="handleerror"
                  :file-list="fileList"
                  :limit="1"
                  :auto-upload="false">
                <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
              </el-upload>
              <div class="tishibox">
                <p>系统支持导入教育部计划来源网导出完整“计划编制”</p>
                <p>下载路径：【来源计划网】计划编制管理→计划数编制→导出Excel</p>
              </div>
            </template>
          </avue-form>
        </el-tab-pane>
        <el-tab-pane label="计划调整" name="second">
          <avue-form v-model="jhform" :option="jhoption">
            <template v-slot:file>
              <el-upload
                  class="upload-demo"
                  ref="jhupload"
                  accept=".xls/.xlsx"
                  action="/ksgl/enrollplan/import"
                  :data="jhform"
                  :on-change="handlechange"
                  :on-remove="handleRemove"
                  :on-success="handlesuccess"
                  :on-error="handleerror"
                  :file-list="fileList"
                  :limit="1"
                  :auto-upload="false">
                <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
              </el-upload>
              <div class="tishibox">
                <p>系统支持导入教育部计划来源网导出完整“计划调整”</p>
                <p>下载路径：【来源计划网】计划调整→调整明细→查询→导出Excel</p>
              </div>
            </template>
          </avue-form>
        </el-tab-pane>
        <el-tab-pane label="自定义计划" name="third">
          <avue-form v-model="jhform" :option="jhoption">
            <template v-slot:file>
              <el-upload
                  class="upload-demo"
                  ref="jhupload"
                  accept=".xls/.xlsx"
                  action="/ksgl/enrollplan/import"
                  :data="jhform"
                  :on-change="handlechange"
                  :on-remove="handleRemove"
                  :on-success="handlesuccess"
                  :on-error="handleerror"
                  :file-list="fileList"
                  :limit="1"
                  :auto-upload="false">
                <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                <div slot="tip" class="el-upload__tip">请上传 .xls,.xlsx 标准格式文件</div>
                <div style="margin-top: 10px;">
                  <el-button type="primary" @click="handleTemplate()">
                    点击下载模板<i class="el-icon-download el-icon--right"></i>
                  </el-button>
                </div>
              </el-upload>
              <div class="tishibox">
                <p>系统支持导入自定义计划数据（自主招生、高校专项计划、保送生）</p>
              </div>
            </template>
          </avue-form>
        </el-tab-pane>
      </el-tabs>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitform">导 入</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import {add, exportDataAsync, getDetail, getList, remove, update} from "@/api/ksgl/enrollplan";
import {mapGetters} from "vuex";

export default {
    data() {
      return {
        form: {},
        fileList: [],
        jhform: {},
        activeName: 'first',
        query: {},
        loading: true,
        dialogloading: false,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        excelBox: false,
        excelForm: {},
        excelOption: {
          submitBtn: false,
          emptyBtn: false,
          column: [
            {
              label: "文件上传",
              prop: "file",
              type: "upload",
              drag: true,
              loadText: "文件上传中，请稍等",
              span: 24,
              propsHttp: {
                res: "info",
              },
              tip: "请上传 .xls,.xlsx 标准格式文件",
              action: "/ksgl/enrollplan/import",
            },
          ],
        },
        selectionList: [],
        nflist: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "年份",
              prop: "nf",
              addDisplay: false,
              editDisplay: false,
              search:true,
              type: 'select',
              props: {
                label: 'nf',
                value: 'nf'
              },
              dicUrl: '/ksgl/enrollplan/allnf',
              rules: [{
                required: true,
                message: "请输入年份",
                trigger: "blur"
              }]
            },
            {
              label: "省份名称",
              prop: "syssmc",
              search: true,
              rules: [{
                required: true,
                message: "请输入省份名称",
                trigger: "blur"
              }]
            },
            {
              label: '层次',
              prop: 'ccmc',
              search: true,
              type: 'select',
              props: {
                label: 'name',
                value: 'name'
              },
              dicUrl: '/code/codeccb/all?isSearch=是',
            },
            {
              label: "计划类别名称",
              prop: "jhlbmc",
              search: true,
              type: 'select',
              props: {
                label: 'jhlbmc',
                value: 'jhlbmc'
              },
              dicUrl: '/ksgl/tdd/selectByField?field=jhlbmc',
              searchLabelWidth: 100,
              rules: [{
                required: true,
                message: "请输入计划类别名称",
                trigger: "blur"
              }]
            },
            {
              label: "招生专业代码",
              prop: "zszydm",
              rules: [{
                required: true,
                message: "请输入招生专业代码",
                trigger: "blur"
              }]
            },
            {
              label: "招生专业名称",
              prop: "zszymc",
              rules: [{
                required: true,
                message: "请输入招生专业名称",
                trigger: "blur"
              }]
            },
            {
              label: "专业代号",
              prop: "xbzydh",
              search: true,
              rules: [{
                required: true,
                message: "请输入专业代号",
                trigger: "blur"
              }]
            },
            {
              label: "专业类别名称",
              prop: "zylbmc",
            },
            {
              label: "招考方向",
              prop: "zkfx",
              search: true,
            },
            {
              label: "学制",
              prop: "xzdm",
            },
            {
              label: "收费标准",
              prop: "sfbz",
            },
            {
              label: "办学地点",
              prop: "bxdd",
            },
            {
              label: "院系名称",
              prop: "yxbmmc",
            },
            {
              label: "专业备注",
              prop: "zybz",
            },
            {
              label: "科类名称",
              prop: "klmc",
              rules: [{
                required: true,
                message: "请输入科类名称",
                trigger: "blur"
              }]
            },
            {
              label: "计划性质名称",
              prop: "jhxzmc",
              rules: [{
                required: true,
                message: "请输入计划性质名称",
                trigger: "blur"
              }]
            },
            {
              label: "批次名称",
              prop: "pcmc",
              rules: [{
                required: true,
                message: "请输入批次名称",
                trigger: "blur"
              }]
            },
            {
              label: "招考类型名称",
              prop: "zklxmc",
            },
            {
              label: "计划编制数",
              prop: "zsjhs",
              rules: [{
                required: true,
                message: "请输入计划编制数",
                trigger: "blur"
              }]
            },
            {
              label: "计划调整",
              prop: "tzjhs",
            },
          ]
        },
        data: []
      };
    },
    created() {
      if(this.$route.query&&this.$route.query.queryjson){
        console.log(this.$route.query.queryjson)
        this.query = JSON.parse(this.$route.query.queryjson)
      }
      const now = new Date();
      const currentYear = now.getFullYear();
      const specifiedNumber = currentYear;
      this.nflist = this.generateNumberArray(specifiedNumber);
    },
  computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.enrollplan_add, false),
          viewBtn: this.vaildData(this.permission.enrollplan_view, false),
          delBtn: this.vaildData(this.permission.enrollplan_delete, false),
          editBtn: this.vaildData(this.permission.enrollplan_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
      jhoption(){
        return {
          submitBtn: false,
          emptyBtn: false,
          size: 'mini',
          column: [
            {
              label: '年份',
              prop: 'nf',
              type: 'select',
              span: 24,
              dicData: this.nflist
            },
            {
              label: '',
              prop: 'file',
              type: 'upload',
              span: 24,
              slot: true
            },
          ]
        }
      },
    },
    methods: {
      generateNumberArray(num) {
        const result = [];
        for (let i = num; i > num -10; i--) {
          result.push({
            label: i,
            value: i,
          });
        }
        return result;
      },
      submitform(){
        this.dialogloading = true;
        if(this.activeName == 'first'){
          this.jhform.type = 'jhbz'
          this.$refs.jhupload.submit();
        }else if(this.activeName == 'second'){
          this.jhform.type = 'jhtz'
          this.$refs.jhupload.submit();
        }else if(this.activeName == 'third'){
          this.jhform.type = 'zdy'
          if(!this.jhform.nf){
            this.$message({
              message: '请选择年份',
              type: 'warning'
            });
          }
          this.$refs.jhupload.submit();
        }

      },
      handlechange(file, fileList){
        console.log(`handlechange`,file, fileList);
        this.fileList = fileList;
      },
      handleRemove(file, fileList) {
        console.log(file, fileList);
        this.fileList = [];
      },
      handlesuccess(response){
        console.log(`handlesuccess`,response)
        if(response.code == 200 || response.code == '00000'){
          this.dialogloading = false;
          this.excelBox = false;
          this.$message({
            message: '导入成功',
            type: 'success'
          });
        }else {
          this.dialogloading = false;
          this.excelBox = false;
          this.$message.error('导入失败');
        }
      },
      handleerror(err){
        console.log(`handleerror`,err)
        this.dialogloading = false;
        this.excelBox = false;
        this.$message.error('导入失败');
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
        this.onLoad(this.page);
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
        this.onLoad(this.page);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      refresh() {
        this.onLoad(this.page);
      },
      // 导入
      handleImport() {
        this.excelBox = true;
      },

      uploadAfter(res, done, loading) {
        if (res === "success") {
          this.excelBox = false;
          this.refresh();
          done();
        } else if (res === undefined) {
          this.$message.error("上传内容格式有误！");
          loading();
        } else {
          this.$message.warning("请上传 .xls,.xlsx 标准格式文件");
          loading();
        }
      },

      handleTemplate() {
        window.open(`/ksgl/enrollplan/downImportTemplate`);
      },
      handleExport() {
        const context = this.query;
        exportDataAsync(context).then((res) => {})
      },
    }
  };
</script>

<style>
.dialog-footer{
  text-align: right;
}
.tishibox{
  width: 100%;
  height: 100%;
  border: 1px solid #00a5ec;
  background-color: #E6F7FF;
  font-size: 12px;
}
.tishibox p{
  margin: 5px 10px;
  line-height: 20px;
}
</style>
