package com.sanyth.report;

import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.statement.Statements;
import org.junit.jupiter.api.Test;

public class TestJsqlParser {

    @Test
    public void testIf() throws JSQLParserException {
        Statements stmt = CCJSqlParserUtil.parseStatements("SELECT * FROM SYT_PERMISSION_ACCOUNT where 1=1 and <PERSON>UM<PERSON><PERSON><PERSON> in (select HUMANCODE from SYT_PERMISSION_ACCOUNT inner join SYT_SYS_ORGANIZATION_USER on ID=USER_ID where ORGANI<PERSON><PERSON>ION_ID in ('2c0bf31100244ebe811ff87daf8196a8','b5445ff2ec1643ff98bf72d6c5350ff3'))");
    }

}
