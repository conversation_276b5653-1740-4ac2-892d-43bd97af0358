package com.sanyth.report;

import org.junit.jupiter.api.Test;
import org.wzy.sqltemplate.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TestTemplate {

    @Test
    public void testIf() {
        Configuration configuration = new Configuration();
//        String sql = "select * from user where <if test='id != null ' > id  = ${id} </if>";
        String sql = "select xh as 学号,xm as 姓名,xb as 性别,dwmc as 部门,zymc as 专业,bjmc as 班级,sqsj as 申请日期 from v_yqsj_rccxsq where 1=1 " +
                "    <if test=\"学号 !=null and 学号 !=''\">and xh = '${学号}'</if>\n" ;
        SqlTemplate template = configuration.getTemplate(sql);
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("部门", "");
        SqlMeta process = template.process(map);
        System.out.println(process);
    }

    @Test
    public void testWhere() {

        Configuration configuration = new Configuration();
        for (int i = 0; i < 2; i++) {
            SqlTemplate template = configuration
                    .getTemplate("select * from user <where> <if test='id != null ' > and id  = ${id} </if>  <if test=' name != null' > and name =${name}</if> </where>");
            HashMap<String, Object> map = new HashMap<String, Object>();
//            map.put("id", "0");
            map.put("name", "1fffdsfdf1");
            SqlMeta process = template.process(map);
            System.out.println(process);
        }

    }


    @Test
    public void testSet() {
        Configuration configuration = new Configuration();
        SqlTemplate template = configuration
                .getTemplate("update user  <set> <if test='id != null '> id = ${id} ,</if><if test='name != null '> name = ${name} ,</if> </set> ");
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("id", "123");
        map.put("name", "1fffdsfdf1");
        SqlMeta process = template.process(map);
        System.out.println(process);

    }


    @Test
    public void testChoose() {

        Configuration configuration = new Configuration();
        SqlTemplate template = configuration
                .getTemplate("select  * from user <where><choose><when test=' id!= null '> and id = ${id} </when><when test=' name!= null '> and name = ${name} </when></choose> </where>");
        HashMap<String, Object> map = new HashMap<String, Object>();
        //map.put("id", "123");
        //map.put("name", "hhh1");
        SqlMeta process = template.process(map);
        System.out.println(process);

    }

    @Test
    public void testForEach() {
        Configuration configuration = new Configuration();
        SqlTemplate template = configuration
                .getTemplate("select  * from user <where> id in <foreach item=\"item\" index=\"index\" collection=\"list\"    open=\"(\" separator=\",\" close=\")\">   ${item}   ${index}  </foreach></where>");
        HashMap<String, Object> map = new HashMap<String, Object>();
        //map.put("id", "123");
        //map.put("name", "hhh1");
		/*ArrayList<String> arrayList = new ArrayList<String>() ;
		arrayList.add("1") ;
		arrayList.add("2") ;
		arrayList.add("3") ;
		arrayList.add("4") ;*/
        HashMap<String, Object> map2 = new HashMap<String, Object>();
        map2.put("11", "11-11");
        map2.put("22", "22-22");
        map.put("list", map2);
        SqlMeta process = template.process(map);
        System.out.println(process);

    }

    @Test
    public void testTemplateEngin() {
        SqlTemplateEngin sqlTemplateEngin = new SqlTemplateEngin();
        String sqlTpl = "select * from user_info <where><if test=' username != null' > and  username = ${username} </if><if test=' email != null' > and  email = ${email} </if></where> ";
        //从字符串读取sql模板内容,还可以从单独的文件读取
        SqlTemplate sqlTemplate = sqlTemplateEngin.getSqlTemplate(sqlTpl);

        Bindings bind = new Bindings().bind("email", "<EMAIL>");

        SqlMeta sqlMeta = sqlTemplate.process(bind); //可传map对象或javabean对象

        //System.out.println(sqlMeta.getSql());

//        Assert.assertEquals("select * from user_info  WHERE  email = ?   ", sqlMeta.getSql());
        List<Object> parameter = sqlMeta.getParameter(); //取出参数
//        Assert.assertEquals(1, parameter.size());


        bind.bind("username", "wenzuojing");
        sqlMeta = sqlTemplate.process(bind);
//        Assert.assertEquals("select * from user_info  WHERE  username = ?   and  email = ?   ", sqlMeta.getSql());
        List<Object> parameter2 = sqlMeta.getParameter();//取出参数
//        Assert.assertEquals(2, parameter2.size());

    }


    @Test
    public void testTemplateEngin2() {

        SqlTemplateEngin sqlTemplateEngin = new SqlTemplateEngin();

        Map<String, Object> userInfo = new HashMap<String, Object>();

        userInfo.put("id", "123456");
        userInfo.put("email", "<EMAIL>");

        String sqlTpl = " update userinfo <set> <if test ='email != null '> email = ${email} </if>, <if test='age'> age = ${age} </if> </set> where id = ${id}";

        SqlTemplate sqlTemplate = sqlTemplateEngin.getSqlTemplate(sqlTpl);

        SqlMeta sqlMeta = sqlTemplate.process(userInfo);

//        Assert.assertEquals(" update userinfo  SET email = ?    where id = ? ", sqlMeta.getSql());

        List<Object> parameter = sqlMeta.getParameter(); //取出参数
//        Assert.assertEquals(2, parameter.size());

    }


}
