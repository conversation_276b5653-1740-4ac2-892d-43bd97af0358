package com.sanyth.report;

import org.junit.jupiter.api.Test;
import org.springframework.util.Base64Utils;

import java.nio.charset.StandardCharsets;

public class TestEncodeUrl {


    @Test
    public void encodeUrl(){
        String url = "/index#/bigscreen/layoutviewer?reportId=1503557985669025793";
//        String url = "/index#/mobile/xueshenghuaxiang/index";
        System.out.println("/noAuth/platform/qywx/toAuth/directive?encodeUrl=" + Base64Utils.encodeToUrlSafeString(url.getBytes(StandardCharsets.UTF_8)));
    }
}
