// 基础路径 注意发布之前要先修改这里
let baseUrl = './'
const isProduction = process.env.NODE_ENV === 'production'

module.exports = {
    publicPath: baseUrl, // 根据你的实际情况更改这里
    lintOnSave: true,
    productionSourceMap: false,
    configureWebpack: config => {
        // 生产环境配置
        if (isProduction) {
            // 压缩处理
            const CompressionPlugin = require('compression-webpack-plugin');
            config.plugins.push(
                new CompressionPlugin({
                    algorithm: 'gzip',
                    test: /\.(js|css|json|txt|html|ico|svg)(\?.*)?$/i,
                    threshold: 10240,
                    minRatio: 0.8
                })
            )

            // 代码分割优化
            config.optimization.splitChunks = {
                chunks: 'all',
                minSize: 30000,
                maxSize: 250000,
                minChunks: 1,
                maxAsyncRequests: 5,
                maxInitialRequests: 3,
                automaticNameDelimiter: '~',
                name: true,
                cacheGroups: {
                    vendors: {
                        test: /[\\/]node_modules[\\/]/,
                        priority: -10,
                        name: 'chunk-vendors'
                    },
                    common: {
                        name: 'chunk-common',
                        minChunks: 2,
                        priority: -20,
                        chunks: 'initial',
                        reuseExistingChunk: true
                    },
                    // 单独打包elementUI
                    elementUI: {
                        name: 'chunk-elementUI',
                        priority: 20,
                        test: /[\\/]node_modules[\\/]element-ui[\\/]/
                    }
                }
            }
        }

        // 添加.mjs支持
        config.module.rules.push({
            test: /\.mjs$/,
            include: /node_modules/,
            type: "javascript/auto"
        });
    },
    chainWebpack: (config) => {
        //忽略的打包文件
        config.externals({
            'vue': 'Vue',
            'vue-router': 'VueRouter',
            'vuex': 'Vuex',
            'axios': 'axios',
            'element-ui': 'ELEMENT',
        })

        const entry = config.entry('app')
        entry.add('babel-polyfill').end()
        entry.add('classlist-polyfill').end()
        // entry.add('@/mock').end()

        // 移除 prefetch 插件，减少首屏加载
        config.plugins.delete('prefetch')

        // 生产环境特定优化
        if (isProduction) {
            // 优化图片处理
            config.module
                .rule('images')
                .set('parser', {
                    dataUrlCondition: {
                        maxSize: 10000
                    }
                });
        }
    },
    css: {
        // 是否使用css分离插件
        extract: isProduction,
        // 开启 CSS source maps
        sourceMap: false
    },
    devServer: {
        proxy: {
            "/": {
                // 目标 API 地址
                target: 'http://localhost:9094/',
                // target: 'http://**************:9094/',
                changeOrigin: true,
                pathRewrite: {
                    '^/': ''
                }
            }
        }
    }
}
