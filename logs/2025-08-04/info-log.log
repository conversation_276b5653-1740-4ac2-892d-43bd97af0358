2025-08-04 15:37:00.350 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-04 15:37:00.841 [main] INFO  com.sanyth.Application - Starting Application using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 84074 (/Users/<USER>/WorkSpace/sanyth-zsxt/sanyth-zsxt/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-zsxt)
2025-08-04 15:37:00.843 [main] INFO  com.sanyth.Application - The following 1 profile is active: "dev"
2025-08-04 15:37:03.509 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 15:37:03.511 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 15:37:03.572 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 MongoDB repository interfaces.
2025-08-04 15:37:03.591 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 15:37:03.593 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:37:03.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-04 15:37:04.526 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.rpamis.security.starter.autoconfigure.SecurityAutoConfiguration' of type [com.rpamis.security.starter.autoconfigure.SecurityAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:04.553 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationAspect' of type [com.rpamis.security.starter.aop.DesensitizationAspect] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:04.555 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationInterceptor' of type [com.rpamis.security.starter.aop.DesensitizationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:04.569 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rpamis.security-com.rpamis.security.starter.autoconfigure.SecurityProperties' of type [com.rpamis.security.starter.autoconfigure.SecurityProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:04.579 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationAdvisor' of type [com.rpamis.security.starter.aop.DesensitizationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:05.384 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9094 (http)
2025-08-04 15:37:05.410 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9094"]
2025-08-04 15:37:05.411 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 15:37:05.411 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-04 15:37:05.571 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 15:37:05.571 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4609 ms
2025-08-04 15:37:06.071 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 15:37:06.072 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 15:37:06.072 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 15:37:08.572 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-04 15:37:08.983 [cluster-rtt-ClusterId{value='68906324033b897012899c2a', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21005}] to *************:13337
2025-08-04 15:37:09.894 [cluster-ClusterId{value='68906324033b897012899c2a', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21007}] to *************:13337
2025-08-04 15:37:09.896 [cluster-ClusterId{value='68906324033b897012899c2a', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=75716792}
2025-08-04 15:37:16.406 [main] INFO  com.sanyth.core.strategy.ExportStrategyRegistry - 注册导出策略: businessType=three_table_export, strategyClass=ThreeTableExportStrategy
2025-08-04 15:37:21.342 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-04 15:37:21.444 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-04 15:37:21.462 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-04 15:37:21.485 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-04 15:37:22.129 [main] INFO  org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor - Inconsistent constructor declaration on bean with name 'org.springblade.core.launch.server.ServerInfo': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public org.springblade.core.launch.server.ServerInfo(org.springframework.boot.autoconfigure.web.ServerProperties)
2025-08-04 15:37:22.461 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-04 15:37:22.529 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 15:37:22.530 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-08-04 15:37:22.541 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-04 15:37:22.546 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 15:37:22.546 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 15:37:22.546 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-08-04 15:37:22.546 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@142b9d98
2025-08-04 15:37:23.453 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-04 15:37:23.556 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-04 15:37:23.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-04 15:37:23.562 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-04 15:37:23.851 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-04 15:37:23.870 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-04 15:37:23.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-04 15:37:23.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-04 15:37:23.877 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-04 15:37:23.877 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-04 15:37:23.877 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-04 15:37:23.877 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-04 15:37:23.878 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-04 15:37:23.882 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-04 15:37:23.882 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-04 15:37:23.882 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
2025-08-04 15:37:23.945 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-04 15:37:24.125 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9094"]
2025-08-04 15:37:24.193 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9094 (http) with context path ''
2025-08-04 15:37:24.199 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-08-04 15:37:24.200 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 15:37:24.206 [task-1] INFO  org.springblade.core.launch.StartEventListener - ---[ZSXT]---启动完成，当前使用的端口:[9094]，环境变量:[dev]---
2025-08-04 15:37:24.235 [main] INFO  com.sanyth.Application - Started Application in 24.631 seconds (JVM running for 32.131)
2025-08-04 15:37:24.270 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-04 15:37:26.681 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-04 15:37:30.714 [RMI TCP Connection(8)-127.0.0.1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 15:37:30.715 [RMI TCP Connection(8)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 15:37:30.728 [RMI TCP Connection(8)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 13 ms
2025-08-04 15:37:32.521 [RMI TCP Connection(5)-127.0.0.1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:21008}] to *************:13337
2025-08-04 15:37:42.243 [http-nio-9094-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:21009}] to *************:13337
2025-08-04 15:37:42.655 [http-nio-9094-exec-6] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:21010}] to *************:13337
2025-08-04 15:37:43.116 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:38:58.491 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:38:58.493 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:00.551 [http-nio-9094-exec-1] INFO  com.sanyth.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-04 15:39:00.833 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:03.249 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:03.266 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:03.397 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:03.420 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:03.849 [http-nio-9094-exec-10] INFO  /数据导入/顶部统计(/data/api/data-import/dbtj) - 执行SQL：SELECT 
    jhs,
    lqs,
    bds,
    ROUND((bds / NULLIF(jhs, 0)) * 100, 2) AS jhwcl,
    ROUND((bds / NULLIF(lqs, 0)) * 100, 2) AS bdl
FROM
    (SELECT COUNT(*) AS jhs FROM SYT_ENROLL_PLAN WHERE NF = ?) a,
    (SELECT COUNT(*) AS lqs FROM T_TDD WHERE NF = ?) b,
    (SELECT SUM(DECODE(PHOTO, NULL, 0, 1)) AS zps FROM T_TDD WHERE NF = ?) c,
    (SELECT SUM(DECODE(bdzt, '已报到', 1, 0)) AS bds FROM T_TDD WHERE NF = ?) d
2025-08-04 15:39:03.849 [http-nio-9094-exec-10] INFO  /数据导入/顶部统计(/data/api/data-import/dbtj) - SQL参数：2025(String), 2025(String), 2025(String), 2025(String)
2025-08-04 15:39:03.865 [http-nio-9094-exec-3] INFO  /数据导入/按省份统计(/data/api/data-import/sftj) - 执行SQL：select sfmc,zsjhs,tzjhs,lqs,zps,bmbs,tjbs,cjyzybs,bds from
(select SFMC, count(*) lqs,
       sum(decode(PHOTO,null,0,1)) zps,
       sum(decode(BMXX,null,0,1)) bmbs,
       sum(
               case
                   -- 优先统计T_TJXX表，如果在T_TJXX表中找到记录则计数1
                   when exists (select 1 from T_TJXX t where t.nf = T_TDD.nf and t.ksh = T_TDD.ksh)
                   then 1
                   -- 如果在T_TJXX表中找不到记录，则根据T_TDD表中的TJXX字段进行判断
                   when TJXX is not null
                   then 1
                   else 0
               end
           ) tjbs,
       -- sum(decode(TJXX,null,0,1)) tjbs,
       sum(case when cj is not null and  tdcj is not null and pcmc is not null  and klmc is not null
             then 1 else case when cjyzyxx is null then 0 else 1 end end) cjyzybs,
       -- sum(decode(cjyzyxx,null,0,1)) cjyzybs,
       sum(decode(bdzt,'已报到',1,0)) bds
from T_TDD
where NF = ?group by SFMC) a left join(select syssmc, sum(to_number(zsjhs)) zsjhs,sum(to_number(tzjhs)) tzjhs from SYT_ENROLL_PLAN
where NF=? group by syssmc) b
on a.SFMC like '%' || b.SYSSMC||'%'
2025-08-04 15:39:03.866 [http-nio-9094-exec-3] INFO  /数据导入/按省份统计(/data/api/data-import/sftj) - SQL参数：2025(String), 2025(String)
2025-08-04 15:39:04.507 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:04.652 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:33.455 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:34.772 [http-nio-9094-exec-5] INFO  com.sanyth.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-04 15:39:35.032 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:36.859 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:36.885 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:36.966 [http-nio-9094-exec-1] INFO  /数据导入/顶部统计(/data/api/data-import/dbtj) - 执行SQL：SELECT 
    jhs,
    lqs,
    bds,
    ROUND((bds / NULLIF(jhs, 0)) * 100, 2) AS jhwcl,
    ROUND((bds / NULLIF(lqs, 0)) * 100, 2) AS bdl
FROM
    (SELECT COUNT(*) AS jhs FROM SYT_ENROLL_PLAN WHERE NF = ?) a,
    (SELECT COUNT(*) AS lqs FROM T_TDD WHERE NF = ?) b,
    (SELECT SUM(DECODE(PHOTO, NULL, 0, 1)) AS zps FROM T_TDD WHERE NF = ?) c,
    (SELECT SUM(DECODE(bdzt, '已报到', 1, 0)) AS bds FROM T_TDD WHERE NF = ?) d
2025-08-04 15:39:36.966 [http-nio-9094-exec-1] INFO  /数据导入/顶部统计(/data/api/data-import/dbtj) - SQL参数：2025(String), 2025(String), 2025(String), 2025(String)
2025-08-04 15:39:36.973 [http-nio-9094-exec-5] INFO  /数据导入/按省份统计(/data/api/data-import/sftj) - 执行SQL：select sfmc,zsjhs,tzjhs,lqs,zps,bmbs,tjbs,cjyzybs,bds from
(select SFMC, count(*) lqs,
       sum(decode(PHOTO,null,0,1)) zps,
       sum(decode(BMXX,null,0,1)) bmbs,
       sum(
               case
                   -- 优先统计T_TJXX表，如果在T_TJXX表中找到记录则计数1
                   when exists (select 1 from T_TJXX t where t.nf = T_TDD.nf and t.ksh = T_TDD.ksh)
                   then 1
                   -- 如果在T_TJXX表中找不到记录，则根据T_TDD表中的TJXX字段进行判断
                   when TJXX is not null
                   then 1
                   else 0
               end
           ) tjbs,
       -- sum(decode(TJXX,null,0,1)) tjbs,
       sum(case when cj is not null and  tdcj is not null and pcmc is not null  and klmc is not null
             then 1 else case when cjyzyxx is null then 0 else 1 end end) cjyzybs,
       -- sum(decode(cjyzyxx,null,0,1)) cjyzybs,
       sum(decode(bdzt,'已报到',1,0)) bds
from T_TDD
where NF = ?group by SFMC) a left join(select syssmc, sum(to_number(zsjhs)) zsjhs,sum(to_number(tzjhs)) tzjhs from SYT_ENROLL_PLAN
where NF=? group by syssmc) b
on a.SFMC like '%' || b.SYSSMC||'%'
2025-08-04 15:39:36.973 [http-nio-9094-exec-5] INFO  /数据导入/按省份统计(/data/api/data-import/sftj) - SQL参数：2025(String), 2025(String)
2025-08-04 15:39:37.044 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:37.293 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:38.590 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:38.728 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:41.679 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:41.746 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:41.769 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:41.769 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:41.810 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:41.810 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:41.850 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:41.994 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.008 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.008 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.008 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.051 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.187 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.272 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.328 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.356 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.491 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.492 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.492 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.631 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.646 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:39:42.761 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:40:06.522 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:40:06.676 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:40:36.553 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:40:36.699 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:41:06.503 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:41:06.646 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:41:36.509 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:41:36.661 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:42:07.055 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:42:07.186 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:42:36.528 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:42:36.673 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:43:06.673 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:43:07.256 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:43:36.675 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:43:36.825 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:44:06.606 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:44:06.758 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:44:36.568 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:44:36.727 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:45:06.522 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:45:06.682 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:45:36.434 [http-nio-9094-exec-7] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 15:45:36.446 [http-nio-9094-exec-7] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 MongoDB repository interfaces.
2025-08-04 15:45:36.446 [http-nio-9094-exec-7] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:45:36.448 [http-nio-9094-exec-7] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 1 ms. Found 0 Redis repository interfaces.
2025-08-04 15:45:36.818 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:45:36.973 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:06.634 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:07.060 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.512 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.512 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.571 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.571 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.572 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.660 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.662 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.874 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.875 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.966 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:14.966 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.141 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.142 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.142 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.221 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.336 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.339 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.410 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.450 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.508 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.606 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:15.803 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:36.508 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:46:36.653 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:06.551 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:06.965 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:21.967 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:21.968 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.015 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.034 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.034 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.107 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.107 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.164 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.184 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.184 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.271 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.271 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.412 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.652 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.652 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.652 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.652 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.914 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.914 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.917 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:22.918 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:23.091 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.195 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.196 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.268 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.270 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.270 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.271 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.353 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.355 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.434 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.434 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.436 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.436 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.633 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.677 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.695 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.709 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.768 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.786 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.823 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.852 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:32.867 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:33.166 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:36.502 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:47:36.646 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.370 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.370 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.433 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.434 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.434 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.525 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.526 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.590 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.591 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.608 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.686 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.686 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.853 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.909 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:01.933 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:02.007 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:02.070 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:02.086 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:02.152 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:02.168 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:02.306 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:02.350 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:06.782 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:06.919 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:21.972 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:21.972 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.047 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.049 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.049 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.130 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.131 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.216 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.217 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.217 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.306 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.542 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.542 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.546 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.571 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.659 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.695 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.695 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.724 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.884 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:22.906 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:23.061 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:38.394 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:38.547 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.192 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.265 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.285 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.285 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.365 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.445 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.446 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.491 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.533 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.620 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.949 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.959 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:39.960 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.107 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.108 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.110 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.124 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.125 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.269 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.269 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.272 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:48:40.598 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:49:06.565 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:49:06.972 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:49:36.786 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:49:36.947 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:50:06.498 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:50:06.645 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:50:36.638 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:50:36.805 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:51:06.627 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:51:06.790 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:51:36.675 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:51:36.811 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:52:06.584 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:52:06.746 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:52:36.507 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:52:36.645 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:53:06.547 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:53:06.986 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:53:36.577 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:53:36.718 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:54:07.159 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:54:07.309 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:54:36.530 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:54:36.692 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:55:06.536 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:55:06.673 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:55:36.521 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:55:36.980 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:56:06.498 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:56:06.628 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:56:36.544 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:56:36.688 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:57:06.887 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:57:07.046 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:57:36.880 [http-nio-9094-exec-9] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 15:57:36.890 [http-nio-9094-exec-9] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 7 ms. Found 0 MongoDB repository interfaces.
2025-08-04 15:57:36.891 [http-nio-9094-exec-9] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:57:36.895 [http-nio-9094-exec-9] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-08-04 15:57:37.456 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:57:37.851 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:58:06.865 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:58:07.273 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:58:36.574 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:58:36.725 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:59:06.526 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:59:06.671 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:59:36.547 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 15:59:36.690 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:00.105 [scheduling-1] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 16:00:00.110 [scheduling-1] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 MongoDB repository interfaces.
2025-08-04 16:00:00.111 [scheduling-1] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:00:00.114 [scheduling-1] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-08-04 16:00:07.820 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:08.005 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:37.581 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:37.711 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:51.942 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.010 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.011 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.074 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.075 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.143 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.172 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.204 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.230 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.270 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.289 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.547 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.559 [http-nio-9094-exec-10] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:3, serverValue:21008}] to *************:13337 because there was a socket exception raised by this connection.
2025-08-04 16:00:52.573 [http-nio-9094-exec-10] INFO  org.mongodb.driver.cluster - No server chosen by ReadPreferenceServerSelector{readPreference=primary} from cluster description ClusterDescription{type=UNKNOWN, connectionMode=SINGLE, serverDescriptions=[ServerDescription{address=*************:13337, type=UNKNOWN, state=CONNECTING, exception={com.mongodb.MongoSocketWriteException: Exception sending message}, caused by {java.net.SocketException: Broken pipe (Write failed)}}]}. Waiting for 30000 ms before timing out
2025-08-04 16:00:52.574 [http-nio-9094-exec-4] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:5, serverValue:21010}] to *************:13337 because there was a socket exception raised on another connection from this pool.
2025-08-04 16:00:52.650 [http-nio-9094-exec-6] INFO  org.mongodb.driver.cluster - Cluster description not yet available. Waiting for 30000 ms before timing out
2025-08-04 16:00:52.724 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.755 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.884 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:52.891 [cluster-ClusterId{value='68906324033b897012899c2a', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:21018}] to *************:13337
2025-08-04 16:00:52.891 [http-nio-9094-exec-10] INFO  org.mongodb.driver.connection - Closed connection [connectionId{localValue:4, serverValue:21009}] to *************:13337 because there was a socket exception raised on another connection from this pool.
2025-08-04 16:00:52.892 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:53.441 [http-nio-9094-exec-6] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:8, serverValue:21020}] to *************:13337
2025-08-04 16:00:53.523 [http-nio-9094-exec-1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:10, serverValue:21021}] to *************:13337
2025-08-04 16:00:53.563 [http-nio-9094-exec-5] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:11, serverValue:21023}] to *************:13337
2025-08-04 16:00:53.603 [http-nio-9094-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:7, serverValue:21019}] to *************:13337
2025-08-04 16:00:53.690 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:53.761 [http-nio-9094-exec-7] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:9, serverValue:21022}] to *************:13337
2025-08-04 16:00:53.827 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:53.828 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:53.844 [http-nio-9094-exec-9] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:12, serverValue:21024}] to *************:13337
2025-08-04 16:00:53.899 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:53.983 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:54.004 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:54.065 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:54.066 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:54.145 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:54.228 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:55.517 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:00:55.705 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:01:01.428 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:01:01.432 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:01:01.585 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:01:01.586 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:01:19.576 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:01:19.806 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:01:49.591 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:01:49.746 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:02:19.912 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:02:20.074 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:02:49.687 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:02:49.827 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:03:19.882 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:03:20.020 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:03:49.606 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:03:49.758 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:04:19.633 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:04:19.805 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:04:49.664 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:04:49.826 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:05:19.597 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:05:19.729 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:05:49.758 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:05:49.925 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:06:19.622 [http-nio-9094-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 16:06:19.629 [http-nio-9094-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 5 ms. Found 0 MongoDB repository interfaces.
2025-08-04 16:06:19.630 [http-nio-9094-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:06:19.635 [http-nio-9094-exec-6] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 Redis repository interfaces.
2025-08-04 16:06:19.849 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:06:20.017 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:06:49.492 [http-nio-9094-exec-8] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 16:06:49.497 [http-nio-9094-exec-8] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 4 ms. Found 0 MongoDB repository interfaces.
2025-08-04 16:06:49.497 [http-nio-9094-exec-8] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:06:49.500 [http-nio-9094-exec-8] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2 ms. Found 0 Redis repository interfaces.
2025-08-04 16:06:49.712 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:06:49.869 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:07:19.614 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:07:19.750 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:07:49.470 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 16:07:49.474 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 MongoDB repository interfaces.
2025-08-04 16:07:49.474 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:07:49.476 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2 ms. Found 0 Redis repository interfaces.
2025-08-04 16:07:49.630 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:07:49.762 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:07:51.463 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 16:07:51.525 [SpringApplicationShutdownHook] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Shutting down Quartz Scheduler
2025-08-04 16:07:51.525 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutting down.
2025-08-04 16:07:51.525 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED paused.
2025-08-04 16:07:51.525 [SpringApplicationShutdownHook] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED shutdown complete.
2025-08-04 16:08:00.657 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-04 16:08:00.937 [main] INFO  com.sanyth.Application - Starting Application using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 89388 (/Users/<USER>/WorkSpace/sanyth-zsxt/sanyth-zsxt/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-zsxt)
2025-08-04 16:08:00.937 [main] INFO  com.sanyth.Application - The following 1 profile is active: "dev"
2025-08-04 16:08:03.000 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 16:08:03.002 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 16:08:03.055 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 41 ms. Found 0 MongoDB repository interfaces.
2025-08-04 16:08:03.067 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 16:08:03.069 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:08:03.096 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.
2025-08-04 16:08:03.926 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.rpamis.security.starter.autoconfigure.SecurityAutoConfiguration' of type [com.rpamis.security.starter.autoconfigure.SecurityAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 16:08:03.948 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationAspect' of type [com.rpamis.security.starter.aop.DesensitizationAspect] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 16:08:03.950 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationInterceptor' of type [com.rpamis.security.starter.aop.DesensitizationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 16:08:03.964 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rpamis.security-com.rpamis.security.starter.autoconfigure.SecurityProperties' of type [com.rpamis.security.starter.autoconfigure.SecurityProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 16:08:03.973 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationAdvisor' of type [com.rpamis.security.starter.aop.DesensitizationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 16:08:04.743 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9094 (http)
2025-08-04 16:08:04.770 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9094"]
2025-08-04 16:08:04.771 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 16:08:04.771 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-04 16:08:04.926 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 16:08:04.926 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 3899 ms
2025-08-04 16:08:05.412 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 16:08:05.413 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 16:08:05.414 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 16:08:07.968 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-04 16:08:08.419 [cluster-ClusterId{value='68906a67b18669544d3515f4', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21025}] to *************:13337
2025-08-04 16:08:08.420 [cluster-ClusterId{value='68906a67b18669544d3515f4', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=157671333}
2025-08-04 16:08:08.474 [cluster-rtt-ClusterId{value='68906a67b18669544d3515f4', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21026}] to *************:13337
2025-08-04 16:08:14.936 [main] INFO  com.sanyth.core.strategy.ExportStrategyRegistry - 注册导出策略: businessType=three_table_export, strategyClass=ThreeTableExportStrategy
2025-08-04 16:08:19.994 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-04 16:08:20.102 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-04 16:08:20.121 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-04 16:08:20.146 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-04 16:08:20.792 [main] INFO  org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor - Inconsistent constructor declaration on bean with name 'org.springblade.core.launch.server.ServerInfo': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public org.springblade.core.launch.server.ServerInfo(org.springframework.boot.autoconfigure.web.ServerProperties)
2025-08-04 16:08:21.146 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-04 16:08:21.216 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 16:08:21.216 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-08-04 16:08:21.227 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-04 16:08:21.232 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 16:08:21.232 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 16:08:21.232 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-08-04 16:08:21.232 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@211edf34
2025-08-04 16:08:22.152 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-04 16:08:22.248 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-04 16:08:22.252 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-04 16:08:22.255 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-04 16:08:22.583 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-04 16:08:22.603 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-04 16:08:22.609 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-04 16:08:22.609 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-04 16:08:22.609 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-04 16:08:22.609 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-04 16:08:22.610 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-04 16:08:22.610 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-04 16:08:22.611 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-04 16:08:22.615 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-04 16:08:22.616 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-04 16:08:22.616 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
2025-08-04 16:08:22.680 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-04 16:08:22.867 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9094"]
2025-08-04 16:08:22.934 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9094 (http) with context path ''
2025-08-04 16:08:22.940 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-08-04 16:08:22.940 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 16:08:22.947 [task-1] INFO  org.springblade.core.launch.StartEventListener - ---[ZSXT]---启动完成，当前使用的端口:[9094]，环境变量:[dev]---
2025-08-04 16:08:22.975 [main] INFO  com.sanyth.Application - Started Application in 23.074 seconds (JVM running for 30.561)
2025-08-04 16:08:23.010 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-04 16:08:25.920 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-04 16:08:30.253 [RMI TCP Connection(4)-127.0.0.1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 16:08:30.253 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 16:08:30.316 [RMI TCP Connection(4)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 63 ms
2025-08-04 16:08:31.578 [RMI TCP Connection(2)-127.0.0.1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:21027}] to *************:13337
2025-08-04 16:08:40.516 [http-nio-9094-exec-8] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:21028}] to *************:13337
2025-08-04 16:08:40.585 [http-nio-9094-exec-9] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:21029}] to *************:13337
2025-08-04 16:08:50.948 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:51.080 [http-nio-9094-exec-9] INFO  com.sanyth.core.handler.MyAuthenticationSuccessHandler - Success hanlder
2025-08-04 16:08:51.675 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:55.911 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:55.912 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:55.988 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:55.989 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.064 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.064 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.086 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.156 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.157 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.232 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.232 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.558 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.558 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.558 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.701 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:56.706 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.080 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.080 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.080 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.149 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.281 [http-nio-9094-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:6, serverValue:21030}] to *************:13337
2025-08-04 16:08:57.363 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.363 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.363 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.363 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.504 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:57.892 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:58.889 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:08:59.026 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:01.249 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:01.372 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:01.470 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:01.851 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:13.330 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:13.724 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:25.233 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:25.627 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:35.733 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:37.988 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:53.653 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:53.836 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:54.213 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:54.414 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:55.151 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:55.429 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.026 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.175 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.176 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.245 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.245 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.245 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.325 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.325 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.424 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.436 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.436 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.514 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.613 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.670 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.682 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.872 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.872 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.941 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.941 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:09:59.941 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:10:00.083 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:10:00.163 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:10:25.265 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:10:25.448 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:10:37.593 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:10:37.765 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:10:55.242 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:10:55.439 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:11:25.364 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:11:25.564 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:11:55.363 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:11:55.514 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:12:25.291 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:12:25.448 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:12:55.284 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:12:55.545 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:13:19.016 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:13:19.141 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:13:19.268 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:13:19.406 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:13:25.213 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:13:25.351 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:13:27.287 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:13:27.427 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:14:17.539 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:14:17.685 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:14:25.232 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:14:25.385 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:14:55.316 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:14:55.445 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:15:25.308 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:15:25.450 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:15:55.176 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 16:15:55.191 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 MongoDB repository interfaces.
2025-08-04 16:15:55.191 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:15:55.194 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 2 ms. Found 0 Redis repository interfaces.
2025-08-04 16:15:55.456 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:15:55.611 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:16:07.506 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:16:07.669 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:16:25.270 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:16:25.411 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:16:58.121 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:16:59.259 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:17:11.504 [http-nio-9094-exec-10] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 16:17:11.519 [http-nio-9094-exec-10] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 13 ms. Found 0 MongoDB repository interfaces.
2025-08-04 16:17:11.519 [http-nio-9094-exec-10] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:17:11.522 [http-nio-9094-exec-10] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 3 ms. Found 0 Redis repository interfaces.
2025-08-04 16:17:11.696 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:17:11.833 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:17:25.237 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:17:25.377 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:17:56.027 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:17:56.165 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:18:25.294 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:18:25.595 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:18:55.298 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:18:55.697 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:19:25.318 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:19:25.479 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:19:55.536 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:19:55.746 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:25.461 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:25.621 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:25.911 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:25.980 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.042 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.089 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.242 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.305 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.305 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.371 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.381 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.443 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.677 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:26.723 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.106 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.106 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.167 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.167 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.226 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.242 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.242 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.313 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.474 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:27.603 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.781 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.787 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.842 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.857 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.866 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.903 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.936 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.939 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:46.971 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.007 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.041 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.189 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.190 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.234 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.264 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.289 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.340 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.366 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.442 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.478 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.584 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:47.584 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:58.765 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.217 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.220 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.267 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.268 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.268 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.361 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.362 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.402 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.410 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.485 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.595 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.689 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.690 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.707 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.764 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.842 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.842 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:20:59.842 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:00.038 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:00.039 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:00.171 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:00.192 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:00.338 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.743 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.742 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.807 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.807 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.807 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.885 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.886 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.965 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.965 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:14.965 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.023 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.112 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.131 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.132 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.179 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.264 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.264 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.264 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.343 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.424 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.425 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:15.589 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:25.356 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:25.499 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.595 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.595 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.656 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.660 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.663 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.764 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.765 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.837 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.838 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.948 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:46.948 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.110 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.111 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.134 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.362 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.363 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.579 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.579 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.581 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.581 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.741 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:47.741 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:55.293 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:21:55.427 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:22:25.322 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:22:25.703 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:22:55.344 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:22:55.478 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.143 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.143 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.206 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.258 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.258 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.286 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.324 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.362 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.409 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.410 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.495 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.602 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.620 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.622 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.682 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.763 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.889 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.890 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.890 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.890 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:03.969 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:04.037 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:25.342 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:25.476 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.480 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.480 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.545 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.546 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.546 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.587 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.632 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.632 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.682 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.683 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.691 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.723 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.953 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:41.953 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:42.069 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:42.069 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:42.082 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:42.086 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:42.165 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:42.203 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:42.219 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:42.297 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:51.953 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:51.953 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.012 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.012 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.013 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.314 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.459 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.459 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.460 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.461 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.526 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.619 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.817 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.817 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.860 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.938 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.966 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:52.967 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:53.016 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:53.042 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:53.090 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:53.186 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:55.232 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:23:55.362 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.424 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.424 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.499 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.500 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.500 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.500 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.567 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.578 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.662 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.662 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.705 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.705 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:01.843 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.063 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.063 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.063 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.064 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.163 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.221 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.221 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.222 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:02.325 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:25.272 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:25.418 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:46.271 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:46.459 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:49.688 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:49.692 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:49.817 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:49.818 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:49.819 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:49.909 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:49.909 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:49.980 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.277 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.277 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.278 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.279 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.468 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.510 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.746 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.750 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.835 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.835 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.898 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:50.909 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:51.016 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:51.046 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:51.075 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:51.460 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:51.461 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:51.608 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:52.729 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:24:52.868 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.180 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.202 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.242 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.242 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.261 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.261 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.317 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.348 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.396 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.396 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.403 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.403 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.542 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.626 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.681 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.767 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.963 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:01.986 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:02.009 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:02.088 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:02.088 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:02.117 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:02.142 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:02.171 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:02.246 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:02.246 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:03.208 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:03.687 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:04.359 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:06.562 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:11.257 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:11.263 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:11.421 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:11.421 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:30.524 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:30.670 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:43.267 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:43.656 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:54.149 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:25:54.384 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:00.508 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:00.644 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:21.123 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.040 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.039 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.162 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.162 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.250 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.251 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.606 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.606 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.916 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:26.947 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:27.057 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:27.364 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:28.130 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:28.277 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:35.275 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 16:26:35.755 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 463 ms. Found 0 MongoDB repository interfaces.
2025-08-04 16:26:35.755 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 16:26:35.831 [http-nio-9094-exec-4] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 76 ms. Found 0 Redis repository interfaces.
2025-08-04 16:26:37.655 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.SytPermissionRoleMapper method cache
2025-08-04 16:26:37.655 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.system.SytPermissionRoleMapper.getByAccount(java.lang.String)
2025-08-04 16:26:37.696 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.ResourceMapper method cache
2025-08-04 16:26:37.696 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.system.ResourceMapper.getButtonsListByRole(java.lang.String)
2025-08-04 16:26:37.696 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.system.ResourceMapper.getFrontListByRole(java.lang.String)
2025-08-04 16:26:37.696 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.system.ResourceMapper.getListByRole(java.lang.String)
2025-08-04 16:26:38.134 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.SytPermissionAccountMapper method cache
2025-08-04 16:26:38.140 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.SytSysOrganizationMapper method cache
2025-08-04 16:26:38.140 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.system.SytSysOrganizationMapper.getOrganizationByUser(java.lang.String)
2025-08-04 16:26:38.779 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.LoginLogsMapper method cache
2025-08-04 16:26:38.945 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.CodeCcbMapper method cache
2025-08-04 16:26:39.240 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.CodeZslxMapper method cache
2025-08-04 16:26:39.328 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.datawarning.DataWarningTaskSummaryMapper method cache
2025-08-04 16:26:39.348 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.datawarning.DataWarningRuleMapper method cache
2025-08-04 16:26:39.684 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.ksgl.TddMapper method cache
2025-08-04 16:26:39.684 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.ksgl.TddMapper.findAllNf()
2025-08-04 16:26:39.684 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.ksgl.TddMapper.selectByField(com.baomidou.mybatisplus.core.conditions.query.QueryWrapper,java.lang.String)
2025-08-04 16:26:39.740 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.DictGroupMapper method cache
2025-08-04 16:26:39.740 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.system.DictGroupMapper.selectDictGroupList(com.sanyth.model.system.DictGroup)
2025-08-04 16:26:40.654 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.DownloadTaskMapper method cache
2025-08-04 16:26:40.656 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.system.DownloadTaskMapper.selectDownloadTaskPage(com.baomidou.mybatisplus.core.metadata.IPage,com.sanyth.vo.DownloadTaskVO)
2025-08-04 16:26:40.657 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract java.util.List com.sanyth.mapper.system.DownloadTaskMapper.selectPendingTasks()
2025-08-04 16:26:40.658 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - 	 method -> public abstract int com.sanyth.mapper.system.DownloadTaskMapper.countTasksByUserIdAndStatus(java.lang.String,java.lang.String)
2025-08-04 16:26:41.409 [http-nio-9094-exec-4] INFO  com.baomidou.mybatisplus.core.override.MybatisMapperProxyFactory - JRebel: clean MybatisMapperProxy:com.sanyth.mapper.system.CustomColMapper method cache
2025-08-04 16:26:42.412 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:42.496 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:42.637 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:42.698 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:42.734 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:42.889 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:43.051 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:43.053 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:43.122 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:43.210 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:43.211 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:43.473 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:43.549 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:50.088 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:50.608 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:52.402 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:52.730 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:53.345 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:53.588 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:54.157 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:26:54.324 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:27:14.227 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:27:14.384 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:27:23.935 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:27:24.097 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:27:54.095 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:27:54.334 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:28:24.015 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:28:24.901 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:28:54.135 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:28:54.326 [http-nio-9094-exec-9] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:29:05.432 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:29:05.602 [http-nio-9094-exec-8] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:29:24.073 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:29:24.339 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:29:54.025 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:29:54.289 [http-nio-9094-exec-4] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:30:24.124 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:30:24.251 [http-nio-9094-exec-1] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:30:40.827 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:30:40.970 [http-nio-9094-exec-10] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:30:54.087 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:30:54.482 [http-nio-9094-exec-6] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:31:25.880 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:31:26.169 [http-nio-9094-exec-5] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:31:54.370 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:31:54.747 [http-nio-9094-exec-2] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:32:02.162 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
2025-08-04 16:32:02.731 [http-nio-9094-exec-3] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
