2025-08-04 15:37:00.350 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2025-08-04 15:37:00.841 [main] INFO  com.sanyth.Application - Starting Application using Java 1.8.0_452 on paynexcs-MacBook-Pro.local with PID 84074 (/Users/<USER>/WorkSpace/sanyth-zsxt/sanyth-zsxt/target/classes started by paynexc in /Users/<USER>/WorkSpace/sanyth-zsxt)
2025-08-04 15:37:00.843 [main] INFO  com.sanyth.Application - The following 1 profile is active: "dev"
2025-08-04 15:37:03.509 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 15:37:03.511 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data MongoDB repositories in DEFAULT mode.
2025-08-04 15:37:03.572 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 40 ms. Found 0 MongoDB repository interfaces.
2025-08-04 15:37:03.591 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-04 15:37:03.593 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-04 15:37:03.623 [main] INFO  org.springframework.data.repository.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 16 ms. Found 0 Redis repository interfaces.
2025-08-04 15:37:04.526 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'com.rpamis.security.starter.autoconfigure.SecurityAutoConfiguration' of type [com.rpamis.security.starter.autoconfigure.SecurityAutoConfiguration$$EnhancerBySpringCGLIB$$1] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:04.553 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationAspect' of type [com.rpamis.security.starter.aop.DesensitizationAspect] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:04.555 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationInterceptor' of type [com.rpamis.security.starter.aop.DesensitizationInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:04.569 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'rpamis.security-com.rpamis.security.starter.autoconfigure.SecurityProperties' of type [com.rpamis.security.starter.autoconfigure.SecurityProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:04.579 [main] INFO  org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker - Bean 'desensitizationAdvisor' of type [com.rpamis.security.starter.aop.DesensitizationAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-04 15:37:05.384 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat initialized with port(s): 9094 (http)
2025-08-04 15:37:05.410 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-9094"]
2025-08-04 15:37:05.411 [main] INFO  org.apache.catalina.core.StandardService - Starting service [Tomcat]
2025-08-04 15:37:05.411 [main] INFO  org.apache.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.60]
2025-08-04 15:37:05.571 [main] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-08-04 15:37:05.571 [main] INFO  org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 4609 ms
2025-08-04 15:37:06.071 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final org.springframework.web.servlet.HandlerExecutionChain org.springframework.web.servlet.handler.AbstractHandlerMapping.getHandler(javax.servlet.http.HttpServletRequest) throws java.lang.Exception] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 15:37:06.072 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.web.context.support.WebApplicationObjectSupport.setServletContext(javax.servlet.ServletContext)] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 15:37:06.072 [main] INFO  org.springframework.aop.framework.CglibAopProxy - Unable to proxy interface-implementing method [public final void org.springframework.context.support.ApplicationObjectSupport.setApplicationContext(org.springframework.context.ApplicationContext) throws org.springframework.beans.BeansException] because it is marked as final: Consider using interface-based JDK proxies instead!
2025-08-04 15:37:08.572 [main] INFO  org.mongodb.driver.cluster - Cluster created with settings {hosts=[*************:13337], mode=SINGLE, requiredClusterType=UNKNOWN, serverSelectionTimeout='30000 ms'}
2025-08-04 15:37:08.983 [cluster-rtt-ClusterId{value='68906324033b897012899c2a', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:1, serverValue:21005}] to *************:13337
2025-08-04 15:37:09.894 [cluster-ClusterId{value='68906324033b897012899c2a', description='null'}-*************:13337] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:2, serverValue:21007}] to *************:13337
2025-08-04 15:37:09.896 [cluster-ClusterId{value='68906324033b897012899c2a', description='null'}-*************:13337] INFO  org.mongodb.driver.cluster - Monitor thread successfully connected to server with description ServerDescription{address=*************:13337, type=STANDALONE, state=CONNECTED, ok=true, minWireVersion=0, maxWireVersion=4, maxDocumentSize=16777216, logicalSessionTimeoutMinutes=null, roundTripTimeNanos=75716792}
2025-08-04 15:37:16.406 [main] INFO  com.sanyth.core.strategy.ExportStrategyRegistry - 注册导出策略: businessType=three_table_export, strategyClass=ThreeTableExportStrategy
2025-08-04 15:37:21.342 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will secure Ant [pattern='classpath:/static'] with []
2025-08-04 15:37:21.444 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/oauth/token'], Ant [pattern='/oauth/token_key'], Ant [pattern='/oauth/check_token']]
2025-08-04 15:37:21.462 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure Or [Ant [pattern='/hi'], Ant [pattern='/user/me'], Ant [pattern='user/detail']]
2025-08-04 15:37:21.485 [main] INFO  org.springframework.security.web.DefaultSecurityFilterChain - Will not secure any request
2025-08-04 15:37:22.129 [main] INFO  org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor - Inconsistent constructor declaration on bean with name 'org.springblade.core.launch.server.ServerInfo': single autowire-marked constructor flagged as optional - this constructor is effectively required since there is no default constructor to fall back to: public org.springblade.core.launch.server.ServerInfo(org.springframework.boot.autoconfigure.web.ServerProperties)
2025-08-04 15:37:22.461 [main] INFO  org.quartz.impl.StdSchedulerFactory - Using default implementation for ThreadExecutor
2025-08-04 15:37:22.529 [main] INFO  org.quartz.core.SchedulerSignalerImpl - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
2025-08-04 15:37:22.530 [main] INFO  org.quartz.core.QuartzScheduler - Quartz Scheduler v.2.3.2 created.
2025-08-04 15:37:22.541 [main] INFO  org.quartz.simpl.RAMJobStore - RAMJobStore initialized.
2025-08-04 15:37:22.546 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'quartzScheduler' with instanceId 'NON_CLUSTERED'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 10 threads.
  Using job-store 'org.quartz.simpl.RAMJobStore' - which does not support persistence. and is not clustered.

2025-08-04 15:37:22.546 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler 'quartzScheduler' initialized from an externally provided properties instance.
2025-08-04 15:37:22.546 [main] INFO  org.quartz.impl.StdSchedulerFactory - Quartz scheduler version: 2.3.2
2025-08-04 15:37:22.546 [main] INFO  org.quartz.core.QuartzScheduler - JobFactory set to: org.springframework.scheduling.quartz.SpringBeanJobFactory@142b9d98
2025-08-04 15:37:23.453 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 未配置集群通知服务，本实例不会推送通知，集群环境下可能会有问题，如需开启，请引用magic-api-plugin-cluster插件
2025-08-04 15:37:23.556 [main] INFO  org.ssssssss.magicapi.datasource.model.MagicDynamicDataSource - 注册数据源：default
2025-08-04 15:37:23.559 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到分页实现,采用默认分页实现,分页配置:(页码=page,页大小=pageSize,默认首页=1,默认页大小=10,最大页大小=-1)
2025-08-04 15:37:23.562 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicModuleConfiguration - 未找到SQL缓存实现，采用默认缓存实现(LRU+TTL)，缓存配置:(容量=10000,TTL=-1)
2025-08-04 15:37:23.851 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - magic-api工作目录:db://magic_api_file//magic-api
2025-08-04 15:37:23.870 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:log -> interface org.slf4j.Logger
2025-08-04 15:37:23.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:db -> class org.ssssssss.magicapi.modules.db.SQLModule
2025-08-04 15:37:23.876 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:http -> class org.ssssssss.magicapi.modules.http.HttpModule
2025-08-04 15:37:23.877 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:env -> class org.ssssssss.magicapi.modules.spring.EnvModule
2025-08-04 15:37:23.877 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:request -> class org.ssssssss.magicapi.modules.servlet.RequestModule
2025-08-04 15:37:23.877 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:response -> class org.ssssssss.magicapi.modules.servlet.ResponseModule
2025-08-04 15:37:23.877 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册模块:magic -> class org.ssssssss.magicapi.core.service.impl.DefaultMagicAPIService
2025-08-04 15:37:23.878 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：log
2025-08-04 15:37:23.882 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导入模块：db
2025-08-04 15:37:23.882 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 自动导包：cn.hutool.core.*
2025-08-04 15:37:23.882 [main] INFO  org.ssssssss.magicapi.spring.boot.starter.MagicAPIAutoConfiguration - 注册扩展:class org.ssssssss.magicapi.modules.servlet.ResponseModule -> class org.ssssssss.magicapi.servlet.javaee.MagicJavaEEResponseExtension
2025-08-04 15:37:23.945 [main] INFO  org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver - Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-04 15:37:24.125 [main] INFO  org.apache.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-9094"]
2025-08-04 15:37:24.193 [main] INFO  org.springframework.boot.web.embedded.tomcat.TomcatWebServer - Tomcat started on port(s): 9094 (http) with context path ''
2025-08-04 15:37:24.199 [main] INFO  org.springframework.scheduling.quartz.SchedulerFactoryBean - Starting Quartz Scheduler now
2025-08-04 15:37:24.200 [main] INFO  org.quartz.core.QuartzScheduler - Scheduler quartzScheduler_$_NON_CLUSTERED started.
2025-08-04 15:37:24.206 [task-1] INFO  org.springblade.core.launch.StartEventListener - ---[ZSXT]---启动完成，当前使用的端口:[9094]，环境变量:[dev]---
2025-08-04 15:37:24.235 [main] INFO  com.sanyth.Application - Started Application in 24.631 seconds (JVM running for 32.131)
2025-08-04 15:37:24.270 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Starting...
2025-08-04 15:37:26.681 [main] INFO  com.zaxxer.hikari.HikariDataSource - DatebookHikariCP - Start completed.
2025-08-04 15:37:30.714 [RMI TCP Connection(8)-127.0.0.1] INFO  org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04 15:37:30.715 [RMI TCP Connection(8)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-04 15:37:30.728 [RMI TCP Connection(8)-127.0.0.1] INFO  org.springframework.web.servlet.DispatcherServlet - Completed initialization in 13 ms
2025-08-04 15:37:32.521 [RMI TCP Connection(5)-127.0.0.1] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:3, serverValue:21008}] to *************:13337
2025-08-04 15:37:42.243 [http-nio-9094-exec-10] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:4, serverValue:21009}] to *************:13337
2025-08-04 15:37:42.655 [http-nio-9094-exec-6] INFO  org.mongodb.driver.connection - Opened connection [connectionId{localValue:5, serverValue:21010}] to *************:13337
2025-08-04 15:37:43.116 [http-nio-9094-exec-7] INFO  com.sanyth.service.system.RoleResourceServiceImpl - 缓存获取权限
