2025-08-04 15:37:07.354 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.dto.StatisticsDto".
2025-08-04 15:37:07.354 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.dto.StatisticsDto ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 15:37:09.937 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.model.system.RoleResourceLink".
2025-08-04 15:37:09.937 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.model.system.RoleResourceLink ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 15:37:09.954 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.model.system.RoleResource".
2025-08-04 15:37:09.954 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.model.system.RoleResource ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 15:37:10.025 [main] WARN  com.sanyth.filter.MyFilterSecurityInterceptor - Could not validate configuration attributes as the SecurityMetadataSource did not return any attributes from getAllConfigAttributes()
2025-08-04 15:37:10.059 [main] WARN  com.baomidou.mybatisplus.core.metadata.TableInfoHelper - Can not find table primary key in Class: "com.sanyth.model.system.SytSysOrganizationUser".
2025-08-04 15:37:10.059 [main] WARN  com.baomidou.mybatisplus.core.injector.DefaultSqlInjector - class com.sanyth.model.system.SytSysOrganizationUser ,Not found @TableId annotation, Cannot use Mybatis-Plus 'xxById' Method.
2025-08-04 15:37:21.340 [main] WARN  org.springframework.security.config.annotation.web.builders.WebSecurity - You are asking Spring Security to ignore Ant [pattern='classpath:/static']. This is not recommended -- please use permitAll via HttpSecurity#authorizeHttpRequests instead.
2025-08-04 15:37:22.082 [main] WARN  org.thymeleaf.templatemode.TemplateMode - [THYMELEAF][main] Template Mode 'HTML5' is deprecated. Using Template Mode 'HTML' instead.
2025-08-04 15:37:24.270 [main] WARN  com.zaxxer.hikari.HikariConfig - DatebookHikariCP - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04 15:37:24.288 [main] WARN  com.zaxxer.hikari.util.DriverDataSource - Registered driver with driverClassName=oracle.jdbc.driver.OracleDriver was not found, trying direct instantiation.
