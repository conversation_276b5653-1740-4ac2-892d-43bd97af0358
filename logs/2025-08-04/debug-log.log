[2m2025-08-04 15:37:29.676[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, RU<PERSON>TYP<PERSON>, SETNAME, SETCODE, SETDESC, ENABLEFLAG, CREATETIME, CREATEUSER, CHECKCRON, PUSHNOW, CONTENT, PUSHCHANNEL, ROLES, ORGANIZATIONS, SCOPE, ISSAVELIST FROM DATA_WARNING_RULE WHERE (enableFlag = ?) ORDER BY enableFlag ASC, ruleType ASC, setName ASC
[2m2025-08-04 15:37:29.676[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, HUM<PERSON><PERSON><PERSON>, HUM<PERSON><PERSON><PERSON>OD<PERSON>, H<PERSON>ANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, EMPLOYEETYPE, ORGANIZATIONNAMES, ORGSHORTNAME, ACTIVEFLAG, AVATARID, AVATARURL, DORMITORYINFO, MZMC FROM SYT_PERMISSION_ACCOUNT WHERE (employeeType = ?)
[2m2025-08-04 15:37:29.701[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==> Parameters: teacher(String)
[2m2025-08-04 15:37:29.705[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m ==> Parameters: 0(Integer)
[2m2025-08-04 15:37:29.847[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:37:29.901[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:37:29.926[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, TASKNAME, EXECCRON, RULEIDS, ENABLEFLAG, CREATEUSER, CREATETIME, TASKDESC FROM DATA_WARNING_TASK_SUMMARY WHERE (enableflag = ?)
[2m2025-08-04 15:37:29.927[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m ==> Parameters: 0(Integer)
[2m2025-08-04 15:37:29.930[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==>  Preparing: SELECT T.* FROM SYT_PERMISSION_ROLE T WHERE EXISTS (SELECT 1 FROM SYT_PERMISSION_ACCOUNT_ROLE WHERE T.ID = ROLE_ID AND ACCOUNT_ID = ?)
[2m2025-08-04 15:37:29.930[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:37:30.009[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:37:30.178[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m <==      Total: 1
