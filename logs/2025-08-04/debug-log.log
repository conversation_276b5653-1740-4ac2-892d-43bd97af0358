[2m2025-08-04 15:37:29.676[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, RU<PERSON>TYP<PERSON>, SETNAME, SETCODE, SETDESC, ENABLEFLAG, CREATETIME, CREATEUSER, CHECKCRON, PUSHNOW, CONTENT, PUSHCHANNEL, ROLES, ORGANIZATIONS, SCOPE, ISSAVELIST FROM DATA_WARNING_RULE WHERE (enableFlag = ?) ORDER BY enableFlag ASC, ruleType ASC, setName ASC
[2m2025-08-04 15:37:29.676[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, HUM<PERSON><PERSON><PERSON>, HUM<PERSON><PERSON><PERSON>OD<PERSON>, H<PERSON>ANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, EMPLOYEETYPE, ORGANIZATIONNAMES, ORGSHORTNAME, ACTIVEFLAG, AVATARID, AVATARURL, DORMITORYINFO, MZMC FROM SYT_PERMISSION_ACCOUNT WHERE (employeeType = ?)
[2m2025-08-04 15:37:29.701[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==> Parameters: teacher(String)
[2m2025-08-04 15:37:29.705[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m ==> Parameters: 0(Integer)
[2m2025-08-04 15:37:29.847[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:37:29.901[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:37:29.926[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, TASKNAME, EXECCRON, RULEIDS, ENABLEFLAG, CREATEUSER, CREATETIME, TASKDESC FROM DATA_WARNING_TASK_SUMMARY WHERE (enableflag = ?)
[2m2025-08-04 15:37:29.927[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m ==> Parameters: 0(Integer)
[2m2025-08-04 15:37:29.930[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==>  Preparing: SELECT T.* FROM SYT_PERMISSION_ROLE T WHERE EXISTS (SELECT 1 FROM SYT_PERMISSION_ACCOUNT_ROLE WHERE T.ID = ROLE_ID AND ACCOUNT_ID = ?)
[2m2025-08-04 15:37:29.930[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:37:30.009[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[           main][0;39m [36mc.s.m.d.D.selectList                    [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:37:30.178[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[         task-2][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:38:59.735[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, HUMANCODE, HUMANSMSCODE, HUMANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, EMPLOYEETYPE, ORGANIZATIONNAMES, ORGSHORTNAME, ACTIVEFLAG, AVATARID, AVATARURL, DORMITORYINFO, MZMC FROM SYT_PERMISSION_ACCOUNT WHERE (humancode = ? OR email = ? OR telmobile1 = ? OR idcode = ?)
[2m2025-08-04 15:38:59.736[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==> Parameters: 10001(String), 10001(String), 10001(String), 10001(String)
[2m2025-08-04 15:38:59.821[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:38:59.876[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, HUMANCODE, HUMANSMSCODE, HUMANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, EMPLOYEETYPE, ORGANIZATIONNAMES, ORGSHORTNAME, ACTIVEFLAG, AVATARID, AVATARURL, DORMITORYINFO, MZMC FROM SYT_PERMISSION_ACCOUNT WHERE (humancode = ? OR email = ? OR telmobile1 = ? OR idcode = ?)
[2m2025-08-04 15:38:59.876[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==> Parameters: 10001(String), 10001(String), 10001(String), 10001(String)
[2m2025-08-04 15:38:59.957[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:38:59.959[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==>  Preparing: SELECT T.* FROM SYT_PERMISSION_ROLE T WHERE EXISTS (SELECT 1 FROM SYT_PERMISSION_ACCOUNT_ROLE WHERE T.ID = ROLE_ID AND ACCOUNT_ID = ?)
[2m2025-08-04 15:38:59.959[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:39:00.107[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:00.137[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:00.137[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:39:00.285[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:00.338[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.system.LoginLogsMapper.insert     [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO sys_login_logs ( id, human_code, ip, login_time, user_agent ) VALUES ( ?, ?, ?, ?, ? )
[2m2025-08-04 15:39:00.399[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.system.LoginLogsMapper.insert     [0;39m [2m:[0;39m ==> Parameters: 1952273046140096513(String), 10001(String), 127.0.0.1(String), 2025-08-04 15:39:00.295(Timestamp), Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36(String)
[2m2025-08-04 15:39:00.551[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.system.LoginLogsMapper.insert     [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-04 15:39:03.216[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:03.217[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:03.217[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:39:03.218[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:39:03.292[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:03.292[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:03.327[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==>  Preparing: SELECT T.* FROM SYT_PERMISSION_ROLE T WHERE EXISTS (SELECT 1 FROM SYT_PERMISSION_ACCOUNT_ROLE WHERE T.ID = ROLE_ID AND ACCOUNT_ID = ?)
[2m2025-08-04 15:39:03.328[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:39:03.380[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m ==>  Preparing: SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND r.TYPE = 1 AND r.STATUS = '是' AND r.IS_BTN = '否' ORDER BY r.sort
[2m2025-08-04 15:39:03.381[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m ==> Parameters: 1496003057396236290(String)
[2m2025-08-04 15:39:03.465[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:03.483[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:03.484[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:39:03.495[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:03.496[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:39:03.527[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:39:03.575[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:03.579[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:03.606[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m ==>  Preparing: SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '否' ORDER BY r.sort
[2m2025-08-04 15:39:03.607[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m ==> Parameters: 1496003057396236290(String)
[2m2025-08-04 15:39:03.650[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:03.650[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: 10001(String), 0(String)
[2m2025-08-04 15:39:03.780[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:03.782[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:03.783[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: 10001(String), 1(String)
[2m2025-08-04 15:39:03.844[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:03.847[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:03.847[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: 10001(String), 2(String)
[2m2025-08-04 15:39:03.910[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:03.912[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:03.912[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: 10001(String), 3(String)
[2m2025-08-04 15:39:03.990[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:03.992[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:03.992[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: 10001(String), 4(String)
[2m2025-08-04 15:39:04.084[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:04.233[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m <==      Total: 48
[2m2025-08-04 15:39:05.332[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:05.333[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: A5E9D900781D5D18E05359EB11AC6F0C(String)
[2m2025-08-04 15:39:05.396[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:05.433[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m ==>  Preparing: SELECT t.* FROM (SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r WHERE r.id IN (SELECT r.parent_id FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '是') UNION ALL SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '是') t
[2m2025-08-04 15:39:05.433[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m ==> Parameters: 1496003057396236290(String), 1496003057396236290(String)
[2m2025-08-04 15:39:06.968[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m <==      Total: 175
[2m2025-08-04 15:39:25.054[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, HUMANCODE, HUMANSMSCODE, HUMANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, EMPLOYEETYPE, ORGANIZATIONNAMES, ORGSHORTNAME, ACTIVEFLAG, AVATARID, AVATARURL, DORMITORYINFO, MZMC FROM SYT_PERMISSION_ACCOUNT WHERE (humancode = ? OR email = ? OR telmobile1 = ? OR idcode = ?)
[2m2025-08-04 15:39:25.057[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), sytadmin(String), sytadmin(String), sytadmin(String)
[2m2025-08-04 15:39:25.132[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:25.148[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, HUMANCODE, HUMANSMSCODE, HUMANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, EMPLOYEETYPE, ORGANIZATIONNAMES, ORGSHORTNAME, ACTIVEFLAG, AVATARID, AVATARURL, DORMITORYINFO, MZMC FROM SYT_PERMISSION_ACCOUNT WHERE (humancode = ? OR email = ? OR telmobile1 = ? OR idcode = ?)
[2m2025-08-04 15:39:25.149[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), sytadmin(String), sytadmin(String), sytadmin(String)
[2m2025-08-04 15:39:25.222[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:25.225[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==>  Preparing: SELECT T.* FROM SYT_PERMISSION_ROLE T WHERE EXISTS (SELECT 1 FROM SYT_PERMISSION_ACCOUNT_ROLE WHERE T.ID = ROLE_ID AND ACCOUNT_ID = ?)
[2m2025-08-04 15:39:25.225[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:25.365[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:25.370[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:25.371[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:25.447[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:33.688[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, HUMANCODE, HUMANSMSCODE, HUMANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, EMPLOYEETYPE, ORGANIZATIONNAMES, ORGSHORTNAME, ACTIVEFLAG, AVATARID, AVATARURL, DORMITORYINFO, MZMC FROM SYT_PERMISSION_ACCOUNT WHERE (humancode = ? OR email = ? OR telmobile1 = ? OR idcode = ?)
[2m2025-08-04 15:39:33.689[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), sytadmin(String), sytadmin(String), sytadmin(String)
[2m2025-08-04 15:39:33.765[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:33.777[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==>  Preparing: SELECT id, HUMANCODE, HUMANSMSCODE, HUMANNAME, HUMANDESCRIPTION, CREATEDATE, VALIDFROMDATE, VALIDTODATE, VALIDFLAG, HUMANPASSWORD, SEX, BIRTHDAY, TELOFFICE, TELHOME, TELMOBILE1, TELMOBILE2, EMAIL, ADDRESS, POSTALCODE, AGE, ORGID, SIGNATURE, ENCRYPTYPE, IDCODE, IDTYPE, LOGINTIME, LOGININFO, DUTYID, HUMANNUMBER, DISPLAYORDER, EMPLOYEETYPE, ORGANIZATIONNAMES, ORGSHORTNAME, ACTIVEFLAG, AVATARID, AVATARURL, DORMITORYINFO, MZMC FROM SYT_PERMISSION_ACCOUNT WHERE (humancode = ? OR email = ? OR telmobile1 = ? OR idcode = ?)
[2m2025-08-04 15:39:33.777[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), sytadmin(String), sytadmin(String), sytadmin(String)
[2m2025-08-04 15:39:34.428[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.selectList                    [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:34.430[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==>  Preparing: SELECT T.* FROM SYT_PERMISSION_ROLE T WHERE EXISTS (SELECT 1 FROM SYT_PERMISSION_ACCOUNT_ROLE WHERE T.ID = ROLE_ID AND ACCOUNT_ID = ?)
[2m2025-08-04 15:39:34.431[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:34.577[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:34.586[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:34.587[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:34.665[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:34.670[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.LoginLogsMapper.insert     [0;39m [2m:[0;39m ==>  Preparing: INSERT INTO sys_login_logs ( id, human_code, ip, login_time, user_agent ) VALUES ( ?, ?, ?, ?, ? )
[2m2025-08-04 15:39:34.674[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.LoginLogsMapper.insert     [0;39m [2m:[0;39m ==> Parameters: 1952273190147330049(String), sytadmin(String), 127.0.0.1(String), 2025-08-04 15:39:34.666(Timestamp), Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36(String)
[2m2025-08-04 15:39:34.771[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.LoginLogsMapper.insert     [0;39m [2m:[0;39m <==    Updates: 1
[2m2025-08-04 15:39:36.819[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:36.819[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:36.819[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:36.819[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:36.888[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:36.898[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:36.906[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m ==>  Preparing: SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND r.TYPE = 1 AND r.STATUS = '是' AND r.IS_BTN = '否' ORDER BY r.sort
[2m2025-08-04 15:39:36.906[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m ==> Parameters: A601530922D36D12E05359EB11ACB7BE(String)
[2m2025-08-04 15:39:36.928[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==>  Preparing: SELECT T.* FROM SYT_PERMISSION_ROLE T WHERE EXISTS (SELECT 1 FROM SYT_PERMISSION_ACCOUNT_ROLE WHERE T.ID = ROLE_ID AND ACCOUNT_ID = ?)
[2m2025-08-04 15:39:36.929[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:36.973[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:39:37.065[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:37.118[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:37.118[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:37.194[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:37.198[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:37.199[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:39:37.290[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:37.296[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:37.297[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:39:37.374[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:37.378[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:37.378[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:39:37.383[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:37.384[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:37.459[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:37.459[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:37.463[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:37.463[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:39:37.469[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m ==>  Preparing: SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '否' ORDER BY r.sort
[2m2025-08-04 15:39:37.469[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m ==> Parameters: A601530922D36D12E05359EB11ACB7BE(String)
[2m2025-08-04 15:39:37.524[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:37.527[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:39:37.528[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:39:37.604[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:38.289[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m <==      Total: 79
[2m2025-08-04 15:39:38.805[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:39:38.805[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:39:38.901[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:38.918[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m ==>  Preparing: SELECT t.* FROM (SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r WHERE r.id IN (SELECT r.parent_id FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '是') UNION ALL SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '是') t
[2m2025-08-04 15:39:38.919[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m ==> Parameters: A601530922D36D12E05359EB11ACB7BE(String), A601530922D36D12E05359EB11ACB7BE(String)
[2m2025-08-04 15:39:41.262[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m <==      Total: 219
[2m2025-08-04 15:39:41.979[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT nf FROM T_TDD ORDER BY nf DESC
[2m2025-08-04 15:39:41.980[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:39:42.088[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:42.110[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, cc, zslx, lqs FROM SYT_CODE_ZSLX ORDER BY nf DESC
[2m2025-08-04 15:39:42.111[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:39:42.188[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT klmc FROM t_tdd WHERE klmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:39:42.189[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT pcmc FROM t_tdd WHERE pcmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:39:42.189[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT sfmc FROM t_tdd WHERE sfmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:39:42.189[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:39:42.189[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:39:42.189[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:39:42.189[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, code, name, bz, is_search FROM SYT_CODE_CCB WHERE (is_search = ?)
[2m2025-08-04 15:39:42.190[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 是(String)
[2m2025-08-04 15:39:42.192[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 15:39:42.271[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 15:39:42.271[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:42.281[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:42.283[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:39:42.496[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT xymc FROM t_tdd WHERE xymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:39:42.497[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:39:42.576[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:39:42.622[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT zymc FROM t_tdd WHERE zymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:39:42.623[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:39:42.698[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 15:39:42.741[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_DICT_GROUP ORDER BY sort
[2m2025-08-04 15:39:42.742[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:39:42.838[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:42.839[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1760905501123649537(String)
[2m2025-08-04 15:39:42.846[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT id, fielden, fieldzh, sort, type, sfss, field_type, field_attribute, field_data FROM SYT_CUSTOM_COL WHERE (type = ?)
[2m2025-08-04 15:39:42.846[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-04 15:39:43.262[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 13
[2m2025-08-04 15:39:43.262[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:43.262[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719039487111169(String)
[2m2025-08-04 15:39:43.294[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM T_TDD WHERE (nf = ?)
[2m2025-08-04 15:39:43.295[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:39:43.399[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 10
[2m2025-08-04 15:39:43.400[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:43.400[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719076044664834(String)
[2m2025-08-04 15:39:43.528[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 11
[2m2025-08-04 15:39:43.529[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:43.529[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719152070619137(String)
[2m2025-08-04 15:39:43.630[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:39:43.648[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT id, nf, sfdm, sfmc, ksh, bh, bmdw, bmdwmc, bylbdm, bylbmc, bylbmcxz, cj, csny, dqdm, dqmc, dqtddw, dxtj, hkdj, hkkh, jhxzdm, jhxz, kldm, klmc, klmcxz, ksjlhcf, kslbdm, kslbmc, kslbmcxz, kslsczbz, kslxdm, kslxmc, kstc, kstz, kstzmc, kszg, kszt, lqlxdm, lqlxmc, lqzy, lqzydm, lqzymc, zylbdm, zylbmc, lqzkfx, mzdm, mzmc, ccdm, cc, zslx, pcdm, pcmc, pcmcxz, sdbz, tdcj, tddwdm, tddwmc, tddwdm1, tddwdm2, tddwdm3, tddwdm4, tddwdm5, tddwdm6, tdyydm, tdyymc, tdzy, tjhg, tjjldm, tzcj, wyks, wytl, wyyzdm, wyyzmc, xbdm, xbmc, csrq, xm, xtdw, ysjzdm, ysjzmc, ytzy, ytzymc, yxdrcj, zgf, zkzh, zsyj, zxdm, zxmc, zyzy1, zydh1, zydm1, zyzy2, zydh2, zydm2, zyzy3, zydh3, zydm3, zyzy4, zydh4, zydm4, zyzy5, zydh5, zydm5, zyzy6, zydh6, zydm6, zyhg, zysxdm1, zysxdm2, zysxdm3, zysxdm4, zysxdm5, zysxdm6, zytjfc, zytz, zyytfjcj, zyytjzcj, zyzytj, zzmmdm, zzmmmc, kslqzy, ksytzy, xydm, xymc, xyxz, zydm, gjzydm, zyxq, zysfsf, zytjsx, zybddd, zyrxrq, zymc, bjdm, bjmc, xh, jtdz, yzbm, lxdh, lxsj, lqtzsyjdz, sjr, lqtzsh, lqtzssxh, kddh, yhkh, tjjlmc, yzmc, syddm, sydmc, xq, sfydy, task_tag, bz, photo, tjxx, bmxx, cjyzyxx, sfz, fjb, wbdwj, hyzp, xjzp, hysfzzp, hysfztxzp, dzqm, byxxdm, byxxmc, byxxdz, byxxp, byxxc, byxxd, byxxppzt, zjlxdm, zjlxmc, zjhm, xjh, txdz, sxpdkhyj, hjlbdm, hjlbmc, czlbdm, czlbmc, xkkm, xkkmhg, skkm, hjdm, hjmc, bj, tag, ljzyh, jhlbdm, jhlbmc, tdlxdm, tdlxmc, tdsj, yxdycj, lqsj, skkmhg, lqxh, tddbh, xzbh, zyfpfs, tddzt, peer_lqxh, score_equ, focused_mul, zycjxdm, zycj, zycjtfpw, zytj, zytjlx, zydh_mul, bdzt, bdztbz, section, pro_line, cjyw, cjsx, cjwy, cjzhzh, cjlsh, cjdl, cjwl, cjhx, cjsw, cjjsh, cjzh, ckzyzs, ckzyjn, cjzf, bqmx, ykszt, klmckz, zymckz, xyxzkz, pcmckz, kslbmckz, bylbmckz, lqtzsdyzt FROM T_TDD WHERE (nf = ?) ORDER BY nf DESC, lqtzssxh ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 15:39:43.650[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==> Parameters: 2024(String), 10(Long), 0(Long)
[2m2025-08-04 15:39:43.684[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 14
[2m2025-08-04 15:39:43.684[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:43.684[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719558465122306(String)
[2m2025-08-04 15:39:43.750[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 8
[2m2025-08-04 15:39:43.751[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:43.751[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719602580811778(String)
[2m2025-08-04 15:39:43.825[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:39:43.828[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:43.828[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719653998784513(String)
[2m2025-08-04 15:39:43.904[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:39:43.905[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:43.905[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719695111352321(String)
[2m2025-08-04 15:39:44.013[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m <==      Total: 10
[2m2025-08-04 15:39:44.165[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 30
[2m2025-08-04 15:39:44.166[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:44.166[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719747775033345(String)
[2m2025-08-04 15:39:44.364[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 22
[2m2025-08-04 15:39:44.364[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:44.365[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719794872872962(String)
[2m2025-08-04 15:39:44.426[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 5
[2m2025-08-04 15:39:44.491[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:39:44.492[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719849163943938(String)
[2m2025-08-04 15:39:45.076[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 65
[2m2025-08-04 15:39:45.077[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m <==      Total: 11
[2m2025-08-04 15:40:00.121[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==>  Preparing: SELECT id, task_name, task_type, task_status, task_desc, task_params, file_path, file_name, file_size, file_id, create_user_id, create_user_name, create_time, start_time, finish_time, expire_time, progress, error_msg, retry_count, max_retry_count, remark, update_time FROM syt_download_task WHERE task_status = '0' ORDER BY create_time ASC FETCH FIRST 50 ROWS ONLY
[2m2025-08-04 15:40:00.128[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:40:00.359[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:40:06.763[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:40:06.764[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:40:06.831[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:40:06.833[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:06.833[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:40:06.896[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:06.898[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:06.898[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:40:06.964[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:06.966[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:06.966[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:40:07.044[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:07.046[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:07.047[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:40:07.123[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:07.125[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:07.126[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:40:07.204[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:36.861[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:40:36.862[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:40:36.942[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:40:36.947[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:36.947[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:40:37.024[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:37.025[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:37.026[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:40:37.092[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:37.094[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:37.095[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:40:37.402[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:37.403[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:37.404[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:40:37.467[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:40:37.469[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:40:37.470[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:40:37.542[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:06.714[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:41:06.715[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:41:06.790[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:41:06.794[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:06.794[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:41:06.865[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:06.867[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:06.867[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:41:06.926[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:06.929[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:06.929[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:41:06.989[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:06.990[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:06.991[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:41:07.059[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:07.062[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:07.063[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:41:07.125[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:36.737[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:41:36.739[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:41:36.811[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:41:36.813[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:36.813[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:41:36.884[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:36.886[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:36.886[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:41:36.968[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:36.969[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:36.970[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:41:37.048[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:37.049[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:37.050[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:41:37.124[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:41:37.127[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:41:37.127[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:41:37.203[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:07.286[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:42:07.287[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:42:07.366[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:42:07.369[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:07.369[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:42:07.428[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:07.430[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:07.430[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:42:07.502[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:07.504[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:07.504[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:42:07.581[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:07.589[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:07.590[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:42:07.660[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:07.665[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:07.666[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:42:07.730[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:36.763[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:42:36.764[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:42:36.835[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:42:36.838[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:36.838[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:42:36.902[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:36.904[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:36.905[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:42:37.208[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:37.211[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:37.213[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:42:37.286[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:37.287[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:37.288[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:42:37.605[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:42:37.687[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:42:37.688[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:42:37.763[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:07.396[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:43:07.401[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:43:07.497[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:43:07.510[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:07.511[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:43:07.636[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:07.638[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:07.639[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:43:07.743[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:07.773[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:07.782[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:43:07.856[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:07.860[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:07.861[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:43:07.954[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:07.959[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:07.960[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:43:08.048[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:36.934[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:43:36.943[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:43:37.019[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:43:37.029[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:37.031[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:43:37.148[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:37.151[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:37.151[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:43:37.214[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:37.217[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:37.218[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:43:37.283[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:37.287[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:37.288[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:43:37.364[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:43:37.371[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:43:37.373[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:43:37.471[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:06.902[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:44:06.907[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:44:06.983[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:44:06.992[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:06.993[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:44:07.065[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:07.068[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:07.069[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:44:07.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:07.387[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:07.388[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:44:07.451[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:07.454[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:07.455[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:44:07.532[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:07.535[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:07.537[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:44:07.616[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:36.812[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:44:36.813[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:44:36.885[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:44:36.888[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:36.888[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:44:36.965[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:36.968[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:36.969[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:44:37.054[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:37.056[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:37.057[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:44:37.390[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:37.459[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:37.460[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:44:37.534[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:44:37.537[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:44:37.537[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:44:37.610[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:00.099[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==>  Preparing: SELECT id, task_name, task_type, task_status, task_desc, task_params, file_path, file_name, file_size, file_id, create_user_id, create_user_name, create_time, start_time, finish_time, expire_time, progress, error_msg, retry_count, max_retry_count, remark, update_time FROM syt_download_task WHERE task_status = '0' ORDER BY create_time ASC FETCH FIRST 50 ROWS ONLY
[2m2025-08-04 15:45:00.101[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:45:00.245[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:45:06.768[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:45:06.769[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:45:06.839[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:45:06.842[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:06.851[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:45:06.925[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:06.927[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:06.928[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:45:06.997[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:06.999[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:07.000[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:45:07.068[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:07.070[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:07.071[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:45:07.136[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:07.137[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:07.138[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:45:07.204[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:37.123[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:45:37.124[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:45:37.203[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:45:37.205[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:37.206[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:45:37.272[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:37.275[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:37.275[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:45:37.350[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:37.353[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:37.353[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:45:37.424[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:37.426[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:37.426[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:45:37.504[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:45:37.505[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:45:37.506[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:45:37.583[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:07.200[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:46:07.201[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:46:07.288[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:46:07.300[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:07.300[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:46:07.369[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:07.372[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:07.374[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:46:07.444[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:07.445[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:07.446[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:46:07.525[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:07.527[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:07.528[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:46:07.589[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:07.592[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:07.593[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:46:07.661[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:14.891[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT klmc FROM t_tdd WHERE klmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:46:14.893[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:46:14.949[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT nf FROM T_TDD ORDER BY nf DESC
[2m2025-08-04 15:46:14.950[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:46:14.974[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:46:15.063[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:46:15.070[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, cc, zslx, lqs FROM SYT_CODE_ZSLX ORDER BY nf DESC
[2m2025-08-04 15:46:15.070[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:46:15.145[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT sfmc FROM t_tdd WHERE sfmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:46:15.146[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 15:46:15.146[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:46:15.221[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 15:46:15.222[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, code, name, bz, is_search FROM SYT_CODE_CCB WHERE (is_search = ?)
[2m2025-08-04 15:46:15.222[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 是(String)
[2m2025-08-04 15:46:15.322[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:46:15.368[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT pcmc FROM t_tdd WHERE pcmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:46:15.378[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:46:15.461[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:15.487[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT id, fielden, fieldzh, sort, type, sfss, field_type, field_attribute, field_data FROM SYT_CUSTOM_COL WHERE (type = ?)
[2m2025-08-04 15:46:15.487[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-04 15:46:15.491[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT xymc FROM t_tdd WHERE xymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:46:15.491[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:46:15.494[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT zymc FROM t_tdd WHERE zymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:46:15.494[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:46:15.562[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:46:15.566[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 15:46:15.903[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_DICT_GROUP ORDER BY sort
[2m2025-08-04 15:46:15.903[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:46:15.985[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:15.985[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1760905501123649537(String)
[2m2025-08-04 15:46:16.147[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 13
[2m2025-08-04 15:46:16.148[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:16.149[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719039487111169(String)
[2m2025-08-04 15:46:16.282[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 10
[2m2025-08-04 15:46:16.282[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:16.284[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719076044664834(String)
[2m2025-08-04 15:46:16.338[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM T_TDD WHERE (nf = ?)
[2m2025-08-04 15:46:16.339[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:46:16.404[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:16.411[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT id, nf, sfdm, sfmc, ksh, bh, bmdw, bmdwmc, bylbdm, bylbmc, bylbmcxz, cj, csny, dqdm, dqmc, dqtddw, dxtj, hkdj, hkkh, jhxzdm, jhxz, kldm, klmc, klmcxz, ksjlhcf, kslbdm, kslbmc, kslbmcxz, kslsczbz, kslxdm, kslxmc, kstc, kstz, kstzmc, kszg, kszt, lqlxdm, lqlxmc, lqzy, lqzydm, lqzymc, zylbdm, zylbmc, lqzkfx, mzdm, mzmc, ccdm, cc, zslx, pcdm, pcmc, pcmcxz, sdbz, tdcj, tddwdm, tddwmc, tddwdm1, tddwdm2, tddwdm3, tddwdm4, tddwdm5, tddwdm6, tdyydm, tdyymc, tdzy, tjhg, tjjldm, tzcj, wyks, wytl, wyyzdm, wyyzmc, xbdm, xbmc, csrq, xm, xtdw, ysjzdm, ysjzmc, ytzy, ytzymc, yxdrcj, zgf, zkzh, zsyj, zxdm, zxmc, zyzy1, zydh1, zydm1, zyzy2, zydh2, zydm2, zyzy3, zydh3, zydm3, zyzy4, zydh4, zydm4, zyzy5, zydh5, zydm5, zyzy6, zydh6, zydm6, zyhg, zysxdm1, zysxdm2, zysxdm3, zysxdm4, zysxdm5, zysxdm6, zytjfc, zytz, zyytfjcj, zyytjzcj, zyzytj, zzmmdm, zzmmmc, kslqzy, ksytzy, xydm, xymc, xyxz, zydm, gjzydm, zyxq, zysfsf, zytjsx, zybddd, zyrxrq, zymc, bjdm, bjmc, xh, jtdz, yzbm, lxdh, lxsj, lqtzsyjdz, sjr, lqtzsh, lqtzssxh, kddh, yhkh, tjjlmc, yzmc, syddm, sydmc, xq, sfydy, task_tag, bz, photo, tjxx, bmxx, cjyzyxx, sfz, fjb, wbdwj, hyzp, xjzp, hysfzzp, hysfztxzp, dzqm, byxxdm, byxxmc, byxxdz, byxxp, byxxc, byxxd, byxxppzt, zjlxdm, zjlxmc, zjhm, xjh, txdz, sxpdkhyj, hjlbdm, hjlbmc, czlbdm, czlbmc, xkkm, xkkmhg, skkm, hjdm, hjmc, bj, tag, ljzyh, jhlbdm, jhlbmc, tdlxdm, tdlxmc, tdsj, yxdycj, lqsj, skkmhg, lqxh, tddbh, xzbh, zyfpfs, tddzt, peer_lqxh, score_equ, focused_mul, zycjxdm, zycj, zycjtfpw, zytj, zytjlx, zydh_mul, bdzt, bdztbz, section, pro_line, cjyw, cjsx, cjwy, cjzhzh, cjlsh, cjdl, cjwl, cjhx, cjsw, cjjsh, cjzh, ckzyzs, ckzyjn, cjzf, bqmx, ykszt, klmckz, zymckz, xyxzkz, pcmckz, kslbmckz, bylbmckz, lqtzsdyzt FROM T_TDD WHERE (nf = ?) ORDER BY nf DESC, lqtzssxh ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 15:46:16.413[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==> Parameters: 2024(String), 10(Long), 0(Long)
[2m2025-08-04 15:46:16.424[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 11
[2m2025-08-04 15:46:16.425[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:16.426[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719152070619137(String)
[2m2025-08-04 15:46:16.568[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 14
[2m2025-08-04 15:46:16.571[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:16.572[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719558465122306(String)
[2m2025-08-04 15:46:16.645[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 8
[2m2025-08-04 15:46:16.648[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:16.649[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719602580811778(String)
[2m2025-08-04 15:46:16.703[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m <==      Total: 10
[2m2025-08-04 15:46:16.725[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:46:16.726[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:16.728[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719653998784513(String)
[2m2025-08-04 15:46:16.803[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:46:16.804[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:16.805[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719695111352321(String)
[2m2025-08-04 15:46:17.073[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 30
[2m2025-08-04 15:46:17.074[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:17.074[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719747775033345(String)
[2m2025-08-04 15:46:17.284[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 22
[2m2025-08-04 15:46:17.286[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:17.287[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719794872872962(String)
[2m2025-08-04 15:46:17.367[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 5
[2m2025-08-04 15:46:17.431[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:46:17.432[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719849163943938(String)
[2m2025-08-04 15:46:18.024[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 65
[2m2025-08-04 15:46:18.025[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m <==      Total: 11
[2m2025-08-04 15:46:36.746[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:46:36.748[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:46:36.813[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:46:36.816[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:36.816[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:46:36.884[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:36.886[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:36.887[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:46:36.964[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:36.966[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:36.966[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:46:37.044[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:37.046[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:37.046[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:46:37.107[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:46:37.110[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:46:37.110[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:46:37.183[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:07.060[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:47:07.063[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:47:07.138[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:47:07.145[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:07.145[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:47:07.220[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:07.222[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:07.222[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:47:07.292[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:07.295[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:07.295[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:47:07.365[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:07.369[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:07.369[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:47:07.444[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:07.447[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:07.447[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:47:07.509[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:22.243[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT nf FROM T_TDD ORDER BY nf DESC
[2m2025-08-04 15:47:22.244[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:47:22.270[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, code, name, bz, is_search FROM SYT_CODE_CCB WHERE (is_search = ?)
[2m2025-08-04 15:47:22.271[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 是(String)
[2m2025-08-04 15:47:22.274[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, cc, zslx, lqs FROM SYT_CODE_ZSLX ORDER BY nf DESC
[2m2025-08-04 15:47:22.275[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:47:22.323[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:47:22.344[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:47:22.347[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 15:47:22.353[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT sfmc FROM t_tdd WHERE sfmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:22.354[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:22.414[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT pcmc FROM t_tdd WHERE pcmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:22.414[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:22.484[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT klmc FROM t_tdd WHERE klmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:22.485[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:22.504[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:22.567[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:47:22.687[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 15:47:22.839[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT xymc FROM t_tdd WHERE xymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:22.840[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:23.013[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:47:23.094[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT id, fielden, fieldzh, sort, type, sfss, field_type, field_attribute, field_data FROM SYT_CUSTOM_COL WHERE (type = ?)
[2m2025-08-04 15:47:23.094[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT zymc FROM t_tdd WHERE zymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:23.095[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:23.095[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-04 15:47:23.163[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_DICT_GROUP ORDER BY sort
[2m2025-08-04 15:47:23.163[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:47:23.176[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 15:47:23.208[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM T_TDD WHERE (nf = ?)
[2m2025-08-04 15:47:23.208[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:23.234[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:23.235[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1760905501123649537(String)
[2m2025-08-04 15:47:23.269[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:23.273[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT id, nf, sfdm, sfmc, ksh, bh, bmdw, bmdwmc, bylbdm, bylbmc, bylbmcxz, cj, csny, dqdm, dqmc, dqtddw, dxtj, hkdj, hkkh, jhxzdm, jhxz, kldm, klmc, klmcxz, ksjlhcf, kslbdm, kslbmc, kslbmcxz, kslsczbz, kslxdm, kslxmc, kstc, kstz, kstzmc, kszg, kszt, lqlxdm, lqlxmc, lqzy, lqzydm, lqzymc, zylbdm, zylbmc, lqzkfx, mzdm, mzmc, ccdm, cc, zslx, pcdm, pcmc, pcmcxz, sdbz, tdcj, tddwdm, tddwmc, tddwdm1, tddwdm2, tddwdm3, tddwdm4, tddwdm5, tddwdm6, tdyydm, tdyymc, tdzy, tjhg, tjjldm, tzcj, wyks, wytl, wyyzdm, wyyzmc, xbdm, xbmc, csrq, xm, xtdw, ysjzdm, ysjzmc, ytzy, ytzymc, yxdrcj, zgf, zkzh, zsyj, zxdm, zxmc, zyzy1, zydh1, zydm1, zyzy2, zydh2, zydm2, zyzy3, zydh3, zydm3, zyzy4, zydh4, zydm4, zyzy5, zydh5, zydm5, zyzy6, zydh6, zydm6, zyhg, zysxdm1, zysxdm2, zysxdm3, zysxdm4, zysxdm5, zysxdm6, zytjfc, zytz, zyytfjcj, zyytjzcj, zyzytj, zzmmdm, zzmmmc, kslqzy, ksytzy, xydm, xymc, xyxz, zydm, gjzydm, zyxq, zysfsf, zytjsx, zybddd, zyrxrq, zymc, bjdm, bjmc, xh, jtdz, yzbm, lxdh, lxsj, lqtzsyjdz, sjr, lqtzsh, lqtzssxh, kddh, yhkh, tjjlmc, yzmc, syddm, sydmc, xq, sfydy, task_tag, bz, photo, tjxx, bmxx, cjyzyxx, sfz, fjb, wbdwj, hyzp, xjzp, hysfzzp, hysfztxzp, dzqm, byxxdm, byxxmc, byxxdz, byxxp, byxxc, byxxd, byxxppzt, zjlxdm, zjlxmc, zjhm, xjh, txdz, sxpdkhyj, hjlbdm, hjlbmc, czlbdm, czlbmc, xkkm, xkkmhg, skkm, hjdm, hjmc, bj, tag, ljzyh, jhlbdm, jhlbmc, tdlxdm, tdlxmc, tdsj, yxdycj, lqsj, skkmhg, lqxh, tddbh, xzbh, zyfpfs, tddzt, peer_lqxh, score_equ, focused_mul, zycjxdm, zycj, zycjtfpw, zytj, zytjlx, zydh_mul, bdzt, bdztbz, section, pro_line, cjyw, cjsx, cjwy, cjzhzh, cjlsh, cjdl, cjwl, cjhx, cjsw, cjjsh, cjzh, ckzyzs, ckzyjn, cjzf, bqmx, ykszt, klmckz, zymckz, xyxzkz, pcmckz, kslbmckz, bylbmckz, lqtzsdyzt FROM T_TDD WHERE (nf = ?) ORDER BY nf DESC, lqtzssxh ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 15:47:23.274[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==> Parameters: 2024(String), 10(Long), 0(Long)
[2m2025-08-04 15:47:23.368[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 13
[2m2025-08-04 15:47:23.370[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:23.371[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719039487111169(String)
[2m2025-08-04 15:47:23.503[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 10
[2m2025-08-04 15:47:23.504[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:23.505[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719076044664834(String)
[2m2025-08-04 15:47:23.513[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m <==      Total: 10
[2m2025-08-04 15:47:23.658[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 11
[2m2025-08-04 15:47:23.659[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:23.660[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719152070619137(String)
[2m2025-08-04 15:47:24.083[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 14
[2m2025-08-04 15:47:24.085[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:24.087[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719558465122306(String)
[2m2025-08-04 15:47:24.163[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 8
[2m2025-08-04 15:47:24.164[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:24.165[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719602580811778(String)
[2m2025-08-04 15:47:24.242[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:47:24.243[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:24.244[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719653998784513(String)
[2m2025-08-04 15:47:24.355[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:47:24.356[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:24.356[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719695111352321(String)
[2m2025-08-04 15:47:24.689[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 30
[2m2025-08-04 15:47:24.689[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:24.690[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719747775033345(String)
[2m2025-08-04 15:47:24.903[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 22
[2m2025-08-04 15:47:24.904[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:24.905[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719794872872962(String)
[2m2025-08-04 15:47:24.985[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 5
[2m2025-08-04 15:47:25.054[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:25.055[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719849163943938(String)
[2m2025-08-04 15:47:25.579[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 65
[2m2025-08-04 15:47:25.579[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m <==      Total: 11
[2m2025-08-04 15:47:32.467[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, code, name, bz, is_search FROM SYT_CODE_CCB WHERE (is_search = ?)
[2m2025-08-04 15:47:32.469[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 是(String)
[2m2025-08-04 15:47:32.516[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT nf FROM T_TDD ORDER BY nf DESC
[2m2025-08-04 15:47:32.517[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, cc, zslx, lqs FROM SYT_CODE_ZSLX ORDER BY nf DESC
[2m2025-08-04 15:47:32.517[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:47:32.518[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:47:32.552[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:47:32.561[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT klmc FROM t_tdd WHERE klmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:32.562[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:32.593[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 15:47:32.593[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT pcmc FROM t_tdd WHERE pcmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:32.593[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT sfmc FROM t_tdd WHERE sfmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:32.594[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:32.594[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:32.598[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:47:32.630[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:47:32.679[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 15:47:32.683[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:32.931[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT id, fielden, fieldzh, sort, type, sfss, field_type, field_attribute, field_data FROM SYT_CUSTOM_COL WHERE (type = ?)
[2m2025-08-04 15:47:32.934[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-04 15:47:32.954[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT xymc FROM t_tdd WHERE xymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:32.955[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:32.972[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT zymc FROM t_tdd WHERE zymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:47:32.975[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:33.027[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:47:33.050[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 15:47:33.102[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM T_TDD WHERE (nf = ?)
[2m2025-08-04 15:47:33.103[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:47:33.179[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:33.180[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT id, nf, sfdm, sfmc, ksh, bh, bmdw, bmdwmc, bylbdm, bylbmc, bylbmcxz, cj, csny, dqdm, dqmc, dqtddw, dxtj, hkdj, hkkh, jhxzdm, jhxz, kldm, klmc, klmcxz, ksjlhcf, kslbdm, kslbmc, kslbmcxz, kslsczbz, kslxdm, kslxmc, kstc, kstz, kstzmc, kszg, kszt, lqlxdm, lqlxmc, lqzy, lqzydm, lqzymc, zylbdm, zylbmc, lqzkfx, mzdm, mzmc, ccdm, cc, zslx, pcdm, pcmc, pcmcxz, sdbz, tdcj, tddwdm, tddwmc, tddwdm1, tddwdm2, tddwdm3, tddwdm4, tddwdm5, tddwdm6, tdyydm, tdyymc, tdzy, tjhg, tjjldm, tzcj, wyks, wytl, wyyzdm, wyyzmc, xbdm, xbmc, csrq, xm, xtdw, ysjzdm, ysjzmc, ytzy, ytzymc, yxdrcj, zgf, zkzh, zsyj, zxdm, zxmc, zyzy1, zydh1, zydm1, zyzy2, zydh2, zydm2, zyzy3, zydh3, zydm3, zyzy4, zydh4, zydm4, zyzy5, zydh5, zydm5, zyzy6, zydh6, zydm6, zyhg, zysxdm1, zysxdm2, zysxdm3, zysxdm4, zysxdm5, zysxdm6, zytjfc, zytz, zyytfjcj, zyytjzcj, zyzytj, zzmmdm, zzmmmc, kslqzy, ksytzy, xydm, xymc, xyxz, zydm, gjzydm, zyxq, zysfsf, zytjsx, zybddd, zyrxrq, zymc, bjdm, bjmc, xh, jtdz, yzbm, lxdh, lxsj, lqtzsyjdz, sjr, lqtzsh, lqtzssxh, kddh, yhkh, tjjlmc, yzmc, syddm, sydmc, xq, sfydy, task_tag, bz, photo, tjxx, bmxx, cjyzyxx, sfz, fjb, wbdwj, hyzp, xjzp, hysfzzp, hysfztxzp, dzqm, byxxdm, byxxmc, byxxdz, byxxp, byxxc, byxxd, byxxppzt, zjlxdm, zjlxmc, zjhm, xjh, txdz, sxpdkhyj, hjlbdm, hjlbmc, czlbdm, czlbmc, xkkm, xkkmhg, skkm, hjdm, hjmc, bj, tag, ljzyh, jhlbdm, jhlbmc, tdlxdm, tdlxmc, tdsj, yxdycj, lqsj, skkmhg, lqxh, tddbh, xzbh, zyfpfs, tddzt, peer_lqxh, score_equ, focused_mul, zycjxdm, zycj, zycjtfpw, zytj, zytjlx, zydh_mul, bdzt, bdztbz, section, pro_line, cjyw, cjsx, cjwy, cjzhzh, cjlsh, cjdl, cjwl, cjhx, cjsw, cjjsh, cjzh, ckzyzs, ckzyjn, cjzf, bqmx, ykszt, klmckz, zymckz, xyxzkz, pcmckz, kslbmckz, bylbmckz, lqtzsdyzt FROM T_TDD WHERE (nf = ?) ORDER BY nf DESC, lqtzssxh ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 15:47:33.180[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==> Parameters: 2024(String), 10(Long), 0(Long)
[2m2025-08-04 15:47:33.235[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_DICT_GROUP ORDER BY sort
[2m2025-08-04 15:47:33.236[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:47:33.315[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:33.316[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1760905501123649537(String)
[2m2025-08-04 15:47:33.468[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 13
[2m2025-08-04 15:47:33.468[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:33.469[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719039487111169(String)
[2m2025-08-04 15:47:33.627[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 10
[2m2025-08-04 15:47:33.628[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:33.629[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719076044664834(String)
[2m2025-08-04 15:47:33.707[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m <==      Total: 10
[2m2025-08-04 15:47:34.037[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 11
[2m2025-08-04 15:47:34.038[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:34.039[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719152070619137(String)
[2m2025-08-04 15:47:34.181[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 14
[2m2025-08-04 15:47:34.182[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:34.185[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719558465122306(String)
[2m2025-08-04 15:47:34.253[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 8
[2m2025-08-04 15:47:34.254[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:34.254[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719602580811778(String)
[2m2025-08-04 15:47:34.324[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:47:34.325[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:34.326[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719653998784513(String)
[2m2025-08-04 15:47:34.405[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:47:34.406[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:34.407[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719695111352321(String)
[2m2025-08-04 15:47:35.249[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 30
[2m2025-08-04 15:47:35.251[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:35.252[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719747775033345(String)
[2m2025-08-04 15:47:35.469[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 22
[2m2025-08-04 15:47:35.470[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:35.470[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719794872872962(String)
[2m2025-08-04 15:47:35.541[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 5
[2m2025-08-04 15:47:35.622[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:47:35.622[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719849163943938(String)
[2m2025-08-04 15:47:36.145[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 65
[2m2025-08-04 15:47:36.145[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m <==      Total: 11
[2m2025-08-04 15:47:36.713[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:47:36.713[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:47:36.783[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:47:36.787[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:36.788[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:47:36.862[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:36.864[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:36.864[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:47:36.929[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:36.931[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:36.931[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:47:36.997[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:36.999[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:36.999[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:47:37.065[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:47:37.069[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:47:37.070[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:47:37.384[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:01.673[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT nf FROM T_TDD ORDER BY nf DESC
[2m2025-08-04 15:48:01.673[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:01.701[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT pcmc FROM t_tdd WHERE pcmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:01.702[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:01.750[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:01.753[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT sfmc FROM t_tdd WHERE sfmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:01.754[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:01.779[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, cc, zslx, lqs FROM SYT_CODE_ZSLX ORDER BY nf DESC
[2m2025-08-04 15:48:01.779[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:01.832[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 15:48:01.852[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 15:48:02.016[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT klmc FROM t_tdd WHERE klmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:02.017[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:02.033[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:02.034[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, code, name, bz, is_search FROM SYT_CODE_CCB WHERE (is_search = ?)
[2m2025-08-04 15:48:02.035[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 是(String)
[2m2025-08-04 15:48:02.088[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:02.117[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:48:02.166[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT xymc FROM t_tdd WHERE xymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:02.167[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:02.171[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT id, fielden, fieldzh, sort, type, sfss, field_type, field_attribute, field_data FROM SYT_CUSTOM_COL WHERE (type = ?)
[2m2025-08-04 15:48:02.179[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-04 15:48:02.231[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT zymc FROM t_tdd WHERE zymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:02.231[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:02.247[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:02.339[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 15:48:02.420[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_DICT_GROUP ORDER BY sort
[2m2025-08-04 15:48:02.420[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:02.489[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:02.490[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1760905501123649537(String)
[2m2025-08-04 15:48:02.571[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM T_TDD WHERE (nf = ?)
[2m2025-08-04 15:48:02.571[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:02.644[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 13
[2m2025-08-04 15:48:02.644[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:02.645[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:02.646[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719039487111169(String)
[2m2025-08-04 15:48:02.646[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT id, nf, sfdm, sfmc, ksh, bh, bmdw, bmdwmc, bylbdm, bylbmc, bylbmcxz, cj, csny, dqdm, dqmc, dqtddw, dxtj, hkdj, hkkh, jhxzdm, jhxz, kldm, klmc, klmcxz, ksjlhcf, kslbdm, kslbmc, kslbmcxz, kslsczbz, kslxdm, kslxmc, kstc, kstz, kstzmc, kszg, kszt, lqlxdm, lqlxmc, lqzy, lqzydm, lqzymc, zylbdm, zylbmc, lqzkfx, mzdm, mzmc, ccdm, cc, zslx, pcdm, pcmc, pcmcxz, sdbz, tdcj, tddwdm, tddwmc, tddwdm1, tddwdm2, tddwdm3, tddwdm4, tddwdm5, tddwdm6, tdyydm, tdyymc, tdzy, tjhg, tjjldm, tzcj, wyks, wytl, wyyzdm, wyyzmc, xbdm, xbmc, csrq, xm, xtdw, ysjzdm, ysjzmc, ytzy, ytzymc, yxdrcj, zgf, zkzh, zsyj, zxdm, zxmc, zyzy1, zydh1, zydm1, zyzy2, zydh2, zydm2, zyzy3, zydh3, zydm3, zyzy4, zydh4, zydm4, zyzy5, zydh5, zydm5, zyzy6, zydh6, zydm6, zyhg, zysxdm1, zysxdm2, zysxdm3, zysxdm4, zysxdm5, zysxdm6, zytjfc, zytz, zyytfjcj, zyytjzcj, zyzytj, zzmmdm, zzmmmc, kslqzy, ksytzy, xydm, xymc, xyxz, zydm, gjzydm, zyxq, zysfsf, zytjsx, zybddd, zyrxrq, zymc, bjdm, bjmc, xh, jtdz, yzbm, lxdh, lxsj, lqtzsyjdz, sjr, lqtzsh, lqtzssxh, kddh, yhkh, tjjlmc, yzmc, syddm, sydmc, xq, sfydy, task_tag, bz, photo, tjxx, bmxx, cjyzyxx, sfz, fjb, wbdwj, hyzp, xjzp, hysfzzp, hysfztxzp, dzqm, byxxdm, byxxmc, byxxdz, byxxp, byxxc, byxxd, byxxppzt, zjlxdm, zjlxmc, zjhm, xjh, txdz, sxpdkhyj, hjlbdm, hjlbmc, czlbdm, czlbmc, xkkm, xkkmhg, skkm, hjdm, hjmc, bj, tag, ljzyh, jhlbdm, jhlbmc, tdlxdm, tdlxmc, tdsj, yxdycj, lqsj, skkmhg, lqxh, tddbh, xzbh, zyfpfs, tddzt, peer_lqxh, score_equ, focused_mul, zycjxdm, zycj, zycjtfpw, zytj, zytjlx, zydh_mul, bdzt, bdztbz, section, pro_line, cjyw, cjsx, cjwy, cjzhzh, cjlsh, cjdl, cjwl, cjhx, cjsw, cjjsh, cjzh, ckzyzs, ckzyjn, cjzf, bqmx, ykszt, klmckz, zymckz, xyxzkz, pcmckz, kslbmckz, bylbmckz, lqtzsdyzt FROM T_TDD WHERE (nf = ?) ORDER BY nf DESC, lqtzssxh ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 15:48:02.646[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==> Parameters: 2024(String), 10(Long), 0(Long)
[2m2025-08-04 15:48:02.803[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 10
[2m2025-08-04 15:48:02.803[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:02.804[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719076044664834(String)
[2m2025-08-04 15:48:02.858[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m <==      Total: 10
[2m2025-08-04 15:48:02.946[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 11
[2m2025-08-04 15:48:02.947[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:02.948[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719152070619137(String)
[2m2025-08-04 15:48:03.092[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 14
[2m2025-08-04 15:48:03.092[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:03.093[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719558465122306(String)
[2m2025-08-04 15:48:03.162[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 8
[2m2025-08-04 15:48:03.163[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:03.164[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719602580811778(String)
[2m2025-08-04 15:48:03.483[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:48:03.484[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:03.485[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719653998784513(String)
[2m2025-08-04 15:48:03.574[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:48:03.576[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:03.579[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719695111352321(String)
[2m2025-08-04 15:48:03.858[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 30
[2m2025-08-04 15:48:03.859[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:03.860[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719747775033345(String)
[2m2025-08-04 15:48:04.083[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 22
[2m2025-08-04 15:48:04.084[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:04.084[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719794872872962(String)
[2m2025-08-04 15:48:04.158[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 5
[2m2025-08-04 15:48:04.254[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:04.255[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719849163943938(String)
[2m2025-08-04 15:48:04.743[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 65
[2m2025-08-04 15:48:04.744[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m <==      Total: 11
[2m2025-08-04 15:48:07.011[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:48:07.012[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:48:07.090[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:07.093[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:07.093[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:48:07.168[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:07.170[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:07.171[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:48:07.243[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:07.247[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:07.247[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:48:07.314[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:07.316[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:07.317[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:48:07.391[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:07.392[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:07.393[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:48:07.464[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:22.302[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, cc, zslx, lqs FROM SYT_CODE_ZSLX ORDER BY nf DESC
[2m2025-08-04 15:48:22.305[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:22.310[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, code, name, bz, is_search FROM SYT_CODE_CCB WHERE (is_search = ?)
[2m2025-08-04 15:48:22.311[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 是(String)
[2m2025-08-04 15:48:22.385[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:48:22.385[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 15:48:22.386[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT nf FROM T_TDD ORDER BY nf DESC
[2m2025-08-04 15:48:22.387[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:22.391[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT pcmc FROM t_tdd WHERE pcmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:22.391[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:22.470[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:22.471[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:22.726[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT sfmc FROM t_tdd WHERE sfmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:22.727[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:22.762[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT klmc FROM t_tdd WHERE klmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:22.763[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:22.808[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 15:48:22.809[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT id, fielden, fieldzh, sort, type, sfss, field_type, field_attribute, field_data FROM SYT_CUSTOM_COL WHERE (type = ?)
[2m2025-08-04 15:48:22.809[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-04 15:48:22.837[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:22.963[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT zymc FROM t_tdd WHERE zymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:22.966[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:23.044[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 15:48:23.085[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT xymc FROM t_tdd WHERE xymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:23.086[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:23.123[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM T_TDD WHERE (nf = ?)
[2m2025-08-04 15:48:23.124[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:23.145[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_DICT_GROUP ORDER BY sort
[2m2025-08-04 15:48:23.146[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:23.210[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:23.211[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1760905501123649537(String)
[2m2025-08-04 15:48:23.362[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 13
[2m2025-08-04 15:48:23.363[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:23.364[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719039487111169(String)
[2m2025-08-04 15:48:23.407[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:23.454[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:23.456[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT id, nf, sfdm, sfmc, ksh, bh, bmdw, bmdwmc, bylbdm, bylbmc, bylbmcxz, cj, csny, dqdm, dqmc, dqtddw, dxtj, hkdj, hkkh, jhxzdm, jhxz, kldm, klmc, klmcxz, ksjlhcf, kslbdm, kslbmc, kslbmcxz, kslsczbz, kslxdm, kslxmc, kstc, kstz, kstzmc, kszg, kszt, lqlxdm, lqlxmc, lqzy, lqzydm, lqzymc, zylbdm, zylbmc, lqzkfx, mzdm, mzmc, ccdm, cc, zslx, pcdm, pcmc, pcmcxz, sdbz, tdcj, tddwdm, tddwmc, tddwdm1, tddwdm2, tddwdm3, tddwdm4, tddwdm5, tddwdm6, tdyydm, tdyymc, tdzy, tjhg, tjjldm, tzcj, wyks, wytl, wyyzdm, wyyzmc, xbdm, xbmc, csrq, xm, xtdw, ysjzdm, ysjzmc, ytzy, ytzymc, yxdrcj, zgf, zkzh, zsyj, zxdm, zxmc, zyzy1, zydh1, zydm1, zyzy2, zydh2, zydm2, zyzy3, zydh3, zydm3, zyzy4, zydh4, zydm4, zyzy5, zydh5, zydm5, zyzy6, zydh6, zydm6, zyhg, zysxdm1, zysxdm2, zysxdm3, zysxdm4, zysxdm5, zysxdm6, zytjfc, zytz, zyytfjcj, zyytjzcj, zyzytj, zzmmdm, zzmmmc, kslqzy, ksytzy, xydm, xymc, xyxz, zydm, gjzydm, zyxq, zysfsf, zytjsx, zybddd, zyrxrq, zymc, bjdm, bjmc, xh, jtdz, yzbm, lxdh, lxsj, lqtzsyjdz, sjr, lqtzsh, lqtzssxh, kddh, yhkh, tjjlmc, yzmc, syddm, sydmc, xq, sfydy, task_tag, bz, photo, tjxx, bmxx, cjyzyxx, sfz, fjb, wbdwj, hyzp, xjzp, hysfzzp, hysfztxzp, dzqm, byxxdm, byxxmc, byxxdz, byxxp, byxxc, byxxd, byxxppzt, zjlxdm, zjlxmc, zjhm, xjh, txdz, sxpdkhyj, hjlbdm, hjlbmc, czlbdm, czlbmc, xkkm, xkkmhg, skkm, hjdm, hjmc, bj, tag, ljzyh, jhlbdm, jhlbmc, tdlxdm, tdlxmc, tdsj, yxdycj, lqsj, skkmhg, lqxh, tddbh, xzbh, zyfpfs, tddzt, peer_lqxh, score_equ, focused_mul, zycjxdm, zycj, zycjtfpw, zytj, zytjlx, zydh_mul, bdzt, bdztbz, section, pro_line, cjyw, cjsx, cjwy, cjzhzh, cjlsh, cjdl, cjwl, cjhx, cjsw, cjjsh, cjzh, ckzyzs, ckzyjn, cjzf, bqmx, ykszt, klmckz, zymckz, xyxzkz, pcmckz, kslbmckz, bylbmckz, lqtzsdyzt FROM T_TDD WHERE (nf = ?) ORDER BY nf DESC, lqtzssxh ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 15:48:23.457[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==> Parameters: 2024(String), 10(Long), 0(Long)
[2m2025-08-04 15:48:23.497[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 10
[2m2025-08-04 15:48:23.498[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:23.498[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719076044664834(String)
[2m2025-08-04 15:48:23.684[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m <==      Total: 10
[2m2025-08-04 15:48:23.890[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 11
[2m2025-08-04 15:48:23.890[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:23.891[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719152070619137(String)
[2m2025-08-04 15:48:24.023[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 14
[2m2025-08-04 15:48:24.023[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:24.023[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719558465122306(String)
[2m2025-08-04 15:48:24.090[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 8
[2m2025-08-04 15:48:24.090[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:24.091[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719602580811778(String)
[2m2025-08-04 15:48:24.163[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:48:24.163[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:24.164[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719653998784513(String)
[2m2025-08-04 15:48:24.232[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:48:24.233[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:24.233[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719695111352321(String)
[2m2025-08-04 15:48:24.872[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 30
[2m2025-08-04 15:48:24.873[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:24.874[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719747775033345(String)
[2m2025-08-04 15:48:25.532[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 22
[2m2025-08-04 15:48:25.534[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:25.535[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719794872872962(String)
[2m2025-08-04 15:48:25.846[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 5
[2m2025-08-04 15:48:25.983[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:25.985[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719849163943938(String)
[2m2025-08-04 15:48:27.509[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 65
[2m2025-08-04 15:48:27.509[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m <==      Total: 11
[2m2025-08-04 15:48:38.647[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:48:38.649[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:48:38.770[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:38.775[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:38.776[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:48:39.110[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:39.346[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:39.347[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:48:39.425[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:39.528[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:39.530[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:48:39.568[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, code, name, bz, is_search FROM SYT_CODE_CCB WHERE (is_search = ?)
[2m2025-08-04 15:48:39.570[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 是(String)
[2m2025-08-04 15:48:39.618[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:39.619[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT nf FROM T_TDD ORDER BY nf DESC
[2m2025-08-04 15:48:39.620[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:39.621[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:39.621[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:48:39.691[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:48:39.692[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT pcmc FROM t_tdd WHERE pcmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:39.693[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:39.700[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:39.702[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:39.705[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:48:39.706[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:48:39.707[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, cc, zslx, lqs FROM SYT_CODE_ZSLX ORDER BY nf DESC
[2m2025-08-04 15:48:39.708[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT sfmc FROM t_tdd WHERE sfmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:39.708[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:39.708[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:39.875[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:39.875[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 15:48:39.875[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:39.875[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 15:48:40.334[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT klmc FROM t_tdd WHERE klmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:40.335[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:40.341[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_DICT_GROUP ORDER BY sort
[2m2025-08-04 15:48:40.341[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:48:40.366[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT id, fielden, fieldzh, sort, type, sfss, field_type, field_attribute, field_data FROM SYT_CUSTOM_COL WHERE (type = ?)
[2m2025-08-04 15:48:40.366[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-04 15:48:40.405[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:40.406[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:40.407[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1760905501123649537(String)
[2m2025-08-04 15:48:40.420[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT zymc FROM t_tdd WHERE zymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:40.421[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:40.596[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 13
[2m2025-08-04 15:48:40.598[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:40.599[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719039487111169(String)
[2m2025-08-04 15:48:40.600[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT xymc FROM t_tdd WHERE xymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 15:48:40.601[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:40.683[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:48:40.782[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 15:48:40.847[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM T_TDD WHERE (nf = ?)
[2m2025-08-04 15:48:40.847[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 15:48:40.915[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:48:40.917[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT id, nf, sfdm, sfmc, ksh, bh, bmdw, bmdwmc, bylbdm, bylbmc, bylbmcxz, cj, csny, dqdm, dqmc, dqtddw, dxtj, hkdj, hkkh, jhxzdm, jhxz, kldm, klmc, klmcxz, ksjlhcf, kslbdm, kslbmc, kslbmcxz, kslsczbz, kslxdm, kslxmc, kstc, kstz, kstzmc, kszg, kszt, lqlxdm, lqlxmc, lqzy, lqzydm, lqzymc, zylbdm, zylbmc, lqzkfx, mzdm, mzmc, ccdm, cc, zslx, pcdm, pcmc, pcmcxz, sdbz, tdcj, tddwdm, tddwmc, tddwdm1, tddwdm2, tddwdm3, tddwdm4, tddwdm5, tddwdm6, tdyydm, tdyymc, tdzy, tjhg, tjjldm, tzcj, wyks, wytl, wyyzdm, wyyzmc, xbdm, xbmc, csrq, xm, xtdw, ysjzdm, ysjzmc, ytzy, ytzymc, yxdrcj, zgf, zkzh, zsyj, zxdm, zxmc, zyzy1, zydh1, zydm1, zyzy2, zydh2, zydm2, zyzy3, zydh3, zydm3, zyzy4, zydh4, zydm4, zyzy5, zydh5, zydm5, zyzy6, zydh6, zydm6, zyhg, zysxdm1, zysxdm2, zysxdm3, zysxdm4, zysxdm5, zysxdm6, zytjfc, zytz, zyytfjcj, zyytjzcj, zyzytj, zzmmdm, zzmmmc, kslqzy, ksytzy, xydm, xymc, xyxz, zydm, gjzydm, zyxq, zysfsf, zytjsx, zybddd, zyrxrq, zymc, bjdm, bjmc, xh, jtdz, yzbm, lxdh, lxsj, lqtzsyjdz, sjr, lqtzsh, lqtzssxh, kddh, yhkh, tjjlmc, yzmc, syddm, sydmc, xq, sfydy, task_tag, bz, photo, tjxx, bmxx, cjyzyxx, sfz, fjb, wbdwj, hyzp, xjzp, hysfzzp, hysfztxzp, dzqm, byxxdm, byxxmc, byxxdz, byxxp, byxxc, byxxd, byxxppzt, zjlxdm, zjlxmc, zjhm, xjh, txdz, sxpdkhyj, hjlbdm, hjlbmc, czlbdm, czlbmc, xkkm, xkkmhg, skkm, hjdm, hjmc, bj, tag, ljzyh, jhlbdm, jhlbmc, tdlxdm, tdlxmc, tdsj, yxdycj, lqsj, skkmhg, lqxh, tddbh, xzbh, zyfpfs, tddzt, peer_lqxh, score_equ, focused_mul, zycjxdm, zycj, zycjtfpw, zytj, zytjlx, zydh_mul, bdzt, bdztbz, section, pro_line, cjyw, cjsx, cjwy, cjzhzh, cjlsh, cjdl, cjwl, cjhx, cjsw, cjjsh, cjzh, ckzyzs, ckzyjn, cjzf, bqmx, ykszt, klmckz, zymckz, xyxzkz, pcmckz, kslbmckz, bylbmckz, lqtzsdyzt FROM T_TDD WHERE (nf = ?) ORDER BY nf DESC, lqtzssxh ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 15:48:40.918[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==> Parameters: 2024(String), 10(Long), 0(Long)
[2m2025-08-04 15:48:41.018[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 10
[2m2025-08-04 15:48:41.018[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:41.019[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719076044664834(String)
[2m2025-08-04 15:48:41.167[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 11
[2m2025-08-04 15:48:41.167[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:41.168[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719152070619137(String)
[2m2025-08-04 15:48:41.179[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m <==      Total: 10
[2m2025-08-04 15:48:41.299[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 14
[2m2025-08-04 15:48:41.299[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:41.300[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719558465122306(String)
[2m2025-08-04 15:48:41.375[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 8
[2m2025-08-04 15:48:41.376[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:41.376[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719602580811778(String)
[2m2025-08-04 15:48:41.455[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:48:41.460[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:41.461[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719653998784513(String)
[2m2025-08-04 15:48:41.547[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 15:48:41.547[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:41.548[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719695111352321(String)
[2m2025-08-04 15:48:42.185[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 30
[2m2025-08-04 15:48:42.187[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:42.188[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719747775033345(String)
[2m2025-08-04 15:48:42.558[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 22
[2m2025-08-04 15:48:42.559[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:42.559[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719794872872962(String)
[2m2025-08-04 15:48:42.729[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 5
[2m2025-08-04 15:48:42.842[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 15:48:42.843[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719849163943938(String)
[2m2025-08-04 15:48:43.644[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 65
[2m2025-08-04 15:48:43.645[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m <==      Total: 11
[2m2025-08-04 15:49:07.064[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:49:07.066[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:49:07.145[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:49:07.147[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:07.148[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:49:07.220[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:07.221[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:07.222[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:49:07.291[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:07.294[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:07.295[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:49:07.622[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:07.625[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:07.625[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:49:07.697[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:07.699[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:07.700[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:49:07.776[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:37.031[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:49:37.034[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:49:37.363[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:49:37.365[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:37.366[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:49:37.454[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:37.457[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:37.457[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:49:37.533[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:37.536[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:37.536[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:49:37.603[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:37.606[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:37.606[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:49:37.688[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:49:37.690[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:49:37.691[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:49:37.763[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:00.102[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==>  Preparing: SELECT id, task_name, task_type, task_status, task_desc, task_params, file_path, file_name, file_size, file_id, create_user_id, create_user_name, create_time, start_time, finish_time, expire_time, progress, error_msg, retry_count, max_retry_count, remark, update_time FROM syt_download_task WHERE task_status = '0' ORDER BY create_time ASC FETCH FIRST 50 ROWS ONLY
[2m2025-08-04 15:50:00.103[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:50:00.250[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:50:06.713[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:50:06.714[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:50:06.787[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:50:06.789[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:06.790[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:50:06.858[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:06.859[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:06.859[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:50:07.178[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:07.180[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:07.180[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:50:07.258[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:07.261[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:07.262[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:50:07.328[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:07.334[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:07.335[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:50:07.423[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:37.150[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:50:37.152[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:50:37.224[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:50:37.227[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:37.229[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:50:37.295[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:37.297[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:37.298[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:50:37.363[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:37.365[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:37.366[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:50:37.446[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:37.448[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:37.449[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:50:37.522[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:50:37.524[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:50:37.525[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:50:37.603[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:07.148[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:51:07.149[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:51:07.259[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:51:07.268[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:07.269[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:51:07.344[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:07.347[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:07.347[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:51:07.423[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:07.427[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:07.428[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:51:07.505[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:07.508[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:07.510[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:51:07.871[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:07.874[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:07.877[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:51:07.957[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:36.899[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:51:36.900[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:51:36.977[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:51:36.981[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:36.982[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:51:37.059[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:37.063[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:37.064[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:51:37.378[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:37.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:37.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:51:37.701[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:37.703[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:37.704[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:51:37.781[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:51:37.785[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:51:37.785[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:51:37.859[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:06.823[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:52:06.824[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:52:07.184[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:52:07.189[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:07.190[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:52:07.293[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:07.298[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:07.298[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:52:07.367[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:07.370[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:07.371[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:52:07.445[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:07.449[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:07.449[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:52:07.522[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:07.524[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:07.525[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:52:07.606[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:36.716[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:52:36.717[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:52:36.790[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:52:36.793[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:36.794[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:52:36.858[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:36.861[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:36.862[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:52:36.941[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:36.943[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:36.944[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:52:37.002[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:37.004[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:37.004[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:52:37.338[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:52:37.344[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:52:37.345[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:52:37.409[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:07.137[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:53:07.137[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:53:07.712[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:53:07.714[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:07.715[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:53:07.888[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:07.890[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:07.890[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:53:07.959[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:07.960[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:07.961[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:53:08.419[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:08.422[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:08.422[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:53:08.492[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:08.494[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:08.496[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:53:08.562[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:36.811[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:53:36.812[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:53:36.884[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:53:36.886[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:36.886[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:53:36.965[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:36.967[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:36.969[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:53:37.043[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:37.044[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:37.048[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:53:37.123[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:37.125[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:37.125[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:53:37.192[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:53:37.194[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:53:37.196[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:53:37.263[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:07.396[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:54:07.397[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:54:07.468[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:54:07.470[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:07.471[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:54:07.545[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:07.547[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:07.548[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:54:07.617[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:07.619[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:07.620[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:54:07.696[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:07.698[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:07.699[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:54:07.764[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:07.767[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:07.767[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:54:07.843[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:36.777[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:54:36.779[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:54:36.883[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:54:36.886[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:36.886[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:54:36.964[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:36.968[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:36.971[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:54:37.042[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:37.044[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:37.045[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:54:37.122[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:37.124[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:37.124[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:54:37.203[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:54:37.205[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:54:37.206[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:54:37.283[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:00.114[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==>  Preparing: SELECT id, task_name, task_type, task_status, task_desc, task_params, file_path, file_name, file_size, file_id, create_user_id, create_user_name, create_time, start_time, finish_time, expire_time, progress, error_msg, retry_count, max_retry_count, remark, update_time FROM syt_download_task WHERE task_status = '0' ORDER BY create_time ASC FETCH FIRST 50 ROWS ONLY
[2m2025-08-04 15:55:00.121[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 15:55:00.251[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 15:55:06.750[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:55:06.751[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:55:06.824[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:55:06.826[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:06.826[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:55:06.888[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:06.890[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:06.891[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:55:06.973[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:06.975[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:06.976[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:55:07.042[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:07.044[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:07.045[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:55:07.109[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:07.111[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:07.112[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:55:07.187[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:37.304[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:55:37.305[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:55:37.380[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:55:37.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:37.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:55:37.446[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:37.447[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:37.448[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:55:37.522[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:37.523[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:37.524[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:55:37.602[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:37.605[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:37.606[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:55:37.671[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:55:37.674[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:55:37.674[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:55:37.738[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:06.721[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:56:06.722[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:56:06.787[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:56:06.789[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:06.789[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:56:06.859[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:06.862[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:06.863[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:56:06.925[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:06.927[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:06.928[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:56:06.994[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:06.995[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:06.996[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:56:07.068[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:07.070[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:07.070[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:56:07.172[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:36.780[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:56:36.781[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:56:36.851[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:56:36.855[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:36.855[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:56:37.166[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:37.169[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:37.169[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:56:37.236[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:37.240[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:37.241[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:56:37.307[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:37.310[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:37.311[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:56:37.380[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:56:37.384[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:56:37.384[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:56:38.377[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:07.210[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:57:07.211[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:57:07.283[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:57:07.285[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:07.285[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:57:07.362[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:07.365[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:07.366[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:57:07.443[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:07.445[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:07.445[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:57:07.511[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:07.513[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:07.514[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:57:07.581[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:07.582[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:07.582[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:57:07.646[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:38.184[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:57:38.187[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:57:38.271[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:57:38.276[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:38.277[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:57:38.343[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:38.345[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:38.346[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:57:38.409[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:38.412[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:38.413[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:57:38.482[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:38.484[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:38.485[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:57:38.562[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:57:38.564[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:57:38.565[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:57:38.887[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:07.370[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:58:07.376[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:58:07.457[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:58:07.464[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:07.465[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:58:07.533[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:07.538[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:07.539[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:58:07.602[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:07.606[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:07.607[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:58:07.682[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:07.686[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:07.687[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:58:07.762[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:07.766[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:07.768[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:58:07.842[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:36.817[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:58:36.818[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:58:36.890[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:58:36.894[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:36.895[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:58:36.962[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:36.966[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:36.967[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:58:37.029[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:37.035[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:37.037[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:58:37.102[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:37.105[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:37.106[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:58:37.179[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:58:37.181[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:58:37.182[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:58:37.246[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:06.752[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:59:06.753[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:59:06.822[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:59:06.826[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:06.827[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:59:06.893[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:06.896[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:06.896[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:59:07.213[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:07.216[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:07.216[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:59:07.284[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:07.287[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:07.287[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:59:07.363[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:07.367[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:07.367[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:59:07.686[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:36.775[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 15:59:36.776[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 15:59:36.848[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 15:59:36.850[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:36.851[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 15:59:36.921[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:36.923[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:36.923[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 15:59:37.237[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:37.241[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:37.241[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 15:59:37.309[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:37.312[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:37.312[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 15:59:37.387[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 15:59:37.389[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 15:59:37.390[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 15:59:37.469[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:00.223[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==>  Preparing: SELECT id, task_name, task_type, task_status, task_desc, task_params, file_path, file_name, file_size, file_id, create_user_id, create_user_name, create_time, start_time, finish_time, expire_time, progress, error_msg, retry_count, max_retry_count, remark, update_time FROM syt_download_task WHERE task_status = '0' ORDER BY create_time ASC FETCH FIRST 50 ROWS ONLY
[2m2025-08-04 16:00:00.224[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 16:00:00.624[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 16:00:08.111[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:00:08.114[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:00:08.433[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:08.437[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:08.437[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:00:08.507[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:08.512[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:08.513[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:00:08.589[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:08.593[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:08.593[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:00:08.671[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:08.677[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:08.678[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:00:08.753[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:08.759[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:08.760[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:00:08.872[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:37.787[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:00:37.788[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:00:37.859[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:37.862[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:37.863[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:00:37.932[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:37.934[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:37.935[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:00:38.001[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:38.005[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:38.006[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:00:38.066[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:38.069[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:38.069[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:00:38.137[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:38.140[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:38.141[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:00:38.201[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:52.343[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, code, name, bz, is_search FROM SYT_CODE_CCB WHERE (is_search = ?)
[2m2025-08-04 16:00:52.345[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m ==> Parameters: 是(String)
[2m2025-08-04 16:00:52.349[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==>  Preparing: SELECT id, nf, cc, zslx, lqs FROM SYT_CODE_ZSLX ORDER BY nf DESC
[2m2025-08-04 16:00:52.350[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 16:00:52.505[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT nf FROM T_TDD ORDER BY nf DESC
[2m2025-08-04 16:00:52.507[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 16:00:52.578[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.mapper.ksgl.TddMapper.findAllNf     [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:52.596[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT sfmc FROM t_tdd WHERE sfmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 16:00:52.597[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 16:00:52.666[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.system.CodeZslxMapper.selectList  [0;39m [2m:[0;39m <==      Total: 4
[2m2025-08-04 16:00:52.672[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 3
[2m2025-08-04 16:00:52.716[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:00:52.717[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:00:52.809[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:52.813[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==>  Preparing: SELECT T.* FROM SYT_PERMISSION_ROLE T WHERE EXISTS (SELECT 1 FROM SYT_PERMISSION_ACCOUNT_ROLE WHERE T.ID = ROLE_ID AND ACCOUNT_ID = ?)
[2m2025-08-04 16:00:52.813[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:00:52.947[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.system.CodeCcbMapper.selectList   [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 16:00:52.964[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getByAccount                  [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:52.974[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:00:52.975[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:00:53.042[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:53.050[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m ==>  Preparing: SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND r.TYPE = 1 AND r.STATUS = '是' AND r.IS_BTN = '否' ORDER BY r.sort
[2m2025-08-04 16:00:53.051[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m ==> Parameters: A601530922D36D12E05359EB11ACB7BE(String)
[2m2025-08-04 16:00:53.135[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.R.getFrontListByRole            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 16:00:53.508[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT klmc FROM t_tdd WHERE klmc IS NOT NULL AND (nf = ?)
[2m2025-08-04 16:00:53.509[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 16:00:53.603[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:53.685[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT zymc FROM t_tdd WHERE zymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 16:00:53.686[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 16:00:53.762[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 5
[2m2025-08-04 16:00:53.903[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==>  Preparing: SELECT DISTINCT xymc FROM t_tdd WHERE xymc IS NOT NULL AND (nf = ?)
[2m2025-08-04 16:00:53.904[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 16:00:53.908[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:00:53.909[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:00:53.981[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.mapper.ksgl.TddMapper.selectByField [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:53.981[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:53.984[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:53.985[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:00:54.064[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:54.068[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:54.071[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:00:54.081[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==>  Preparing: SELECT id, fielden, fieldzh, sort, type, sfss, field_type, field_attribute, field_data FROM SYT_CUSTOM_COL WHERE (type = ?)
[2m2025-08-04 16:00:54.082[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.system.CustomColMapper.selectList [0;39m [2m:[0;39m ==> Parameters: 学生(String)
[2m2025-08-04 16:00:54.143[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:54.145[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:54.146[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:00:54.227[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:54.230[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:54.230[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:00:54.232[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:00:54.233[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:00:54.305[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:54.306[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:54.307[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:00:54.308[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:00:54.310[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM SYT_DICT_GROUP ORDER BY sort
[2m2025-08-04 16:00:54.311[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 16:00:54.314[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(*) AS total FROM T_TDD WHERE (nf = ?)
[2m2025-08-04 16:00:54.315[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m ==> Parameters: 2024(String)
[2m2025-08-04 16:00:54.315[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m ==>  Preparing: SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '否' ORDER BY r.sort
[2m2025-08-04 16:00:54.316[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m ==> Parameters: A601530922D36D12E05359EB11ACB7BE(String)
[2m2025-08-04 16:00:54.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:54.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.ksgl.TddMapper.selectPage_mpCount [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:00:54.383[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:54.384[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1760905501123649537(String)
[2m2025-08-04 16:00:54.385[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==>  Preparing: SELECT * FROM ( SELECT TMP.*, ROWNUM ROW_ID FROM ( SELECT id, nf, sfdm, sfmc, ksh, bh, bmdw, bmdwmc, bylbdm, bylbmc, bylbmcxz, cj, csny, dqdm, dqmc, dqtddw, dxtj, hkdj, hkkh, jhxzdm, jhxz, kldm, klmc, klmcxz, ksjlhcf, kslbdm, kslbmc, kslbmcxz, kslsczbz, kslxdm, kslxmc, kstc, kstz, kstzmc, kszg, kszt, lqlxdm, lqlxmc, lqzy, lqzydm, lqzymc, zylbdm, zylbmc, lqzkfx, mzdm, mzmc, ccdm, cc, zslx, pcdm, pcmc, pcmcxz, sdbz, tdcj, tddwdm, tddwmc, tddwdm1, tddwdm2, tddwdm3, tddwdm4, tddwdm5, tddwdm6, tdyydm, tdyymc, tdzy, tjhg, tjjldm, tzcj, wyks, wytl, wyyzdm, wyyzmc, xbdm, xbmc, csrq, xm, xtdw, ysjzdm, ysjzmc, ytzy, ytzymc, yxdrcj, zgf, zkzh, zsyj, zxdm, zxmc, zyzy1, zydh1, zydm1, zyzy2, zydh2, zydm2, zyzy3, zydh3, zydm3, zyzy4, zydh4, zydm4, zyzy5, zydh5, zydm5, zyzy6, zydh6, zydm6, zyhg, zysxdm1, zysxdm2, zysxdm3, zysxdm4, zysxdm5, zysxdm6, zytjfc, zytz, zyytfjcj, zyytjzcj, zyzytj, zzmmdm, zzmmmc, kslqzy, ksytzy, xydm, xymc, xyxz, zydm, gjzydm, zyxq, zysfsf, zytjsx, zybddd, zyrxrq, zymc, bjdm, bjmc, xh, jtdz, yzbm, lxdh, lxsj, lqtzsyjdz, sjr, lqtzsh, lqtzssxh, kddh, yhkh, tjjlmc, yzmc, syddm, sydmc, xq, sfydy, task_tag, bz, photo, tjxx, bmxx, cjyzyxx, sfz, fjb, wbdwj, hyzp, xjzp, hysfzzp, hysfztxzp, dzqm, byxxdm, byxxmc, byxxdz, byxxp, byxxc, byxxd, byxxppzt, zjlxdm, zjlxmc, zjhm, xjh, txdz, sxpdkhyj, hjlbdm, hjlbmc, czlbdm, czlbmc, xkkm, xkkmhg, skkm, hjdm, hjmc, bj, tag, ljzyh, jhlbdm, jhlbmc, tdlxdm, tdlxmc, tdsj, yxdycj, lqsj, skkmhg, lqxh, tddbh, xzbh, zyfpfs, tddzt, peer_lqxh, score_equ, focused_mul, zycjxdm, zycj, zycjtfpw, zytj, zytjlx, zydh_mul, bdzt, bdztbz, section, pro_line, cjyw, cjsx, cjwy, cjzhzh, cjlsh, cjdl, cjwl, cjhx, cjsw, cjjsh, cjzh, ckzyzs, ckzyjn, cjzf, bqmx, ykszt, klmckz, zymckz, xyxzkz, pcmckz, kslbmckz, bylbmckz, lqtzsdyzt FROM T_TDD WHERE (nf = ?) ORDER BY nf DESC, lqtzssxh ASC ) TMP WHERE ROWNUM <=?) WHERE ROW_ID > ?
[2m2025-08-04 16:00:54.387[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m ==> Parameters: 2024(String), 10(Long), 0(Long)
[2m2025-08-04 16:00:54.544[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 13
[2m2025-08-04 16:00:54.545[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:54.545[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719039487111169(String)
[2m2025-08-04 16:00:54.641[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.mapper.ksgl.TddMapper.selectPage    [0;39m [2m:[0;39m <==      Total: 10
[2m2025-08-04 16:00:54.686[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 10
[2m2025-08-04 16:00:54.687[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:54.688[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719076044664834(String)
[2m2025-08-04 16:00:54.901[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.ResourceMapper.getListByRole    [0;39m [2m:[0;39m <==      Total: 79
[2m2025-08-04 16:00:55.061[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 11
[2m2025-08-04 16:00:55.061[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:55.062[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719152070619137(String)
[2m2025-08-04 16:00:55.204[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 14
[2m2025-08-04 16:00:55.205[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:55.206[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719558465122306(String)
[2m2025-08-04 16:00:55.517[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 8
[2m2025-08-04 16:00:55.518[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:55.519[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719602580811778(String)
[2m2025-08-04 16:00:55.624[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 16:00:55.625[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:55.626[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719653998784513(String)
[2m2025-08-04 16:00:55.700[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 2
[2m2025-08-04 16:00:55.701[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:55.703[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719695111352321(String)
[2m2025-08-04 16:00:55.787[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:00:55.787[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:00:55.852[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:00:55.872[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m ==>  Preparing: SELECT t.* FROM (SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r WHERE r.id IN (SELECT r.parent_id FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '是') UNION ALL SELECT r.ID, r.code, r.action, r.is_open, r.LABEL, r.IS_BTN, r.ICON, r.PARAM, r.PATH, r.PARENT_ID, r.STATUS, r.DESCRIPTION, r.URL, r.COMPONENT, r.type, r.sort, r.event FROM SYS_RESOURCE r, SYS_ROLE_RESOURCE srr WHERE r.id = srr.RESOURCE_ID AND srr.ROLE_ID = ? AND (r.TYPE = '0' OR r.TYPE IS NULL) AND r.STATUS = '是' AND r.IS_BTN = '是') t
[2m2025-08-04 16:00:55.873[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m ==> Parameters: A601530922D36D12E05359EB11ACB7BE(String), A601530922D36D12E05359EB11ACB7BE(String)
[2m2025-08-04 16:00:56.241[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 30
[2m2025-08-04 16:00:56.243[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:56.244[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719747775033345(String)
[2m2025-08-04 16:00:56.441[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 22
[2m2025-08-04 16:00:56.441[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:56.442[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719794872872962(String)
[2m2025-08-04 16:00:56.518[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 5
[2m2025-08-04 16:00:56.583[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====>  Preparing: select * from SYT_DICT_FIELD where sfxs = '是' and gid=? order by sort
[2m2025-08-04 16:00:56.584[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m ====> Parameters: 1774719849163943938(String)
[2m2025-08-04 16:00:57.124[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectByGroupIdOfField        [0;39m [2m:[0;39m <====      Total: 65
[2m2025-08-04 16:00:57.126[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.selectDictGroupList           [0;39m [2m:[0;39m <==      Total: 11
[2m2025-08-04 16:00:57.407[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.R.getButtonsListByRole          [0;39m [2m:[0;39m <==      Total: 219
[2m2025-08-04 16:01:19.892[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:01:19.893[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:01:19.965[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:01:19.969[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:19.969[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:01:20.044[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:20.048[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:20.049[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:01:20.122[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:20.124[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:20.124[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:01:20.266[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:20.270[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:20.271[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:01:20.344[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:20.347[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:20.348[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:01:20.418[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:49.837[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:01:49.839[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:01:49.925[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:01:49.947[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:49.949[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:01:50.015[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:50.017[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:50.018[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:01:50.086[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:50.089[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:50.092[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:01:50.171[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:50.173[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:50.174[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:01:50.489[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:01:50.492[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:01:50.493[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:01:50.562[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:20.175[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:02:20.177[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:02:20.252[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:02:20.256[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:20.256[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:02:20.329[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:20.332[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:20.333[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:02:20.404[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:20.407[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:20.408[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:02:20.485[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:20.488[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:20.489[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:02:20.572[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:20.575[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:20.575[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:02:20.655[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:49.934[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:02:49.935[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:02:50.012[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:02:50.020[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:50.022[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:02:50.091[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:50.094[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:50.095[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:02:50.165[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:50.168[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:50.169[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:02:50.242[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:50.244[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:50.245[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:02:50.324[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:02:50.327[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:02:50.327[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:02:50.391[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:20.118[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:03:20.126[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:03:20.200[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:03:20.203[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:20.203[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:03:20.270[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:20.275[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:20.277[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:03:20.350[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:20.352[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:20.353[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:03:20.416[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:20.419[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:20.420[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:03:20.491[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:20.494[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:20.495[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:03:20.569[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-2][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:49.836[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:03:49.838[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:03:49.904[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:03:49.907[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:49.907[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:03:49.981[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:49.983[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:49.984[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:03:50.048[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:50.050[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:50.050[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:03:50.179[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:50.183[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:50.184[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:03:50.276[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:03:50.280[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:03:50.281[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:03:50.347[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-3][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:19.888[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:04:19.889[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:04:19.959[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:04:19.961[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:19.962[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:04:20.029[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:20.032[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:20.032[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:04:20.103[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:20.106[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:20.106[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:04:20.173[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:20.175[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:20.175[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:04:20.241[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:20.243[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:20.244[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:04:20.321[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-5][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:49.923[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:04:49.925[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:04:50.231[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:04:50.234[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:50.235[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:04:50.527[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:50.533[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:50.534[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:04:51.765[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:51.773[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:51.774[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:04:51.850[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:51.858[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:51.859[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:04:51.933[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:04:51.939[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:04:51.940[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:04:52.014[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-1][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:00.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==>  Preparing: SELECT id, task_name, task_type, task_status, task_desc, task_params, file_path, file_name, file_size, file_id, create_user_id, create_user_name, create_time, start_time, finish_time, expire_time, progress, error_msg, retry_count, max_retry_count, remark, update_time FROM syt_download_task WHERE task_status = '0' ORDER BY create_time ASC FETCH FIRST 50 ROWS ONLY
[2m2025-08-04 16:05:00.384[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m ==> Parameters: 
[2m2025-08-04 16:05:00.547[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[   scheduling-1][0;39m [36mc.s.m.s.D.selectPendingTasks            [0;39m [2m:[0;39m <==      Total: 0
[2m2025-08-04 16:05:19.801[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:05:19.802[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:05:19.867[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:05:19.870[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:19.871[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:05:19.939[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:19.943[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:19.944[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:05:20.007[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:20.010[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:20.011[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:05:20.086[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:20.088[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:20.089[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:05:20.161[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:20.164[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:20.164[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:05:20.243[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-9][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:50.016[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:05:50.018[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:05:50.087[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:05:50.090[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:50.091[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:05:50.164[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:50.166[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:50.167[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:05:50.241[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:50.244[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:50.245[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:05:50.306[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:50.309[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:50.309[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:05:50.377[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:05:50.382[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:05:50.383[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:05:50.457[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-7][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:20.139[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:06:20.141[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:06:20.244[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:06:20.251[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:20.251[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:06:20.322[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:20.324[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:20.324[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:06:20.408[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:20.410[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:20.411[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:06:20.496[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:20.498[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:20.499[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:06:20.573[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:20.576[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:20.577[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:06:20.649[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-6][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:49.970[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:06:49.971[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:06:50.058[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:06:50.061[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:50.061[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:06:50.131[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:50.134[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:50.134[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:06:50.205[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:50.209[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:50.210[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:06:50.288[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:50.290[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:50.290[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:06:50.627[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:06:50.630[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:06:50.630[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:06:50.721[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-8][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:19.856[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:07:19.857[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:07:19.927[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:07:19.931[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:19.931[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:07:20.001[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:20.005[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:20.005[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:07:20.070[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:20.073[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:20.073[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:07:20.386[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:20.389[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:20.389[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:07:20.461[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:20.463[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:20.464[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:07:20.526[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[io-9094-exec-10][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:49.864[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==>  Preparing: SELECT o.ID, o.CODE, o.ORGNAME, o.VALID, o.DISPLAYORDER, o.ORGDESCRIPTION, o.CATEGORY_ID, o.PARENT, o.PARENTS, o.ORGCODE FROM SYT_SYS_ORGANIZATION_USER u, SYT_SYS_ORGANIZATION o WHERE u.organization_id = o.id AND u.user_id = ?
[2m2025-08-04 16:07:49.865[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m ==> Parameters: af574206017548d9a32bf4f4f4f49bf6(String)
[2m2025-08-04 16:07:49.937[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.S.getOrganizationByUser         [0;39m [2m:[0;39m <==      Total: 2
[2m2025-08-04 16:07:49.941[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:49.942[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 0(String)
[2m2025-08-04 16:07:50.001[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:50.004[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:50.004[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 1(String)
[2m2025-08-04 16:07:50.069[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:50.071[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:50.072[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 2(String)
[2m2025-08-04 16:07:50.141[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:50.143[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:50.144[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 3(String)
[2m2025-08-04 16:07:50.483[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
[2m2025-08-04 16:07:50.488[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==>  Preparing: SELECT COUNT(1) FROM syt_download_task WHERE create_user_id = ? AND task_status = ?
[2m2025-08-04 16:07:50.488[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m ==> Parameters: sytadmin(String), 4(String)
[2m2025-08-04 16:07:50.572[0;39m [32mDEBUG[0;39m [35m84074[0;39m [2m---[0;39m [2m[nio-9094-exec-4][0;39m [36mc.s.m.s.D.countTasksByUserIdAndStatus   [0;39m [2m:[0;39m <==      Total: 1
