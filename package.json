{"name": "avue-cli", "version": "2.0.0", "private": true, "scripts": {"serve": "NODE_OPTIONS=\"--openssl-legacy-provider\" vue-cli-service serve", "build": "NODE_OPTIONS=\"--openssl-legacy-provider\" vue-cli-service build", "lint": "vue-cli-service lint", "analyz": "npm_config_report=true npm run build", "test:unit": "vue-cli-service test:unit", "test:e2e": "vue-cli-service test:e2e"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "@smallwei/avue": "^2.8.17", "@sv-print/vue": "^0.1.18", "axios": "^0.18.0", "classlist-polyfill": "^1.2.0", "codemirror": "^5.58.1", "echarts": "^4.9.0", "echarts-gl": "^1.1.1", "element-ui": "^2.9.2", "file-saver": "^2.0.5", "js-base64": "^3.7.5", "js-cookie": "^2.2.0", "jsrsasign": "^10.5.27", "miment": "0.0.9", "mockjs": "^1.0.1-beta3", "monaco-editor": "^0.28.1", "nprogress": "^0.2.0", "qs": "^6.10.3", "reconnecting-websocket": "^4.4.0", "sass-loader": "^8.0.2", "script-loader": "^0.7.2", "sortablejs": "^1.10.0-rc2", "v-chart": "^1.0.0", "vant": "^2.10.4", "vue": "^2.6.12", "vue-axios": "^2.1.2", "vue-codemirror": "^4.0.6", "vue-color": "^2.8.1", "vue-cron-generator": "^0.2.4", "vue-drag-resize": "^1.5.4", "vue-echarts": "^5.0.0-beta.0", "vue-grid-layout": "^2.3.12", "vue-i18n": "^8.7.0", "vue-json-editor": "^1.4.3", "vue-json-excel": "^0.3.0", "vue-router": "3.0.1", "vue-ruler-tool": "^1.2.4", "vue-superslide": "^0.1.1", "vuedraggable": "^2.24.1", "vuex": "^3.0.1", "wangeditor": "^3.1.1"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.1.1", "@vue/cli-plugin-eslint": "^3.1.5", "@vue/cli-service": "^3.1.4", "babel-eslint": "^10.0.3", "babel-polyfill": "^6.26.0", "chai": "^4.1.2", "compression-webpack-plugin": "^6.1.1", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.0.0", "less": "^2.7.3", "less-loader": "^4.1.0", "monaco-editor-webpack-plugin": "^4.1.1", "sass": "^1.49.9", "vue-template-compiler": "^2.6.12", "webpack-bundle-analyzer": "^3.0.3"}, "lint-staged": {"*.js": ["vue-cli-service lint", "git add"], "*.vue": ["vue-cli-service lint", "git add"]}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "rules": {}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "browserslist": ["> 1%", "last 2 versions"]}